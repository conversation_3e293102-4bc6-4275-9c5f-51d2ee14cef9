# Bedrock Chatbot Function

This AWS Lambda function provides a serverless backend for the Bedrock Chatbot service, enabling integration with Amazon Bedrock's AI models for natural language processing.

## Features

- **Chat Processing**: Process chat messages using Amazon Bedrock's AI models
- **Session Management**: Track conversations with session-based context
- **Data Management**: Store and retrieve chatbot data for users
- **Modular Architecture**: Follows a clean, modular structure for maintainability
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Input Validation**: Robust validation for all API inputs
- **Error Handling**: Consistent error responses and error handling

## Project Structure

```
src/
├── index.js                 # Main Lambda handler
├── resolver.js              # GraphQL resolver router
├── modules/                 # Feature modules
│   └── chatbot/             # Chatbot module
│       ├── __tests__/       # Unit tests
│       │   └── chatbot.handlers.test.js
│       ├── chatbot.handlers.js  # Request handlers
│       ├── chatbot.resolvers.js # GraphQL resolvers
│       ├── chatbot.service.js   # Business logic
│       └── chatbot.validation.js # Validation logic
└── shared/
    └── utils/              # Shared utilities
        ├── handlerUtils.js      # Handler utilities
        ├── logger.js            # Logging utilities
        ├── responseUtils.js     # Response formatting
        └── standardizedErrorUtils.js # Error handling
```

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `ENV` | Environment (dev, staging, prod) | Yes | - |
| `REGION` | AWS region | Yes | - |
| `DEBUG` | Enable debug logging | No | `false` |
| `LOG_RESPONSE` | Log full API responses | No | `false` |
| `AWS_ACCESS_KEY_ID` | AWS access key | Yes* | - |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | Yes* | - |

*Required if not running in AWS Lambda with appropriate IAM roles

## API Endpoints

### bedrockChatBot

Process a chat message using Amazon Bedrock.

**Input:**
```graphql
input BedrockChatInput {
  message: String!       # The message to process
  sessionId: String     # Optional session ID for conversation context
  userId: String!       # ID of the user sending the message
}
```

**Response:**
```typescript
{
  response: string;     // The generated response
  sessionId: string;     // Session ID for the conversation
  timestamp: string;     // ISO timestamp of the response
  usage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  }
}
```

### fetchChatbotData

Fetch stored chatbot data for a user.

**Input:**
```graphql
input FetchChatbotDataInput {
  userId: String!       # ID of the user
  chatType: String!     # Type of chat data to fetch
}
```

### removeChatbotData

Remove stored chatbot data for a user.

**Input:**
```graphql
input RemoveChatbotDataInput {
  userId: String!       # ID of the user
  chatType: String!     # Type of chat data to remove
}
```

## Error Handling

The API returns standardized error responses with the following structure:

```typescript
{
  statusCode: number;   // HTTP status code
  error: {
    code: string;       // Error code
    message: string;     // Human-readable message
    details?: any;       // Additional error details
  }
}
```

## Local Development

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables in `.env` file:
   ```
   ENV=dev
   REGION=us-east-1
   DEBUG=true
   ```

3. Run tests:
   ```bash
   npm test
   ```

## Deployment

This function is designed to be deployed using AWS Amplify. The deployment is handled automatically through the Amplify CI/CD pipeline.

## Monitoring and Logging

All API requests and errors are logged to CloudWatch Logs. You can monitor the function's performance and errors using AWS CloudWatch.

## Security

- All endpoints require authentication
- Input validation is performed on all requests
- Sensitive data is not logged
- Follows the principle of least privilege for IAM permissions
