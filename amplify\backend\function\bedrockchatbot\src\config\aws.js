const AWS = require("aws-sdk");

console.log('process.env.REGION: ', process.env.REGION);
console.log('process.env.ACCESS_KEY_ID: ', process.env.ACCESS_KEY_ID);
console.log('process.env.SECRET_ACCESS_KEY: ', process.env.SECRET_ACCESS_KEY);

// Configure AWS SDK
AWS.config.update({
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});

// Initialize AWS services
const ddb = new AWS.DynamoDB();
const dynamoClient = new AWS.DynamoDB.DocumentClient();

module.exports = {
  AWS,
  ddb,
  dynamoClient
};
