const { createLogger } = require('../../shared/utils/logger');
const { getCommunityEventsPrompt } = require('../static/communityHandler');
const { formatCommunityData } = require('../../shared/utils/formatters/communityDataFormatter');

const logger = createLogger('communityHandler');

/**
 * Handles community-related chat functionality
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @returns {Promise<string>} - The chatbot's response
 */
async function communityChatbot(question, promptData, userId, apolloClient) {
  const startTime = Date.now();
  const requestId = `community-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    logger.info(`=== [communityChatbot] START - ${requestId} ===`, { 
      question: question?.substring(0, 200),
      hasPromptData: !!promptData,
      userId,
      hasApolloClient: !!apolloClient
    });
    
    // Format the prompt data into a human-readable summary
    const formattedData = formatCommunityData(promptData);
    
    // Get the prompt with the formatted data
    const result = await getCommunityEventsPrompt(question, formattedData, userId, apolloClient);
    const endTime = Date.now();
    
    logger.info(`[communityChatbot] Request completed successfully in ${endTime - startTime}ms`, {
      resultLength: result?.length || 0,
      requestId
    });
    logger.info(`=== [communityChatbot] END - ${requestId} (${endTime - startTime}ms) ===`);
    return result;
  } catch (error) {
    const errorTime = Date.now();
    logger.error(`[communityChatbot] Request failed after ${errorTime - startTime}ms:`, {
      error: error.message,
      stack: error.stack,
      userId,
      requestId,
      hasApolloClient: !!apolloClient
    });
    logger.info(`=== [communityChatbot] ERROR - ${requestId} (${errorTime - startTime}ms) ===`);
    throw error; // Re-throw to be handled by the caller
  }
}

module.exports = {
  communityChatbot
};
