const { createLogger } = require('../../shared/utils/logger');
const { getKnowledgePrompt } = require('../static/knowledgeHandler');

const logger = createLogger('knowledgeHandler');

/**
 * Handles knowledge-related chat functionality
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @returns {Promise<string>} - The chatbot's response
 */
async function knowledgeChatbot(question, promptData, userId, apolloClient) {
  const startTime = Date.now();
  const requestId = `knowledge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    logger.info(`=== [knowledgeChatbot] START - ${requestId} ===`, { 
      question: question?.substring(0, 200),
      hasPromptData: !!promptData,
      userId,
      hasApolloClient: !!apolloClient
    });
    
    const result = await getKnowledgePrompt(question, promptData, userId, apolloClient);
    const endTime = Date.now();
    
    logger.info(`[knowledgeChatbot] Request completed successfully in ${endTime - startTime}ms`, {
      resultLength: result?.length || 0,
      requestId
    });
    logger.info(`=== [knowledgeChatbot] END - ${requestId} (${endTime - startTime}ms) ===`);
    return result;
  } catch (error) {
    const errorTime = Date.now();
    logger.error(`[knowledgeChatbot] Request failed after ${errorTime - startTime}ms:`, {
      error: error.message,
      stack: error.stack,
      userId,
      requestId,
      hasApolloClient: !!apolloClient
    });
    throw error; // Re-throw to be handled by the caller
  }
}

module.exports = {
  knowledgeChatbot
};
