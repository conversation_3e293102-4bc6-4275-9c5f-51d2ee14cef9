const { getChatHistoryPrompt } = require('../../shared/utils/chatHistory');
const { createLogger } = require('../../shared/utils/logger');
const { formatPromptData } = require('./formatPromptData');

const logger = createLogger('familyHandler');

/**
 * Get the family chatbot prompt
 * @param {string} question - The user's question
 * @param {Object} promptData - The data to use for generating the prompt
 * @param {string} userId - The ID of the user
 * @param {Object} apolloClient - The Apollo Client instance
 * @returns {Promise<string>} - The generated prompt
 */
async function getFamilyPrompt(question, promptData, userId, apolloClient) {
  // Log the raw prompt data for debugging
  console.log('=== RAW FAMILY PROMPT DATA ===');
  console.log('Type of promptData:', typeof promptData);
  console.log(
    'Keys in promptData:',
    promptData ? Object.keys(promptData) : 'promptData is null/undefined'
  );
  console.log('Full promptData:', JSON.stringify(promptData, null, 2));
  console.log('=== END RAW FAMILY PROMPT DATA ===');

  // Extract chat history and family data
  const { chatHistory: chatHistoryData, ...familyData } = promptData || {};
  
  // Log the extracted data
  console.log('=== EXTRACTED FAMILY DATA ===');
  console.log(
    'chatHistoryData length:',
    chatHistoryData ? chatHistoryData.length : 'No chat history'
  );
  console.log('familyData keys:', Object.keys(familyData));
  console.log('familyData content:', JSON.stringify(familyData, null, 2));
  console.log('=== END EXTRACTED FAMILY DATA ===');

  // Format the family data using the shared formatter
  const formattedData = formatPromptData('family', familyData, userId);

  // Get chat history if apolloClient is available
  const chatHistory = apolloClient ? await getChatHistoryPrompt(apolloClient, userId) : '';
  
  logger.debug('Retrieved chat history for family chat', { 
    hasChatHistory: !!chatHistory,
    chatHistoryLength: chatHistory?.length || 0 
  });

  return `${chatHistory ? `### Previous Conversation Context:
${chatHistory}

` : ''}
  Hello,  
  You are a helpful assistant for MyVillage family matters. Follow these rules:
  
   Below is the family-related data for the MyVillage app for the user with id ${userId}:
  
  ### Explanation of the Family Data:
  
  **1. Family Members**  
  - List of family members with their relationships and basic information.
  - Each member includes name, relationship, and relevant details.
  
  **2. Family Events**  
  - Upcoming family gatherings, birthdays, and special occasions.
  - Includes event name, date, time, and location.
  
  **3. Family Groups**  
  - Different family groups the user belongs to.
  - Group names and member counts.
  
  **4. Family Tasks**  
  - Assigned chores and responsibilities.
  - Task status and due dates.
  
  ### Important Rules:
  1. **DO NOT fabricate answers.** Use only the provided data to respond.
  2. For greetings, respond with: "Hello! How can I help with your family matters today?"
  3. Keep responses family-focused and relevant to family-related queries.
  4. Maintain privacy and do not share sensitive family information.
  5. If asked about family members, provide relevant details while respecting privacy.
  6. For event-related questions, filter based on the specified time period.
  
  **User Question:**  
  **${question}**  

  Here is the family data to use for generating the answer:

  ${formattedData}`;
}

module.exports = {
  getFamilyPrompt
};
