const { formatUnifyData } = require('../../shared/utils/formatters/unifyDataFormatter');
const { formatFamilyData } = require('../../shared/utils/formatters/familyDataFormatter');
const { formatCommunityData } = require('../../shared/utils/formatters/communityDataFormatter');
const { formatKnowledgeData } = require('../../shared/utils/formatters/knowledgeDataFormatter');
const { createLogger } = require('../../shared/utils/logger');

const logger = createLogger('formatPromptData');

/**
 * Formats prompt data for a given chat type using the appropriate formatter.
 * @param {string} chatType - The chat type (e.g., 'unify', 'family', 'community', 'knowledge')
 * @param {Object} data - The data to format
 * @param {string} userId - The user ID
 * @returns {string} Formatted data string
 */
function formatPromptData(chatType, data, userId) {
  let formattedData = `No ${chatType} data available`;
  try {
    const baseData = {
      ...data,
      _metadata: {
        chatType,
        userId,
        timestamp: new Date().toISOString(),
      },
    };
    switch (chatType) {
      case 'unify':
        formattedData = formatUnifyData(baseData);
        break;
      case 'family':
        formattedData = formatFamilyData(baseData);
        break;
      case 'community':
        formattedData = formatCommunityData(baseData);
        break;
      case 'knowledge':
        formattedData = formatKnowledgeData(baseData);
        break;
      default:
        logger.warn('Unknown chatType for formatting', { chatType });
        formattedData = JSON.stringify(baseData, null, 2);
    }
  } catch (error) {
    logger.error('Error formatting prompt data', {
      error: error.message,
      stack: error.stack,
      chatType,
    });
    formattedData = JSON.stringify(data || {}, null, 2);
  }
  return formattedData;
}

module.exports = { formatPromptData };
