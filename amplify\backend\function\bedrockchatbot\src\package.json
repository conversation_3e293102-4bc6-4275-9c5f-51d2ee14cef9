{"name": "bedrockchatbot", "version": "1.0.0", "description": "AWS Lambda function for Bedrock Chatbot integration", "main": "src/index.js", "scripts": {"test": "jest --verbose --coverage", "test:watch": "jest --watch", "lint": "eslint . --ext .js", "lint:fix": "eslint . --ext .js --fix", "deploy": "echo 'Deployment handled by Amplify'"}, "dependencies": {"@apollo/client": "^3.8.8", "@aws-sdk/client-bedrock-runtime": "^3.0.0", "apollo-link-http": "^1.5.17", "aws-sdk": "^2.0.0", "graphql": "^15.8.0", "joi": "^17.0.0", "node-fetch": "^2.6.1", "pino": "^8.17.2", "uuid": "^9.0.0", "pino-pretty": "^13.0.0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "babel-jest": "^29.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jest": "^27.0.0", "jest": "^29.0.0", "prettier": "^3.0.0"}, "jest": {"testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/", "/__tests__/"], "collectCoverageFrom": ["src/**/*.js", "!src/index.js", "!**/node_modules/**"]}, "eslintConfig": {"env": {"node": true, "es2021": true, "jest/globals": true}, "extends": ["eslint:recommended", "plugin:jest/recommended", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}}, "prettier": {"semi": true, "singleQuote": true, "printWidth": 100, "tabWidth": 2, "trailingComma": "es5"}}