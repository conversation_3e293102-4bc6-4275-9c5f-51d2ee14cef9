const {
  BedrockRuntimeClient,
  InvokeModelCommand,
} = require("@aws-sdk/client-bedrock-runtime");
const { createLogger, logError } = require('../shared/utils/logger');

const client = new BedrockRuntimeClient({ region: "us-east-1" });

/**
 * Converts a JSON response from Bedrock into a human-readable form based on a given question.
 * @param {string} question - The question asked by the user.
 * @param {string} responseOutput - The JSON response from Bedrock.
 * @returns {string|object} The formatted output, or an object with the error message if an error occurred.
 * @throws {Error} If an error occurred while invoking Nova Lite model.
 */

async function respondWithBedrockChatBot(prompt) {
  const logger = createLogger({}, { functionName: 'respondWithBedrockChatBot' });
  try {
    // Log the full prompt data before sending to Bedrock
    logger.info('=== FULL PROMPT DATA ===');
    // logger.info(prompt);
    logger.info('=== END PROMPT DATA ===');
    const params = {
      modelId: "amazon.nova-lite-v1:0",
      body: JSON.stringify({
        messages: [
          {
            role: "user",
            content: [
              {
                toolUse: {
                  name: "chat",
                  toolUseId: "bedrock_chatbot",
                  input: {
                    prompt: prompt
                  }
                }
              }
            ],
          },
        ],
      }),
    };
    const command = new InvokeModelCommand(params);
    const response = await client.send(command);
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));
    const output = responseBody?.output?.message?.content[0]?.text;
    if (output) {
      return { success: true, message: 'Bedrock response received', data: output };
    } else {
      return { success: false, message: 'No response from Bedrock model', data: null };
    }
  } catch (error) {
    logError(logger, error, 'respondWithBedrockChatBot');
    if (error.name === 'ExpiredTokenException' || (error?.message?.includes('expired'))) {
      return {
        success: false,
        message: 'The security token included in the request is expired. Please refresh your credentials and try again.',
        error: error.message,
        code: 'ExpiredTokenException'
      };
    }
    return {
      success: false,
      message: 'An error occurred while invoking the Bedrock model.',
      error: error.message,
      code: error.name || 'UnknownError'
    };
  }
}

module.exports = {
  respondWithBedrockChatBot,
};
