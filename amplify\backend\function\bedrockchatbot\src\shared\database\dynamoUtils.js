/**
 * DynamoDB utility functions for the Bedrock Chatbot service
 */

const AWS = require('aws-sdk');
const { createLogger } = require('../utils/logger');

// Initialize DynamoDB DocumentClient for easier data handling
const docClient = new AWS.DynamoDB.DocumentClient();

// Configuration constants
const CONFIG = {
  MAX_RETRIES: 3,
  BASE_DELAY_MS: 100,
  MAX_ITEMS_PER_BATCH: 25 // DynamoDB batch write limit
};

/**
 * Get table name with environment prefix
 * @param {string} tableName - Base table name
 * @returns {string} Full table name with environment prefix
 */
function getTableName(tableName) {
  const fullTableName = `${tableName}-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
  
  if (process.env.AWS_LAMBDA_LOG_LEVEL === 'DEBUG') {
    const logger = createLogger({}, 'dynamoUtils:getTableName');
    logger.debug({
      baseTableName: tableName,
      fullTableName,
      apiId: process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT,
      environment: process.env.ENV
    });
  }
  
  return fullTableName;
}

/**
 * Execute a DynamoDB operation with retry logic
 * @param {string} operation - Name of the operation for logging
 * @param {Function} operationFn - Function that performs the DynamoDB operation
 * @param {Object} logger - Logger instance
 * @param {Object} context - Context for logging
 * @returns {Promise<any>} Result of the operation
 */
async function executeWithRetry(operation, operationFn, logger, context = {}) {
  let lastError;
  
  for (let attempt = 0; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      const result = await operationFn();
      logger.info(`${operation} succeeded`, { ...context, attempt });
      return result;
    } catch (error) {
      lastError = error;
      
      // Don't retry on validation or resource not found errors
      if (error.code === 'ValidationException' || error.code === 'ResourceNotFoundException') {
        throw error;
      }
      
      // Only retry on throttling or conditional check failures
      if (error.code === 'ConditionalCheckFailedException' || 
          error.code === 'ProvisionedThroughputExceededException' ||
          error.code === 'ThrottlingException') {
        
        if (attempt === CONFIG.MAX_RETRIES) break;
        
        const delay = CONFIG.BASE_DELAY_MS * Math.pow(2, attempt) + Math.random() * 100;
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      // For other errors, rethrow immediately
      throw error;
    }
  }
  
  throw lastError || new Error(`Operation ${operation} failed after ${CONFIG.MAX_RETRIES} attempts`);
}

/**
 * Get an item from DynamoDB
 * @param {string} tableName - Base table name
 * @param {Object} key - Key of the item to get
 * @param {Object} options - Additional options
 * @returns {Promise<Object|null>} The retrieved item or null if not found
 */
async function getItem(tableName, key, options = {}) {
  const { consistentRead = false } = options;
  const logger = createLogger({}, 'dynamoUtils:getItem');
  const fullTableName = getTableName(tableName);
  
  const params = {
    TableName: fullTableName,
    Key: key,
    ConsistentRead: consistentRead
  };
  
  try {
    const result = await executeWithRetry(
      'getItem',
      () => docClient.get(params).promise(),
      logger,
      { tableName: fullTableName, key }
    );
    
    return result.Item || null;
  } catch (error) {
    logger.error('Error getting item from DynamoDB', { error, tableName: fullTableName, key });
    throw error;
  }
}

/**
 * Query items from DynamoDB
 * @param {string} tableName - Base table name
 * @param {Object} params - Query parameters
 * @returns {Promise<Array>} Array of matching items
 */
async function queryItems(tableName, params = {}) {
  const logger = createLogger({}, 'dynamoUtils:queryItems');
  const fullTableName = getTableName(tableName);
  
  const queryParams = {
    TableName: fullTableName,
    ...params
  };
  
  try {
    const result = await executeWithRetry(
      'query',
      () => docClient.query(queryParams).promise(),
      logger,
      { tableName: fullTableName, ...params }
    );
    
    return result.Items || [];
  } catch (error) {
    logger.error('Error querying items from DynamoDB', { error, tableName: fullTableName, params });
    throw error;
  }
}

/**
 * Scan items from DynamoDB
 * @param {string} tableName - Base table name
 * @param {Object} params - Scan parameters
 * @returns {Promise<Array>} Array of scanned items
 */
/**
 * Scan items from DynamoDB
 * @param {string} tableName - Base table name
 * @param {Object} params - Scan parameters
 * @returns {Promise<Array>} Array of scanned items
 */
async function scanItems(tableName, params = {}) {
  const logger = createLogger({}, 'dynamoUtils:scanItems');
  const fullTableName = getTableName(tableName);
  
  // Log the scan operation details
  logger.debug('Initiating DynamoDB scan', {
    tableName: fullTableName,
    filter: params.FilterExpression,
    limit: params.Limit
  });
  
  const scanParams = {
    TableName: fullTableName,
    ...params
  };
  
  try {
    const result = await executeWithRetry(
      'scan',
      () => docClient.scan(scanParams).promise(),
      logger,
      { 
        tableName: fullTableName,
        filter: params.FilterExpression,
        limit: params.Limit
      }
    );
    
    logger.debug('Successfully scanned items', {
      tableName: fullTableName,
      itemCount: result?.Items?.length || 0,
      scannedCount: result?.ScannedCount || 0
    });
    
    return result.Items || [];
  } catch (error) {
    // Create a safe error object to avoid circular references
    const safeError = {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      retryable: error.retryable,
      time: new Date().toISOString()
    };
    
    // Log the error safely
    logger.error({
      message: 'Error scanning items from DynamoDB',
      error: safeError, 
      tableName: fullTableName,
      operation: 'scan',
      params: {
        FilterExpression: params.FilterExpression,
        ExpressionAttributeValues: params.ExpressionAttributeValues ? '***' : undefined,
        Limit: params.Limit
      }
    });
    
    // Re-throw with additional context
    const enhancedError = new Error(`Failed to scan items from ${fullTableName}: ${error.message}`);
    enhancedError.originalError = safeError;
    enhancedError.tableName = fullTableName;
    enhancedError.operation = 'scan';
    throw enhancedError;
  }
}

/**
 * Put an item in DynamoDB
 * @param {string} tableName - Base table name
 * @param {Object} item - Item to put
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Result of the put operation
 */
async function putItem(tableName, item, options = {}) {
  const { conditionExpression, expressionAttributeValues, expressionAttributeNames } = options;
  const logger = createLogger({}, 'dynamoUtils:putItem');
  const fullTableName = getTableName(tableName);
  
  const params = {
    TableName: fullTableName,
    Item: item,
    ...(conditionExpression && { ConditionExpression: conditionExpression }),
    ...(expressionAttributeValues && { ExpressionAttributeValues: expressionAttributeValues }),
    ...(expressionAttributeNames && { ExpressionAttributeNames: expressionAttributeNames })
  };
  
  try {
    return await executeWithRetry(
      'putItem',
      () => docClient.put(params).promise(),
      logger,
      { tableName: fullTableName, itemId: item.id }
    );
  } catch (error) {
    logger.error('Error putting item to DynamoDB', { error, tableName: fullTableName, itemId: item.id });
    throw error;
  }
}

/**
 * Delete an item from DynamoDB
 * @param {string} tableName - Base table name
 * @param {Object} key - Key of the item to delete
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Result of the delete operation
 */
async function deleteItem(tableName, key, options = {}) {
  const { conditionExpression, expressionAttributeValues, expressionAttributeNames } = options;
  const logger = createLogger({}, 'dynamoUtils:deleteItem');
  const fullTableName = getTableName(tableName);
  
  const params = {
    TableName: fullTableName,
    Key: key,
    ...(conditionExpression && { ConditionExpression: conditionExpression }),
    ...(expressionAttributeValues && { ExpressionAttributeValues: expressionAttributeValues }),
    ...(expressionAttributeNames && { ExpressionAttributeNames: expressionAttributeNames })
  };
  
  try {
    return await executeWithRetry(
      'deleteItem',
      () => docClient.delete(params).promise(),
      logger,
      { tableName: fullTableName, key }
    );
  } catch (error) {
    logger.error('Error deleting item from DynamoDB', { error, tableName: fullTableName, key });
    throw error;
  }
}

/**
 * Batch write items to DynamoDB
 * @param {string} tableName - Base table name
 * @param {Array} items - Array of items to write
 * @param {string} operation - 'put' or 'delete'
 * @returns {Promise<Array>} Results of the batch write operations
 */
async function batchWriteItems(tableName, items, operation = 'put') {
  const logger = createLogger({}, 'dynamoUtils:batchWriteItems');
  const fullTableName = getTableName(tableName);
  
  // Process items in batches of 25 (DynamoDB limit)
  const batches = [];
  for (let i = 0; i < items.length; i += CONFIG.MAX_ITEMS_PER_BATCH) {
    batches.push(items.slice(i, i + CONFIG.MAX_ITEMS_PER_BATCH));
  }
  
  const results = [];
  
  for (const batch of batches) {
    const requestItems = {
      [fullTableName]: batch.map(item => ({
        [operation === 'put' ? 'PutRequest' : 'DeleteRequest']: 
          operation === 'put' 
            ? { Item: item } 
            : { Key: item }
      }))
    };
    
    try {
      const result = await executeWithRetry(
        'batchWrite',
        () => docClient.batchWrite({ RequestItems: requestItems }).promise(),
        logger,
        { 
          tableName: fullTableName, 
          operation,
          batchSize: batch.length 
        }
      );
      
      results.push(result);
    } catch (error) {
      logger.error('Error in batch write to DynamoDB', { 
        error, 
        tableName: fullTableName, 
        operation,
        batchSize: batch.length 
      });
      throw error;
    }
  }
  
  return results;
}

/**
 * Update an item in DynamoDB
 * @param {string} tableName - Base table name
 * @param {Object} key - Key of the item to update
 * @param {Object} updates - Object with fields to update
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} The updated item
 */
async function updateItem(tableName, key, updates, options = {}) {
  const { 
    conditionExpression,
    expressionAttributeValues = {},
    expressionAttributeNames = {},
    returnValues = 'ALL_NEW'
  } = options;
  
  const logger = createLogger({}, 'dynamoUtils:updateItem');
  const fullTableName = getTableName(tableName);
  
  // Build update expression
  const updateExpressions = [];
  const expressionAttributeValuesCopy = { ...expressionAttributeValues };
  const expressionAttributeNamesCopy = { ...expressionAttributeNames };
  
  Object.entries(updates).forEach(([field, value]) => {
    const valueKey = `:${field}`;
    const fieldKey = `#${field}`;
    
    updateExpressions.push(`${fieldKey} = ${valueKey}`);
    expressionAttributeValuesCopy[valueKey] = value;
    expressionAttributeNamesCopy[fieldKey] = field;
  });
  
  const params = {
    TableName: fullTableName,
    Key: key,
    UpdateExpression: `SET ${updateExpressions.join(', ')}`,
    ExpressionAttributeValues: expressionAttributeValuesCopy,
    ExpressionAttributeNames: expressionAttributeNamesCopy,
    ReturnValues: returnValues,
    ...(conditionExpression && { ConditionExpression: conditionExpression })
  };
  
  try {
    const result = await executeWithRetry(
      'updateItem',
      () => docClient.update(params).promise(),
      logger,
      { tableName: fullTableName, key }
    );
    
    return returnValues === 'NONE' ? null : result.Attributes;
  } catch (error) {
    logger.error('Error updating item in DynamoDB', { 
      error, 
      tableName: fullTableName, 
      key,
      updates: Object.keys(updates) 
    });
    throw error;
  }
}

module.exports = {
  getItem,
  putItem,
  updateItem,
  deleteItem,
  queryItems,
  scanItems,
  batchWriteItems,
  getTableName
};
