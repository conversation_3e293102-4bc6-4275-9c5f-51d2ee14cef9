const AWS = require('aws-sdk');
const { createLogger } = require('./logger');

const logger = createLogger('chatHistory');

const docClient = new AWS.DynamoDB.DocumentClient();

/**
 * Fetches the last 5 chat messages for a user
 * @param {Object} ddb - The DynamoDB instance (kept for backward compatibility)
 * @param {string} userId - The ID of the user
 * @returns {Promise<Array>} - Array of chat messages or empty array on error
 */
async function getChatHistory(ddb, userId) {
  const startTime = Date.now();
  const operation = 'getChatHistory';
  
  if (!userId) {
    logger.error('User ID is required', { operation });
    return [];
  }
  
  try {
    logger.debug('Fetching chat history', { operation, userId });
    
    const params = {
      TableName: `ChatGPT-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      FilterExpression: 'userId = :userId AND isDeleted = :isDeleted',
      ExpressionAttributeValues: {
        ':userId': userId,
        ':isDeleted': 'false'
      },
      Limit: 5
    };

    logger.debug('Executing DynamoDB scan', { operation, userId });
    
    const data = await docClient.scan(params).promise();
    
    if (!data || !data.Items) {
      logger.warn('No data returned from DynamoDB query', { operation, userId });
      return [];
    }
    
    const items = data.Items
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 5);
    
    // logger.debug('Found chat history items', { 
    //   operation, 
    //   itemCount: items.length,
    //   items: items.map(item => ({
    //     id: item.id,
    //     type: item.type,
    //     timestamp: item.timestamp,
    //     contentLength: item.content?.length
    //   })) 
    // });

    // logger.debug('Successfully retrieved chat history', { 
    //   operation, 
    //   userId, 
    //   itemCount: items.length 
    // });
    
    return items;
  } catch (error) {
    const errorMessage = `Error in ${operation} for user ${userId}: ${error.message}`;
    logger.error(errorMessage, {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
        ...(error.code && { code: error.code })
      },
      operation,
      userId
    });
    
    return [];
  }
}

/**
 * Formats chat history into a readable string
 * @param {Array} chatHistory - Array of chat messages
 * @returns {string} - Formatted chat history string or empty string on error
 */
function formatChatHistory(chatHistory) {
  const operation = 'formatChatHistory';
  
  try {
    if (!chatHistory || !Array.isArray(chatHistory)) {
      logger.error('Invalid chat history format', { 
        operation, 
        expected: 'array', 
        actual: typeof chatHistory 
      });
      return '';
    }
    
    if (chatHistory.length === 0) {
      logger.debug('Empty chat history array provided', { operation });
      return '';
    }

    const validMessages = chatHistory.filter(msg => 
      msg && 
      typeof msg === 'object' && 
      msg.message && 
      typeof msg.message === 'string' &&
      msg.role
    );

    if (validMessages.length === 0) {
      logger.warn('No valid messages found in chat history', { operation });
      return '';
    }

    const sortedHistory = [...validMessages].sort((a, b) => {
      try {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
        return dateA - dateB;
      } catch (error) {
        logger.error('Error sorting messages by date', { 
          operation, 
          error: error.message 
        });
        return 0;
      }
    });

    const formattedMessages = sortedHistory.map((msg, index) => {
      try {
        const role = msg.role === 'user' ? 'User' : 'Assistant';
        const message = (msg.message || '').trim();
        return `- ${role}: ${message}`;
      } catch (error) {
        logger.error('Error formatting message', { 
          operation, 
          index, 
          error: error.message 
        });
        return null;
      }
    }).filter(Boolean);

    if (formattedMessages.length === 0) {
      logger.warn('No messages could be formatted', { operation });
      return '';
    }

    return formattedMessages.join('\n');
    
  } catch (error) {
    logger.error('Unexpected error formatting chat history', { 
      operation, 
      error: error.message,
      stack: error.stack
    });
    return '';
  }
}

/**
 * Gets the chat history prompt to be prepended to the main prompt
 * @param {Object} ddb - The DynamoDB instance
 * @param {string} userId - The ID of the user
 * @returns {Promise<string>} - Formatted chat history prompt or empty string on error
 */
async function getChatHistoryPrompt(ddb, userId) {
  const startTime = Date.now();
  const operation = 'getChatHistoryPrompt';
  
  // Input validation
  if (!userId) {
    logger.error('Missing required parameter: userId', { operation });
    return '';
  }

  try {
    logger.debug('Starting chat history prompt generation', { operation, userId });
    
    const chatHistory = await getChatHistory(ddb, userId);
    
    // logger.debug('Retrieved chat history', { 
    //   operation, 
    //   hasChatHistory: !!chatHistory,
    //   chatHistoryLength: chatHistory?.length || 0 
    // });
    
    if (!chatHistory || !Array.isArray(chatHistory) || chatHistory.length === 0) {
      logger.debug('No chat history found for user', { operation, userId });
      return '';
    }

    logger.debug('Formatting chat history', { 
      operation, 
      itemCount: chatHistory.length 
    });
    const formattedHistory = formatChatHistory(chatHistory);
    const result = `\n\nPrevious conversation:\n${formattedHistory}`;
    
    logger.debug('Successfully generated chat history prompt', { 
      operation, 
      userId,
      promptLength: result.length,
      preview: result.substring(0, 200) + (result.length > 200 ? '...' : '')
    });
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorDetails = {
      message: error.message,
      name: error.name,
      stack: error.stack,
      userId,
      operation,
      duration
    };
    
    logger.error('Error generating chat history prompt', { 
      operation, 
      durationMs: duration,
      error: errorDetails 
    });
    
    if (error.graphQLErrors) {
      logger.error('GraphQL errors', { 
        operation, 
        errors: error.graphQLErrors 
      });
    }
    if (error.networkError) {
      logger.error('Network error', { 
        operation, 
        message: error.networkError.message,
        statusCode: error.networkError.statusCode,
        body: error.networkError.body
      });
    }
    
    return '';
  }
}

module.exports = {
  getChatHistory,
  formatChatHistory,
  getChatHistoryPrompt,
};
