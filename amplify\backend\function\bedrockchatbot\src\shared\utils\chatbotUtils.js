/**
 * Utility functions for chatbot operations
 */
const { createLogger } = require('./logger');
const { 
  GET_USER_FOR_COMMUNITY, 
  GET_USER_FOR_FAMILY, 
  GET_USER_DETAILS_FOR_KNOWLEDGE, 
  GET_KNOWLEDGE_REPOSITORY_STORE,
  GET_USER_FOR_KNOWLEDGE,
  GET_USER_FOR_UNIFY 
} = require('../../graphql/queries');

const logger = createLogger('chatbotUtils');

/**
 * Fetches basic user data from the GraphQL API
 * @param {Object} apolloClient - The Apollo Client instance
 * @param {Object} query - The GraphQL query to execute
 * @param {string} userId - The ID of the user to fetch data for
 * @returns {Promise<string|null>} - JSON string of user data or null if not found
 */
async function fetchBasicUserData(apolloClient, query, userId) {
  try {
    if (!apolloClient) {
      logger.warn('No Apollo client available for fetching user data');
      return null;
    }

    // Add context to the query for better error tracking
    const context = {
      headers: {
        'x-user-id': userId,
        'x-request-source': 'bedrockchatbot'
      }
    };

    logger.debug('Fetching user data', { 
      query: query.definitions[0].name.value,
      userId
    });

    const response = await apolloClient.query({ 
      query, 
      variables: { id: userId },
      context,
      fetchPolicy: 'network-only' // Always fetch fresh data
    });

    if (!response || !response.data || !response.data.getUser) {
      logger.warn('No user data found in response', { userId });
      return null;
    }

    return JSON.stringify(response.data.getUser);
  } catch (error) {
    logger.error('Error in fetchBasicUserData', {
      error: error.message,
      userId,
      queryName: query?.definitions?.[0]?.name?.value || 'unknown',
      hasGraphQLErrors: !!error.graphQLErrors?.length,
      hasNetworkError: !!error.networkError
    });
    
    // Return null to allow fallback to static response
    return null;
  }
}

/**
 * Fetches knowledge-related data for a user in a structured format
 * @param {Object} apolloClient - The Apollo Client instance
 * @param {string} userId - The ID of the user to fetch data for
 * @returns {Promise<string>} - JSON string containing knowledge data in a structured format
 */
async function fetchKnowledgeData(apolloClient, userId) {
  try {
    const [communityRes, knowledgeRes] = await Promise.all([
      apolloClient.query({ query: GET_USER_FOR_COMMUNITY, variables: { id: userId } }),
      apolloClient.query({ query: GET_USER_DETAILS_FOR_KNOWLEDGE, variables: { id: userId } }),
    ]);

    const user = knowledgeRes?.data?.getUser;
    const { filterSubmission, filterKnowledgeRepository } = buildKnowledgeFilters(
      userId,
      user
    );

    const [submissionRes, knowledgeRepoRes] = await Promise.all([
      apolloClient.query({
        query: GET_USER_FOR_KNOWLEDGE,
        variables: { filter: filterSubmission },
      }),
      apolloClient.query({
        query: GET_KNOWLEDGE_REPOSITORY_STORE,
        variables: { filter: filterKnowledgeRepository },
      }),
    ]);

    // Format data in the new structured format
    const result = {
      user: {
        id: user.id,
        givenName: user.givenName,
        familyName: user.familyName,
        email: user.email,
        profileImageUrl: user.profileImageUrl,
        accessLevel: user.accessLevel || 'Standard',
      },
      featuredArticles: knowledgeRepoRes?.data?.listKnowledgeRepositoryStores?.items
        .filter(item => item.type === 'ARTICLE')
        .map(article => ({
          id: article.id,
          title: article.title,
          content: article.content,
          category: article.category,
          lastUpdated: article.updatedAt,
          isPublic: article.isPublic,
        })) || [],
      recentTutorials: knowledgeRepoRes?.data?.listKnowledgeRepositoryStores?.items
        .filter(item => item.type === 'TUTORIAL')
        .map(tutorial => ({
          id: tutorial.id,
          title: tutorial.title,
          content: tutorial.content,
          difficulty: tutorial.difficulty,
          duration: tutorial.duration,
          objectives: tutorial.objectives || [],
          steps: tutorial.steps || [],
          lastUpdated: tutorial.updatedAt,
        })) || [],
      popularTopics: Array.from(new Set(
        knowledgeRepoRes?.data?.listKnowledgeRepositoryStores?.items
          .flatMap(item => item.tags || [])
      )) || [],
      faqCategories: [
        {
          name: 'General',
          questions: knowledgeRepoRes?.data?.listKnowledgeRepositoryStores?.items
            .filter(item => item.type === 'FAQ')
            .map(faq => ({
              question: faq.title,
              answer: faq.content,
            })) || []
        }
      ],
      platformUpdates: knowledgeRepoRes?.data?.listKnowledgeRepositoryStores?.items
        .filter(item => item.type === 'UPDATE')
        .map(update => ({
          id: update.id,
          title: update.title,
          description: update.content,
          date: update.createdAt,
          impact: update.impact,
        })) || [],
      _metadata: {
        timestamp: new Date().toISOString(),
        dataSource: 'knowledge',
        userId
      }
    };

    return JSON.stringify(result);
  } catch (error) {
    logger.error('Error in fetchKnowledgeData', { 
      error: error.message,
      stack: error.stack,
      userId
    });
    // Return empty but properly structured data on error
    return JSON.stringify({
      user: {},
      featuredArticles: [],
      recentTutorials: [],
      popularTopics: [],
      faqCategories: [],
      platformUpdates: [],
      _metadata: {
        timestamp: new Date().toISOString(),
        dataSource: 'knowledge',
        userId,
        error: 'Failed to fetch knowledge data'
      }
    });
  }
}

/**
 * Fetches family-related data for a user in a structured format
 * @param {Object} apolloClient - The Apollo Client instance
 * @param {string} userId - The ID of the user to fetch data for
 * @returns {Promise<string>} - JSON string containing family data in a structured format
 */
async function fetchFamilyData(apolloClient, userId) {
  try {
    const [userRes, familyRes] = await Promise.all([
      apolloClient.query({ query: GET_USER_FOR_FAMILY, variables: { id: userId } }),
      apolloClient.query({ query: GET_USER_DETAILS_FOR_KNOWLEDGE, variables: { id: userId } }),
    ]);

    const user = userRes?.data?.getUser;
    const familyUser = familyRes?.data?.getUser;

    // Process family members
    const familyMembers = user?.familyMembers?.items || [];
    // Process family events (filter for upcoming events)
    const upcomingEvents = (user?.familyEvents?.items || [])
      .filter(event => new Date(event.startDateTime) > new Date())
      .sort((a, b) => new Date(a.startDateTime) - new Date(b.startDateTime));
    
    // Process family groups
    const familyGroups = user?.familyGroups?.items || [];
    
    // Process family tasks (filter for pending tasks)
    const pendingTasks = (user?.familyTasks?.items || [])
      .filter(task => !task.completed)
      .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate));

    // Format data in the new structured format
    const result = {
      user: {
        id: user.id,
        givenName: user.givenName,
        familyName: user.familyName,
        email: user.email,
        profileImageUrl: user.profileImageUrl,
        phone: user.phone,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
      },
      members: familyMembers.map(member => ({
        id: member.id,
        name: member.name,
        relationship: member.relationship,
        dateOfBirth: member.dateOfBirth,
        gender: member.gender,
        phone: member.phone,
        email: member.email,
        profileImageUrl: member.profileImageUrl,
      })),
      upcomingEvents: upcomingEvents.map(event => ({
        id: event.id,
        name: event.name,
        description: event.description,
        startDateTime: event.startDateTime,
        endDateTime: event.endDateTime,
        location: event.location,
        organizer: event.organizer,
        isRecurring: event.isRecurring,
      })),
      groups: familyGroups.map(group => ({
        id: group.id,
        name: group.name,
        description: group.description,
        memberCount: group.members?.items?.length || 0,
        createdAt: group.createdAt,
        updatedAt: group.updatedAt,
      })),
      pendingTasks: pendingTasks.map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        dueDate: task.dueDate,
        priority: task.priority,
        status: task.status,
        assignedTo: task.assignedTo ? {
          id: task.assignedTo.id,
          name: task.assignedTo.name,
          email: task.assignedTo.email,
        } : null,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      })),
      _metadata: {
        timestamp: new Date().toISOString(),
        dataSource: 'family',
        userId
      }
    };

    return JSON.stringify(result);
  } catch (error) {
    logger.error('Error in fetchFamilyData', { 
      error: error.message,
      stack: error.stack,
      userId
    });
    // Return empty but properly structured data on error
    return JSON.stringify({
      user: {},
      members: [],
      upcomingEvents: [],
      groups: [],
      pendingTasks: [],
      _metadata: {
        timestamp: new Date().toISOString(),
        dataSource: 'family',
        userId,
        error: 'Failed to fetch family data'
      }
    });
  }
}

/**
 * Fetches unified data for the Unify chatbot
 * @param {Object} apolloClient - The Apollo Client instance
 * @param {string} userId - The ID of the user to fetch data for
 * @returns {Promise<string>} - JSON string containing unified data
 */
async function fetchUnifiedData(apolloClient, userId) {
  try {
    const [combinedUserRes, knowledgeRes] = await Promise.all([
      apolloClient.query({ query: GET_USER_FOR_UNIFY, variables: { id: userId } }),
      apolloClient.query({ query: GET_USER_DETAILS_FOR_KNOWLEDGE, variables: { id: userId } }),
    ]);

    const user = combinedUserRes?.data?.getUser;
    const { filterSubmission, filterKnowledgeRepository } = buildKnowledgeFilters(
      userId,
      knowledgeRes?.data?.getUser
    );

    const [submissionRes, knowledgeRepoRes] = await Promise.all([
      apolloClient.query({ 
        query: GET_USER_FOR_KNOWLEDGE, 
        variables: { filter: filterSubmission } 
      }),
      apolloClient.query({
        query: GET_KNOWLEDGE_REPOSITORY_STORE,
        variables: { filter: filterKnowledgeRepository },
      }),
    ]);

    const communityData = {
      id: user.id,
      givenName: user.givenName,
      familyName: user.familyName,
      userAssociations: {
        items: user.userAssociations.items.filter(
          (assoc) => assoc.type === 'Organization' || assoc.organization !== null
        ),
      },
    };

    const familyData = {
      id: user.id,
      givenName: user.givenName,
      familyName: user.familyName,
      associations: user.associations,
      userAssociations: {
        items: user.userAssociations.items.filter(
          (assoc) => assoc.type === 'Person' || assoc.otherPerson !== null
        ),
      },
    };

    // Return data in the format expected by the unify formatter
    const result = {
      user: {
        id: user.id,
        givenName: user.givenName,
        familyName: user.familyName,
        email: user.email,
        profileImageUrl: user.profileImageUrl,
        bio: user.bio,
        // Include other user fields as needed
      },
      organizations: {
        items: user.userAssociations.items
          .filter(assoc => assoc.type === 'Organization' && assoc.organization)
          .map(assoc => ({
            id: assoc.organization.id,
            name: assoc.organization.name,
            description: assoc.organization.description,
            imageUrl: assoc.organization.imageUrl,
            membership: assoc.organization.membership,
          }))
      },
      events: { items: [] },
      activities: { items: [] },
      connections: {
        items: user.userAssociations.items
          .filter(assoc => assoc.type === 'Person' && assoc.otherPerson)
          .map(assoc => ({
            id: assoc.otherPerson.id,
            givenName: assoc.otherPerson.givenName,
            familyName: assoc.otherPerson.familyName,
            email: assoc.otherPerson.email,
            profileImageUrl: assoc.otherPerson.profileImageUrl,
          }))
      },
      submissions: { items: submissionRes?.data?.SubmissionByDate?.items || [] },
      knowledgeRepositories: { items: knowledgeRepoRes?.data?.listKnowledgeRepositoryStores?.items || [] },
      _metadata: {
        timestamp: new Date().toISOString(),
        dataSource: 'unified',
        userId
      }
    };

    return result;
  } catch (error) {
    logger.error('Error in fetchUnifiedData', {
      error: error.message,
      stack: error.stack,
      userId
    });
    return {
      user: {},
      organizations: [],
      events: [],
      activities: [],
      connections: [],
      submissions: [],
      knowledgeRepositories: [],
      _metadata: {
        timestamp: new Date().toISOString(),
        dataSource: 'unified',
        userId,
        error: 'Failed to fetch unified data'
      }
    };
  }
}

/**
 * Builds filters for knowledge-related queries
 * @param {string} userId - The ID of the current user
 * @param {Object} knowledgeUser - The user data containing associations
 * @returns {Object} - Filters for submissions and knowledge repositories
 */
function buildKnowledgeFilters(userId, knowledgeUser) {
  const orgIds = safeMapIds(knowledgeUser?.associatedOrganizationIds?.items, 'organizationId');
  const personIds = safeMapIds(knowledgeUser?.associatedPersonIds?.items, 'associatedPersonId');

  const memberOrs = [...personIds, ...orgIds].map((id) => ({ memberId: { eq: id } }));
  const userOrs = [...personIds, ...orgIds].map((id) => ({ userId: { eq: id } }));

  return {
    filterSubmission: {
      or: [{ createdBy: { eq: userId } }, { or: memberOrs }, { isPublic: { eq: true } }],
    },
    filterKnowledgeRepository: {
      or: [{ userId: { eq: userId } }, { or: userOrs }, { isPublic: { eq: true } }],
    },
  };
}

/**
 * Safely extracts IDs from an array of objects
 * @param {Array} items - Array of items to process
 * @param {string} key - The key to extract from each item
 * @returns {Array} - Array of extracted IDs
 */
function safeMapIds(items = [], key) {
  return items.filter((item) => item?.[key] && item[key] !== 'null').map((item) => item[key]);
}

/**
 * Extracts organization information from user data
 * @param {Object} userCommunityData - The user data containing organization associations
 * @returns {Array} - Array of organization objects with name, imageUrl, cityId, and membership
 */
function extractOrganizations(userCommunityData) {
  return (userCommunityData?.userAssociations?.items || [])
    .filter((assoc) => assoc.type === 'Organization' && assoc.organization)
    .map((assoc) => ({
      name: assoc.organization.name,
      imageUrl: assoc.organization.imageUrl,
      cityId: assoc.organization.cityId,
      membership: assoc.organization.membership,
    }));
}

module.exports = {
  fetchBasicUserData,
  fetchKnowledgeData,
  fetchFamilyData,
  fetchUnifiedData,
  buildKnowledgeFilters,
  safeMapIds,
  extractOrganizations
};
