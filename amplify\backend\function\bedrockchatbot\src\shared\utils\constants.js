const STATUS_CODES = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500
};

const ChatTypeEnum = Object.freeze({
  FAMILY: 'Family',
  KNOWLEDGE: 'Knowledge',
  DREAMS: 'Dreams',
  SPIRITUALITY: 'Spirituality',
  COMMUNITY: 'Community',
  UNIFY: 'Unify'
});

const DEFAULT_STATIC_RESPONSES = {
  [ChatTypeEnum.COMMUNITY]: "I'm here to help with community-related questions. It seems I'm having trouble accessing some information right now. Could you please rephrase your question or try again later?",
  [ChatTypeEnum.FAMILY]: "I'm here to assist with family-related matters. I'm currently experiencing some technical difficulties. Could you please try again in a few moments?",
  [ChatTypeEnum.KNOWLEDGE]: "I'm here to help with knowledge base queries. I'm having trouble accessing the knowledge base right now. Please try again later or rephrase your question.",
  [ChatTypeEnum.UNIFY]: "I'm here to assist with your questions. I'm currently experiencing some technical difficulties. Could you please try again in a few moments?",
  default: "I'm sorry, but I'm having trouble processing your request right now. Please try again later or contact support if the issue persists."
};

const TABLES = {
  CHATBOT_PROMPTS: 'ChatbotPromptsData',
  CHATBOT_PROMPT: 'chatbotPrompt'
};

const CONFIG = {
  MAX_QUESTION_LENGTH: 1000,
  MAX_PROMPT_LENGTH: 10000,
  DEFAULT_ERROR_MESSAGE: 'An error occurred while processing your request. Please try again later.',
  
};

module.exports = {
  STATUS_CODES,
  ChatTypeEnum,
  DEFAULT_STATIC_RESPONSES,
  TABLES,
  CONFIG
};
