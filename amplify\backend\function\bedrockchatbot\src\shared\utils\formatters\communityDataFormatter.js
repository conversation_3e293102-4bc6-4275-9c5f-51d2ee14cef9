const { createLogger } = require('../logger');
const logger = createLogger('communityDataFormatter');

/**
 * Formats a date string to a human-readable format
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date string
 */
function formatDate(dateString) {
  if (!dateString) return 'Date not specified';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Formats organization data into a readable string
 * @param {Object} org - Organization data
 * @returns {string} Formatted organization info
 */
function formatOrganization(org) {
  if (!org) return 'No organization data';

  return (
    `
  - **${org.name || 'Unnamed Organization'}**\n` +
    (org.description ? `    ${org.description}\n` : '') +
    (org.cityId ? `    Location: ${org.cityId}\n` : '') +
    (org.membership
      ? `    Your Membership:\n` +
        (org.membership.currentImpactScore !== undefined
          ? `      - Impact Score: ${org.membership.currentImpactScore}\n`
          : '') +
        (org.membership.MVPTokens !== undefined
          ? `      - MVP Tokens: ${org.membership.MVPTokens}\n`
          : '') +
        (org.membership.fundTokens !== undefined
          ? `      - Fund Tokens: ${org.membership.fundTokens}\n`
          : '')
      : '')
  );
}

/**
 * Formats event data into a readable string
 * @param {Object} event - Event data
 * @returns {string} Formatted event info
 */
function formatEvent(event) {
  if (!event) return 'No event data';

  return (
    `\n  - **${event.name || 'Unnamed Event'}**\n` +
    (event.startDateTime
      ? `    When: ${formatDate(event.startDateTime)}` +
        (event.endDateTime ? ` to ${formatDate(event.endDateTime)}\n` : '\n')
      : '') +
    (event.description ? `    ${event.description}\n` : '') +
    (event.location ? `    Location: ${event.location}\n` : '')
  );
}

/**
 * Converts community query data into a meaningful summary
 * @param {Object} data - Raw community data from the query
 * @returns {string} Formatted summary of the community data
 */
function formatCommunityData(data) {
  console.log('formatCommunityData: ', JSON.stringify(data, null, 2));
  if (!data) return 'No community data available';

  try {
    let summary = '';

    // User Information
    summary += `## User Information\n`;
    summary += `- **Name**: ${data.givenName || ''} ${data.familyName || ''}\n`;
    if (data.id) {
      summary += `- **User ID**: ${data.id}\n`;
    }
    summary += '\n';

    // Process user associations
    if (data.userAssociations?.items?.length > 0) {
      const organizations = data.userAssociations.items
        .filter((item) => item.type === 'Organization' && item.organization)
        .map((item) => item.organization);

      const people = data.userAssociations.items
        .filter((item) => item.type === 'Person')
        .map((item) => ({
          type: 'Person',
          createdAt: item.createdAt,
        }));

      const businesses = data.userAssociations.items
        .filter((item) => item.type === 'Business')
        .map((item) => ({
          type: 'Business',
          createdAt: item.createdAt,
        }));

      // Format Organizations
      if (organizations.length > 0) {
        summary += `## Organizations (${organizations.length})\n`;
        organizations.forEach((org) => {
          summary += formatOrganization(org);
        });
        summary += '\n';
      }

      // Format Personal Connections
      if (people.length > 0) {
        summary += `## Personal Connections (${people.length})\n`;
        people.forEach((person) => {
          summary += `- Connection established on ${formatDate(person.createdAt)}\n`;
        });
        summary += '\n';
      }

      // Format Business Connections
      if (businesses.length > 0) {
        summary += `## Business Connections (${businesses.length})\n`;
        businesses.forEach((business) => {
          summary += `- Business connection established on ${formatDate(business.createdAt)}\n`;
        });
        summary += '\n';
      }
    } else {
      summary += 'No community connections found.\n\n';
    }

    // Add metadata if present
    if (data._metadata) {
      summary += '---\n';
      summary += `*Last updated: ${new Date().toLocaleString()}*\n`;
      if (data._metadata.chatType) {
        summary += `*Chat Type: ${data._metadata.chatType}*\n`;
      }
    }
    return summary;
  } catch (error) {
    logger.error('Error formatting community data:', {
      error: error.message,
      stack: error.stack,
    });
    return 'Error processing community data. Please try again later.';
  }
}

module.exports = {
  formatCommunityData,
  formatDate,
  formatOrganization,
  formatEvent,
};
