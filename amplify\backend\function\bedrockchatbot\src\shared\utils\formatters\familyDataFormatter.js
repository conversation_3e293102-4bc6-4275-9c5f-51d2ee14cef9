const { createLogger } = require('../logger');
const logger = createLogger('familyDataFormatter');

/**
 * Formats a date string to a human-readable format
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date string
 */
function formatDate(dateString) {
  if (!dateString) return 'Date not specified';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Formats family member data into a readable string
 * @param {Object} member - Family member data
 * @returns {string} Formatted family member info
 */
function formatFamilyMember(member) {
  if (!member) return 'No member data';
  
  return (
    `- **${member.name || 'Unnamed Member'}**
` +
    (member.relationship ? `  - Relationship: ${member.relationship}
` : '') +
    (member.dateOfBirth ? `  - Date of Birth: ${formatDate(member.dateOfBirth)}
` : '') +
    (member.gender ? `  - Gender: ${member.gender}
` : '') +
    (member.phone ? `  - Phone: ${member.phone}
` : '') +
    (member.email ? `  - Email: ${member.email}
` : '')
  );
}

/**
 * Formats family event data into a readable string
 * @param {Object} event - Family event data
 * @returns {string} Formatted event info
 */
function formatFamilyEvent(event) {
  if (!event) return 'No event data';
  
  return (
    `- **${event.name || 'Unnamed Event'}**
` +
    (event.startDateTime
      ? `  - When: ${formatDate(event.startDateTime)}` +
        (event.endDateTime ? ` to ${formatDate(event.endDateTime)}\n` : '\n')
      : '') +
    (event.description ? `  - ${event.description}\n` : '') +
    (event.location ? `  - Location: ${event.location}\n` : '')
  );
}

/**
 * Converts family query data into a meaningful summary
 * @param {Object} data - Raw family data from the query
 * @returns {string} Formatted summary of the family data
 */
function formatFamilyData(data) {
  console.log('formatFamilyData input: ', JSON.stringify(data, null, 2));
  
  if (!data) {
    logger.warn('No family data provided to formatter');
    return 'No family data available';
  }

  try {
    // If data is already in the new format (from fetchFamilyData), use it directly
    if (data._metadata?.dataSource === 'family') {
      return formatStructuredFamilyData(data);
    }
    
    // Legacy format handling (kept for backward compatibility)
    return formatLegacyFamilyData(data);
  } catch (error) {
    logger.error('Error formatting family data:', {
      error: error.message,
      stack: error.stack,
      dataType: typeof data,
      dataKeys: data ? Object.keys(data) : []
    });
    return 'Error processing family data. Please try again later.';
  }
}

/**
 * Formats family data in the new structured format
 * @param {Object} data - Structured family data
 * @returns {string} Formatted family data
 */
function formatStructuredFamilyData(data) {
  let summary = '';
  
  // User Information
  if (data.user) {
    summary += `## Family Information\n`;
    summary += `- **User**: ${data.user.givenName || ''} ${data.user.familyName || ''}\n`;
    if (data.user.id) {
      summary += `- **User ID**: ${data.user.id}\n`;
    }
    summary += '\n';
  }

  // Family Members
  if (data.members?.length > 0) {
    summary += `## Family Members (${data.members.length})\n`;
    data.members.forEach((member) => {
      summary += formatFamilyMember(member);
    });
    summary += '\n';
  }

  // Upcoming Events
  if (data.upcomingEvents?.length > 0) {
    summary += `## Upcoming Family Events (${data.upcomingEvents.length})\n`;
    data.upcomingEvents.forEach((event) => {
      summary += formatFamilyEvent(event);
    });
    summary += '\n';
  }

  // Family Groups
  if (data.groups?.length > 0) {
    summary += `## Family Groups (${data.groups.length})\n`;
    data.groups.forEach((group) => {
      summary += `- **${group.name || 'Unnamed Group'}**\n`;
      if (group.description) {
        summary += `  - ${group.description}\n`;
      }
      if (group.memberCount > 0) {
        summary += `  - Members: ${group.memberCount}\n`;
      }
    });
    summary += '\n';
  }

  // Pending Tasks
  if (data.pendingTasks?.length > 0) {
    summary += `## Pending Family Tasks (${data.pendingTasks.length})\n`;
    data.pendingTasks.forEach((task) => {
      summary += `- **${task.title || 'Untitled Task'}**\n`;
      if (task.dueDate) {
        summary += `  - Due: ${formatDate(task.dueDate)}\n`;
      }
      if (task.assignedTo) {
        summary += `  - Assigned to: ${task.assignedTo.name || 'Unassigned'}\n`;
      }
    });
    summary += '\n';
  }

  // Add metadata if present
  if (data._metadata) {
    summary += '---\n';
    summary += `*Last updated: ${data._metadata.timestamp ? formatDate(data._metadata.timestamp) : new Date().toLocaleString()}*\n`;
    if (data._metadata.chatType) {
      summary += `*Chat Type: ${data._metadata.chatType}*\n`;
    }
  }

  return summary;
}

/**
 * Formats legacy family data structure
 * @param {Object} data - Legacy family data
 * @returns {string} Formatted family data
 */
function formatLegacyFamilyData(data) {
  let summary = '';
  
  // User Information
  summary += `## Family Information\n`;
  summary += `- **User**: ${data.givenName || ''} ${data.familyName || ''}\n`;
  if (data.id) {
    summary += `- **User ID**: ${data.id}\n`;
  }
  summary += '\n';

  // Process family members
  if (data.familyMembers?.items?.length > 0) {
    summary += `## Family Members (${data.familyMembers.items.length})\n`;
    data.familyMembers.items.forEach((member) => {
      summary += formatFamilyMember(member);
    });
    summary += '\n';
  }

  // Process family events
  if (data.familyEvents?.items?.length > 0) {
    const upcomingEvents = data.familyEvents.items.filter(
      (event) => new Date(event.startDateTime) > new Date()
    );
    
    if (upcomingEvents.length > 0) {
      summary += `## Upcoming Family Events (${upcomingEvents.length})\n`;
      upcomingEvents.forEach((event) => {
        summary += formatFamilyEvent(event);
      });
      summary += '\n';
    }
  }

  // Process family groups
  if (data.familyGroups?.items?.length > 0) {
    summary += `## Family Groups (${data.familyGroups.items.length})\n`;
    data.familyGroups.items.forEach((group) => {
      summary += `- **${group.name || 'Unnamed Group'}**\n`;
      if (group.description) {
        summary += `  - ${group.description}\n`;
      }
      if (group.members?.items?.length > 0) {
        summary += `  - Members: ${group.members.items.length}\n`;
      }
    });
    summary += '\n';
  }

  // Process family tasks
  if (data.familyTasks?.items?.length > 0) {
    const pendingTasks = data.familyTasks.items.filter(
      (task) => !task.completed
    );
    
    if (pendingTasks.length > 0) {
      summary += `## Pending Family Tasks (${pendingTasks.length})\n`;
      pendingTasks.forEach((task) => {
        summary += `- **${task.title || 'Untitled Task'}**\n`;
        if (task.dueDate) {
          summary += `  - Due: ${formatDate(task.dueDate)}\n`;
        }
        if (task.assignedTo) {
          summary += `  - Assigned to: ${task.assignedTo.name || 'Unassigned'}\n`;
        }
      });
      summary += '\n';
    }
  }

  // Add metadata if present
  if (data._metadata) {
    summary += '---\n';
    summary += `*Last updated: ${new Date().toLocaleString()}*\n`;
    if (data._metadata.chatType) {
      summary += `*Chat Type: ${data._metadata.chatType}*\n`;
    }
  }

  return summary;
}

module.exports = {
  formatFamilyData,
  formatDate,
  formatFamilyMember,
  formatFamilyEvent,
  formatStructuredFamilyData,
  formatLegacyFamilyData
};
