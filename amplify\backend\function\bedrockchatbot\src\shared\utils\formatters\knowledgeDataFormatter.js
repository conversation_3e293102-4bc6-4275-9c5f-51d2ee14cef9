const { createLogger } = require('../logger');
const logger = createLogger('knowledgeDataFormatter');

/**
 * Formats a date string to a human-readable format
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date string
 */
function formatDate(dateString) {
  if (!dateString) return 'Date not specified';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * Formats a help article into a readable string
 * @param {Object} article - Help article data
 * @returns {string} Formatted article info
 */
function formatArticle(article) {
  if (!article) return 'No article data';
  
  return (
    `### ${article.title || 'Untitled Article'}
` +
    (article.category ? `**Category**: ${article.category}\n` : '') +
    (article.lastUpdated ? `*Last updated: ${formatDate(article.lastUpdated)}*\n` : '') +
    (article.content ? `\n${article.content}\n` : '') +
    (article.relatedArticles?.length > 0 
      ? `\n**Related Articles**: ${article.relatedArticles.join(', ')}\n` 
      : '') +
    '---\n\n'
  );
}

/**
 * Formats a tutorial into a readable string
 * @param {Object} tutorial - Tutorial data
 * @returns {string} Formatted tutorial info
 */
function formatTutorial(tutorial) {
  if (!tutorial) return 'No tutorial data';
  
  return (
    `### ${tutorial.title || 'Untitled Tutorial'}
` +
    (tutorial.difficulty ? `**Difficulty**: ${tutorial.difficulty}\n` : '') +
    (tutorial.duration ? `**Duration**: ${tutorial.duration} minutes\n` : '') +
    (tutorial.objectives?.length > 0 
      ? `**Objectives**:\n${tutorial.objectives.map(obj => `- ${obj}`).join('\n')}\n` 
      : '') +
    (tutorial.steps?.length > 0 
      ? `\n**Steps**:\n${tutorial.steps.map((step, i) => `${i + 1}. ${step}`).join('\n')}\n` 
      : '') +
    '---\n\n'
  );
}

/**
 * Converts knowledge base query data into a meaningful summary
 * @param {Object} data - Raw knowledge data from the query
 * @returns {string} Formatted summary of the knowledge data
 */
function formatKnowledgeData(data) {
  console.log('formatKnowledgeData input: ', JSON.stringify(data, null, 2));
  
  if (!data) {
    logger.warn('No knowledge data provided to formatter');
    return 'No knowledge base data available';
  }

  try {
    // If data is already in the new format (from fetchKnowledgeData), use it directly
    if (data._metadata?.dataSource === 'knowledge') {
      return formatStructuredKnowledgeData(data);
    }
    
    // Legacy format handling (kept for backward compatibility)
    return formatLegacyKnowledgeData(data);
  } catch (error) {
    logger.error('Error formatting knowledge data:', {
      error: error.message,
      stack: error.stack,
      dataType: typeof data,
      dataKeys: data ? Object.keys(data) : []
    });
    return 'Error processing knowledge base data. Please try again later.';
  }
}

/**
 * Formats knowledge data in the new structured format
 * @param {Object} data - Structured knowledge data
 * @returns {string} Formatted knowledge data
 */
function formatStructuredKnowledgeData(data) {
  let summary = '';
  
  // User Information
  if (data.user) {
    summary += `## Knowledge Base Access\n`;
    summary += `- **User**: ${data.user.givenName || ''} ${data.user.familyName || ''}\n`;
    if (data.user.id) {
      summary += `- **User ID**: ${data.user.id}\n`;
    }
    summary += `- **Access Level**: ${data.accessLevel || 'Standard'}\n\n`;
  }

  // Featured Articles
  if (data.featuredArticles?.length > 0) {
    summary += `## Featured Articles (${data.featuredArticles.length})\n\n`;
    data.featuredArticles.forEach((article) => {
      summary += formatArticle(article);
    });
  }

  // Recent Tutorials
  if (data.recentTutorials?.length > 0) {
    summary += `## Recent Tutorials (${data.recentTutorials.length})\n\n`;
    data.recentTutorials.forEach((tutorial) => {
      summary += formatTutorial(tutorial);
    });
  }

  // Popular Topics
  if (data.popularTopics?.length > 0) {
    summary += `## Popular Topics\n\n`;
    summary += data.popularTopics.map(topic => `- ${topic}`).join('\n');
    summary += '\n\n';
  }

  // FAQ Categories
  if (data.faqCategories?.length > 0) {
    summary += `## Frequently Asked Questions\n\n`;
    data.faqCategories.forEach((category) => {
      summary += `### ${category.name || 'General'}\n`;
      if (category.questions?.length > 0) {
        category.questions.forEach((q) => {
          summary += `**Q:** ${q.question}\n`;
          summary += `**A:** ${q.answer}\n\n`;
        });
      }
    });
  }

  // Platform Updates
  if (data.platformUpdates?.length > 0) {
    const recentUpdates = [...data.platformUpdates]
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 3); // Show only 3 most recent updates
    
    if (recentUpdates.length > 0) {
      summary += `## Recent Platform Updates\n\n`;
      recentUpdates.forEach((update) => {
        summary += `### ${update.title || 'Update'} (${formatDate(update.date)})\n`;
        summary += `${update.description || ''}\n`;
        if (update.impact) {
          summary += `**Impact:** ${update.impact}\n`;
        }
        summary += '\n';
      });
    }
  }

  // Add metadata if present
  if (data._metadata) {
    summary += '---\n';
    summary += `*Last updated: ${data._metadata.timestamp ? formatDate(data._metadata.timestamp) : new Date().toLocaleString()}*\n`;
    if (data._metadata.chatType) {
      summary += `*Chat Type: ${data._metadata.chatType}*\n`;
    }
  }

  return summary;
}

/**
 * Formats legacy knowledge data structure
 * @param {Object} data - Legacy knowledge data
 * @returns {string} Formatted knowledge data
 */
function formatLegacyKnowledgeData(data) {
  let summary = '';
  
  // User Information
  summary += `## Knowledge Base Access\n`;
  summary += `- **User**: ${data.givenName || ''} ${data.familyName || ''}\n`;
  if (data.id) {
    summary += `- **User ID**: ${data.id}\n`;
  }
  summary += `- **Access Level**: ${data.accessLevel || 'Standard'}\n\n`;

  // Featured Articles
  if (data.featuredArticles?.items?.length > 0) {
    summary += `## Featured Articles (${data.featuredArticles.items.length})\n\n`;
    data.featuredArticles.items.forEach((article) => {
      summary += formatArticle(article);
    });
  }

  // Recent Tutorials
  if (data.recentTutorials?.items?.length > 0) {
    summary += `## Recent Tutorials (${data.recentTutorials.items.length})\n\n`;
    data.recentTutorials.items.forEach((tutorial) => {
      summary += formatTutorial(tutorial);
    });
  }

  // Popular Topics
  if (data.popularTopics?.length > 0) {
    summary += `## Popular Topics\n\n`;
    summary += data.popularTopics.map(topic => `- ${topic}`).join('\n');
    summary += '\n\n';
  }

  // FAQ Categories
  if (data.faqCategories?.length > 0) {
    summary += `## Frequently Asked Questions\n\n`;
    data.faqCategories.forEach((category) => {
      summary += `### ${category.name || 'General'}\n`;
      if (category.questions?.length > 0) {
        category.questions.forEach((q) => {
          summary += `**Q:** ${q.question}\n`;
          summary += `**A:** ${q.answer}\n\n`;
        });
      }
    });
  }

  // Platform Updates
  if (data.platformUpdates?.items?.length > 0) {
    const recentUpdates = data.platformUpdates.items
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 3); // Show only 3 most recent updates
    
    if (recentUpdates.length > 0) {
      summary += `## Recent Platform Updates\n\n`;
      recentUpdates.forEach((update) => {
        summary += `### ${update.title || 'Update'} (${formatDate(update.date)})\n`;
        summary += `${update.description || ''}\n`;
        if (update.impact) {
          summary += `**Impact:** ${update.impact}\n`;
        }
        summary += '\n';
      });
    }
  }

  // Add metadata if present
  if (data._metadata) {
    summary += '---\n';
    summary += `*Last updated: ${new Date().toLocaleString()}*\n`;
    if (data._metadata.chatType) {
      summary += `*Chat Type: ${data._metadata.chatType}*\n`;
    }
  }

  return summary;
}

module.exports = {
  formatKnowledgeData,
  formatDate,
  formatArticle,
  formatTutorial,
  formatStructuredKnowledgeData,
  formatLegacyKnowledgeData
};
