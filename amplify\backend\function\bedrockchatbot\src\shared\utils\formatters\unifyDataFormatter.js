const { createLogger } = require('../logger');
const logger = createLogger('unifyDataFormatter');

/**
 * Formats a date string to a human-readable format
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date string
 */
function formatDate(dateString) {
  if (!dateString) return 'Date not specified';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Formats organization data into a readable string
 * @param {Object} org - Organization data
 * @returns {string} Formatted organization info
 */
function formatOrganization(org) {
  if (!org) return 'No organization data';

  return (
    `\n  - **${org.name || 'Unnamed Organization'}**\n` +
    (org.description ? `    ${org.description}\n` : '') +
    (org.website ? `    Website: ${org.website}\n` : '') +
    (org.industry ? `    Industry: ${org.industry}\n` : '') +
    (org.size ? `    Size: ${org.size} employees\n` : '') +
    (org.membership
      ? `    Your Membership:\n` +
        (org.membership.currentImpactScore !== undefined
          ? `      - Impact Score: ${org.membership.currentImpactScore}\n`
          : '') +
        (org.membership.MVPTokens !== undefined
          ? `      - MVP Tokens: ${org.membership.MVPTokens}\n`
          : '') +
        (org.membership.fundTokens !== undefined
          ? `      - Fund Tokens: ${org.membership.fundTokens}\n`
          : '')
      : '')
  );
}

/**
 * Formats event data into a readable string
 * @param {Object} event - Event data
 * @returns {string} Formatted event info
 */
function formatEvent(event) {
  if (!event) return 'No event data';

  return (
    `\n  - **${event.name || 'Unnamed Event'}**\n` +
    (event.startDateTime
      ? `    When: ${formatDate(event.startDateTime)}` +
        (event.endDateTime ? ` to ${formatDate(event.endDateTime)}\n` : '\n')
      : '') +
    (event.description ? `    ${event.description}\n` : '') +
    (event.location ? `    Location: ${event.location}\n` : '') +
    (event.status ? `    Status: ${event.status}\n` : '')
  );
}

/**
 * Formats user profile information
 * @param {Object} user - User data
 * @returns {string} Formatted user info
 */
function formatUserProfile(user) {
  if (!user) return 'No user data available';
  
  return (
    `## User Information\n` +
    (user.givenName || user.familyName 
      ? `- **Name**: ${user.givenName || ''} ${user.familyName || ''}\n` 
      : '') +
    (user.email ? `- **Email**: ${user.email}\n` : '') +
    (user.phone ? `- **Phone**: ${user.phone}\n` : '') +
    (user.memberSince ? `- **Member Since**: ${formatDate(user.memberSince)}\n` : '')
  );
}

/**
 * Formats connection data into a readable string
 * @param {Object} connection - Connection data
 * @returns {string} Formatted connection info
 */
function formatConnection(connection) {
  if (!connection) return 'No connection data';
  
  const displayName =
    connection.name ||
    ((connection.givenName || connection.familyName)
      ? `${connection.givenName || ''} ${connection.familyName || ''}`.trim()
      : 'Unnamed Connection');
  return (
    `\n  - **${displayName}**\n` +
    (connection.type ? `    Type: ${connection.type}\n` : '') +
    (connection.createdAt ? `    Connected: ${formatDate(connection.createdAt)}\n` : '')
  );
}

/**
 * Formats activity data into a readable string
 * @param {Object} activity - Activity data
 * @returns {string} Formatted activity info
 */
function formatActivity(activity) {
  if (!activity) return 'No activity data';
  
  return (
    `\n  - **${activity.type || 'Activity'}**\n` +
    (activity.description ? `    ${activity.description}\n` : '') +
    (activity.timestamp ? `    ${formatDate(activity.timestamp)}\n` : '')
  );
}

/**
 * Converts unified query data into a meaningful summary
 * @param {Object} data - Raw unified data from the query
 * @returns {string} Formatted summary of the unified data
 */
function formatUnifyData(data) {
  // console.log('formatUnifyData: ', JSON.stringify(data, null, 2));
  if (!data) return 'No unified data available';

  try {
    let summary = '';

    // User Profile Section
    if (data.user) {
      summary += formatUserProfile(data.user);
      summary += '\n';
    }

    // Organizations Section
    if (data.organizations?.items?.length > 0) {
      summary += `## Organizations (${data.organizations.items.length})\n`;
      data.organizations.items.forEach((org) => {
        summary += formatOrganization(org);
      });
      summary += '\n';
    }

    // Upcoming Events Section
    if (data.events?.items?.length > 0) {
      const upcomingEvents = data.events.items
        .filter(event => event.startDateTime && new Date(event.startDateTime) > new Date())
        .sort((a, b) => new Date(a.startDateTime) - new Date(b.startDateTime));
      
      if (upcomingEvents.length > 0) {
        summary += `## Upcoming Events (${upcomingEvents.length})\n`;
        upcomingEvents.forEach((event) => {
          summary += formatEvent(event);
        });
        summary += '\n';
      }
    }

    // Recent Activity Section
    if (data.recentActivity?.items?.length > 0) {
      const recentActivities = data.recentActivity.items
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 5);
      
      summary += `## Recent Activity (${recentActivities.length})\n`;
      recentActivities.forEach((activity) => {
        summary += formatActivity(activity);
      });
      summary += '\n';
    }

    // Connections Section
    if (data.connections?.items?.length > 0) {
      summary += `## Connections (${data.connections.items.length})\n`;
      data.connections.items.forEach((connection) => {
        summary += formatConnection(connection);
      });
      summary += '\n';
    }

    // Add metadata if present
    if (data._metadata) {
      summary += '---\n';
      summary += `*Last updated: ${new Date().toLocaleString()}*\n`;
      if (data._metadata.chatType) {
        summary += `*Chat Type: ${data._metadata.chatType}*\n`;
      }
    }

    return summary;
  } catch (error) {
    logger.error('Error formatting unified data:', {
      error: error.message,
      stack: error.stack,
    });
    return 'Error processing unified data. Please try again later.';
  }
}

module.exports = {
  formatUnifyData,
  formatDate,
  formatUserProfile,
  formatOrganization,
  formatEvent,
  formatConnection,
  formatActivity,
};
