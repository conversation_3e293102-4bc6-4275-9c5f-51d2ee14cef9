/**
 * Shared Logging Utility for AWS Lambda Functions
 *
 * This module provides a structured logging solution using Pino,
 * optimized for AWS Lambda environments with CloudWatch integration.
 */
const pino = require('pino');
const pretty = require('pino-pretty');
const { v4: uuidv4 } = require('uuid');

let prettyStream;
if (process.env.NODE_ENV === 'development' || process.env.ENV === 'amplifydev') {
  prettyStream = pretty({
    colorize: true,
    translateTime: 'SYS:standard',
    ignore: 'pid,hostname',
    messageFormat: '{msg}',
    singleLine: false,
    sync: true,
  });
}

function getLogLevel() {
  if (process.env.AWS_LAMBDA_LOG_LEVEL) {
    return process.env.AWS_LAMBDA_LOG_LEVEL.toLowerCase();
  }
  const env = (process.env.ENV || process.env.NODE_ENV || 'development').toLowerCase();
  if (env === 'prod' || env === 'production') return 'warn';
  if (env === 'dev' || env === 'development') return 'debug';
  if (env === 'amplifydev' || env === 'staging') return 'info';
  return 'info';
}

const baseConfig = {
  level: getLogLevel(),
  formatters: {
    level: (label) => ({ level: label }),
    bindings: (bindings) => {
      const safeBindings = typeof bindings === 'object' && bindings !== null 
        ? bindings 
        : { context: bindings };
      
      return {
        nodeVersion: process.version,
        functionName: process.env.AWS_LAMBDA_FUNCTION_NAME || 'unknown',
        ...safeBindings,
      };
    },
    timestamp: () => `,"time":"${new Date().toISOString()}"`,
  },
  redact: {
    paths: [
      'password', '*.password', '*.secret', '*.token', '*.accessKey',
      '*.access_key', '*.api_key', '*.apiKey', '*.auth_token',
      'headers.authorization', 'headers.cookie', 'headers["x-api-key"]',
      'body.password', 'body.token', 'body.access_token',
      'response.headers["set-cookie"]', 'request.headers.authorization',
      'request.body.authToken',
    ],
    remove: true,
  },
  serializers: {
    err: pino.stdSerializers.err,
    error: pino.stdSerializers.err,
    req: (req) => {
      if (!req || !req.raw) return req;
      return {
        id: req.id,
        method: req.method,
        url: req.url,
        headers: req.headers,
        remoteAddress: req.socket.remoteAddress,
        remotePort: req.socket.remotePort,
      };
    },
    res: (res) => {
      if (!res || !res.raw) return res;
      return {
        statusCode: res.statusCode,
        headers: res.getHeaders(),
      };
    },
  },
  customLevels: { trace: 10, debug: 20, info: 30, warn: 40, error: 50, fatal: 60 },
  useOnlyCustomLevels: true,
};

const baseLoggerRaw = prettyStream
  ? pino(baseConfig, prettyStream)
  : pino(baseConfig);

function createSafeLoggerMethod(level) {
  return function(message, ...args) {
    try {
      let logObj = {};
      
      if (message && typeof message === 'object') {
        logObj = { ...message };
      } 
      else if (typeof message === 'string') {
        logObj.msg = message;
        if (args.length > 0 && args[0] && typeof args[0] === 'object') {
          Object.assign(logObj, args[0]);
        }
      } 
      else {
        logObj.msg = String(message);
      }
      
      return baseLoggerRaw[level](logObj);
    } catch (error) {
      console.error('Logger error:', error);
      console[level](message, ...args);
    }
  };
}

// Create the base logger with safe methods
const baseLogger = {
  trace: createSafeLoggerMethod('trace'),
  debug: createSafeLoggerMethod('debug'),
  info: createSafeLoggerMethod('info'),
  warn: createSafeLoggerMethod('warn'),
  error: createSafeLoggerMethod('error'),
  fatal: createSafeLoggerMethod('fatal'),
  child: (bindings) => {
    const childLogger = baseLoggerRaw.child(bindings);
    return {
      trace: createSafeLoggerMethod('trace').bind(childLogger),
      debug: createSafeLoggerMethod('debug').bind(childLogger),
      info: createSafeLoggerMethod('info').bind(childLogger),
      warn: createSafeLoggerMethod('warn').bind(childLogger),
      error: createSafeLoggerMethod('error').bind(childLogger),
      fatal: createSafeLoggerMethod('fatal').bind(childLogger),
      child: (bindings) => baseLogger.child(bindings)
    };
  }
};

const noopLogger = {
  trace: () => {}, debug: () => {}, info: () => {}, warn: () => {},
  error: () => {}, fatal: () => {}, child: () => noopLogger,
};

const logger = (process.env.NODE_ENV === 'test') ? noopLogger : baseLogger;

/**
 * Create a child logger with Lambda context
 */
function createLogger(context = {}, additionalFields = {}) {
  if (typeof context === 'string') {
    return createComponentLogger(context, additionalFields);
  }
  const contextFields = {
    requestId: context.awsRequestId || context.requestId || uuidv4(),
    remainingTimeMs: context.getRemainingTimeInMillis
      ? context.getRemainingTimeInMillis() : undefined,
    ...additionalFields,
  };
  Object.keys(contextFields).forEach(k => {
    if (contextFields[k] === undefined) delete contextFields[k];
  });
  return logger.child(contextFields);
}

/**
 * Other specialized logger factories...
 */
function createUserLogger(context = {}, userId = null, additionalFields = {}) {
  return createLogger(context, {
    component: 'user',
    userId: userId || 'anonymous',
    userType: additionalFields.userType || 'user',
    ...additionalFields,
  });
}

function createComponentLogger(component, additionalFields = {}) {
  return createLogger({ component, ...additionalFields });
}

function createDatabaseLogger(context = {}, operation = '', tableName = '', additionalFields = {}) {
  return createLogger(context, {
    component: 'database',
    operation,
    tableName,
    ...additionalFields,
  });
}

function createApiLogger(context = {}, method = '', path = '', additionalFields = {}) {
  return createLogger(context, {
    component: 'api',
    method,
    path,
    ...additionalFields,
  });
}

/**
 * Performance, error, and success helpers...
 */
function logPerformance(logger, operation, startTime, additionalMetrics = {}) {
  const duration = Date.now() - startTime;
  const metrics = { operation, durationMs: duration, ...additionalMetrics };
  logger.info({ performanceMetrics: metrics }, `Performance: ${operation} completed in ${duration}ms`);
  return duration;
}

function logError(loggerInstance, error, operation = 'unknown', context = {}) {
  const err = (error instanceof Error) ? error : new Error(String(error));
  const errorInfo = {
    name: err.name, message: err.message, stack: err.stack, code: err.code,
    ...(err.response && { response: err.response }),
    ...(err.config && { config: err.config }),
  };
  loggerInstance.error(
    { error: errorInfo, operation, errorContext: context, isOperational: !!err.isOperational },
    `Error in ${operation}: ${err.message}`
  );
}

function logSuccess(loggerInstance, operation, result = {}, summary = {}, additionalFields = {}) {
  loggerInstance.info(
    { operation, success: true, resultSummary: summary, ...result, ...additionalFields },
    `Successfully completed ${operation}`
  );
}

function logHttpRequest(loggerInstance, req, res, startTime, additionalFields = {}) {
  const duration = Date.now() - startTime;
  const { method, originalUrl: url, params, query, headers } = req;
  const { statusCode } = res;
  const info = {
    method, url, statusCode, durationMs: duration,
    params, query,
    headers: {
      'content-type': headers['content-type'],
      'user-agent': headers['user-agent'],
      'x-forwarded-for': headers['x-forwarded-for'],
    },
    ...additionalFields,
  };
  const level = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
  loggerInstance[level]({ ...info }, `${method} ${url} ${statusCode} - ${duration}ms`);
}

module.exports = {
  createLogger,
  createUserLogger,
  createComponentLogger,
  createDatabaseLogger,
  createApiLogger,
  logPerformance,
  logError,
  logSuccess,
  logHttpRequest,
  baseLogger,
};
