const { STATUS_CODES } = require('./constants');
const standardizedErrorUtils = require('./standardizedErrorUtils');
const { createLogger } = require('./logger');

// Create a logger for this module
const logger = createLogger('responseUtils');

function createStandardResponse(data, message, statusCode) {
  return {
    statusCode,
    message,
    data: data === null || data === undefined ? null : data,
  };
}

function createSuccessResponse(data, message, statusCode = STATUS_CODES.SUCCESS) {
  return createStandardResponse(data, message, statusCode);
}

function createErrorResponse(message, statusCode, data = null) {
  return createStandardResponse(data, message, statusCode);
}

/**
 * Create unauthorized response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Unauthorized response
 */
function createUnauthorizedResponse(message = 'Unauthorized access') {
  return createErrorResponse(message, STATUS_CODES.UNAUTHORIZED);
}

/**
 * Create forbidden response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Forbidden response
 */
function createForbiddenResponse(message = 'Access forbidden') {
  return createErrorResponse(message, STATUS_CODES.FORBIDDEN);
}

/**
 * Create bad request response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Bad request response
 */
function createBadRequestResponse(message = 'Bad request') {
  return createErrorResponse(message, STATUS_CODES.BAD_REQUEST);
}

/**
 * Create not found response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Not found response
 */
function createNotFoundResponse(message = 'Resource not found') {
  return createErrorResponse(message, STATUS_CODES.NOT_FOUND);
}

/**
 * Create internal server error response
 * @param {string} message - Custom message (optional)
 * @returns {object} - Internal server error response
 */
function createInternalErrorResponse(message = 'Internal server error') {
  return createErrorResponse(message, STATUS_CODES.INTERNAL_ERROR);
}

/**
 * Handle service errors and return appropriate response
 * @param {Error} error - Error object
 * @param {string} defaultMessage - Default error message
 * @param {string} functionName - GraphQL function name for standardized errors (optional)
 * @returns {object} - Error response
 */
function handleServiceError(error, defaultMessage = 'Service error occurred', functionName = null) {
  logger.error('Service error occurred', {
    error: {
      message: error.message,
      name: error.name,
      stack: error.stack,
      ...(error.code && { code: error.code })
    }
  });

  if (functionName) {
    return standardizedErrorUtils.createAutoDetectedError(functionName, error, 'service operation');
  }

  let errorMessage;
  if (error?.message?.includes('timeout')) {
    errorMessage = 'Service timeout. Please try again in a few moments.';
  } else if (error?.message?.includes('network')) {
    errorMessage = 'Network error. Please check your connection and try again.';
  } else if (error?.message?.includes('validation')) {
    errorMessage = 'Invalid request. Please verify your parameters.';
  } else {
    errorMessage = error?.message || defaultMessage;
  }
  return createInternalErrorResponse(errorMessage);
}

module.exports = {
  createSuccessResponse,
  createErrorResponse,
  createUnauthorizedResponse,
  createForbiddenResponse,
  createBadRequestResponse,
  createNotFoundResponse,
  createInternalErrorResponse,
  handleServiceError,
  standardizedErrors: standardizedErrorUtils,
};
