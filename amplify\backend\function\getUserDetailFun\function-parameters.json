{"lambdaLayers": [], "permissions": {"api": {"myvillageprojectadmi": ["Query", "Mutation", "Subscription"]}, "auth": {"myvillageprojectadmifeb4ea87": ["read"]}}, "environmentVariableList": [{"cloudFormationParameterName": "accessKeyId", "environmentVariableName": "ACCESS_KEY_ID"}, {"cloudFormationParameterName": "webClientId", "environmentVariableName": "WEB_CLIENT_ID"}, {"cloudFormationParameterName": "secretAccessKey", "environmentVariableName": "SECRET_ACCESS_KEY"}, {"cloudFormationParameterName": "<PERSON><PERSON><PERSON><PERSON>", "environmentVariableName": "API_KEY"}, {"cloudFormationParameterName": "stripeWebhookSecret", "environmentVariableName": "STRIPE_WEBHOOK_SECRET"}]}