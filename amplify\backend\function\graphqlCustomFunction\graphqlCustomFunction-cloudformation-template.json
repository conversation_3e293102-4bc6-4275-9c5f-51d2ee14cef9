{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Windows\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.0.0\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "apimyvillageprojectadmiGraphQLAPIIdOutput": {"Type": "String", "Default": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "apimyvillageprojectadmiGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apimyvillageprojectadmiGraphQLAPIEndpointOutput"}, "authmyvillageprojectadmifeb4ea87UserPoolId": {"Type": "String", "Default": "authmyvillageprojectadmifeb4ea87UserPoolId"}, "storages3myvillageprojectadminportalorgprofilelogoBucketName": {"Type": "String", "Default": "storages3myvillageprojectadminportalorgprofilelogoBucketName"}, "accessKeyId": {"Type": "String"}, "secretAccessKey": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "graphqlCustomFunction", {"Fn::Join": ["", ["graphqlCustomFunction", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT": {"Ref": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apimyvillageprojectadmiGraphQLAPIEndpointOutput"}, "AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID": {"Ref": "authmyvillageprojectadmifeb4ea87UserPoolId"}, "STORAGE_S3MYVILLAGEPROJECTADMINPORTALORGPROFILELOGO_BUCKETNAME": {"Ref": "storages3myvillageprojectadminportalorgprofilelogoBucketName"}, "ACCESS_KEY_ID": {"Ref": "accessKeyId"}, "SECRET_ACCESS_KEY": {"Ref": "secretAccessKey"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs16.x", "Layers": [], "Timeout": 25}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "myvillageprojectadmiLambdaRoleeadc6474", {"Fn::Join": ["", ["myvillageprojectadmiLambdaRoleeadc6474", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["appsync:GraphQL"], "Resource": [{"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "/types/Query/*"]]}, {"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "/types/Mutation/*"]]}, {"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apimyvillageprojectadmiGraphQLAPIIdOutput"}, "/types/Subscription/*"]]}]}, {"Effect": "Allow", "Action": ["cognito-idp:ConfirmSignUp", "cognito-idp:AdminCreateUser", "cognito-idp:CreateUserImportJob", "cognito-idp:AdminSetUserSettings", "cognito-idp:AdminLinkProviderForUser", "cognito-idp:CreateIdentityProvider", "cognito-idp:AdminConfirmSignUp", "cognito-idp:AdminDisableUser", "cognito-idp:AdminRemoveUserFromGroup", "cognito-idp:SetUserMFAPreference", "cognito-idp:SetUICustomization", "cognito-idp:SignUp", "cognito-idp:VerifyUserAttribute", "cognito-idp:SetRiskConfiguration", "cognito-idp:StartUserImportJob", "cognito-idp:AdminSetUserPassword", "cognito-idp:AssociateSoftwareToken", "cognito-idp:CreateResourceServer", "cognito-idp:RespondToAuthChallenge", "cognito-idp:CreateUserPoolClient", "cognito-idp:AdminUserGlobalSignOut", "cognito-idp:GlobalSignOut", "cognito-idp:AddCustomAttributes", "cognito-idp:CreateGroup", "cognito-idp:CreateUserPool", "cognito-idp:AdminForgetDevice", "cognito-idp:AdminAddUserToGroup", "cognito-idp:AdminRespondToAuthChallenge", "cognito-idp:ForgetDev<PERSON>", "cognito-idp:CreateUserPoolDomain", "cognito-idp:AdminEnableUser", "cognito-idp:AdminUpdateDeviceStatus", "cognito-idp:StopUserImportJob", "cognito-idp:InitiateAuth", "cognito-idp:AdminInitiateAuth", "cognito-idp:AdminSetUserMFAPreference", "cognito-idp:ConfirmForgotPassword", "cognito-idp:SetUserSettings", "cognito-idp:VerifySoftwareToken", "cognito-idp:AdminDisableProviderForUser", "cognito-idp:SetUserPoolMfaConfig", "cognito-idp:ChangePassword", "cognito-idp:ConfirmDevice", "cognito-idp:AdminResetUserPassword", "cognito-idp:ResendConfirmationCode", "cognito-identity:Describe*", "cognito-identity:Get*", "cognito-identity:List*", "cognito-idp:Describe*", "cognito-idp:AdminGetDevice", "cognito-idp:AdminGetUser", "cognito-idp:AdminList*", "cognito-idp:List*", "cognito-sync:Describe*", "cognito-sync:Get*", "cognito-sync:List*", "iam:ListOpenIdConnectProviders", "iam:ListRoles", "sns:ListPlatformApplications", "cognito-idp:ForgotPassword", "cognito-idp:UpdateAuthEventFeedback", "cognito-idp:UpdateResourceServer", "cognito-idp:UpdateUserPoolClient", "cognito-idp:AdminUpdateUserAttributes", "cognito-idp:UpdateUserAttributes", "cognito-idp:UpdateUserPoolDomain", "cognito-idp:UpdateIdentityProvider", "cognito-idp:UpdateGroup", "cognito-idp:AdminUpdateAuthEventFeedback", "cognito-idp:UpdateDeviceStatus", "cognito-idp:UpdateUserPool", "cognito-idp:DeleteUserPoolDomain", "cognito-idp:DeleteResourceServer", "cognito-idp:DeleteGroup", "cognito-idp:AdminDeleteUserAttributes", "cognito-idp:DeleteUserPoolClient", "cognito-idp:DeleteUserAttributes", "cognito-idp:DeleteUserPool", "cognito-idp:AdminDeleteUser", "cognito-idp:DeleteIdentityProvider", "cognito-idp:Delete<PERSON>ser"], "Resource": [{"Fn::Join": ["", ["arn:aws:cognito-idp:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":userpool/", {"Ref": "authmyvillageprojectadmifeb4ea87UserPoolId"}]]}]}, {"Effect": "Allow", "Action": "s3:ListBucket", "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "storages3myvillageprojectadminportalorgprofilelogoBucketName"}]]}]}, {"Effect": "Allow", "Action": ["s3:PutObject", "s3:GetObject", "s3:DeleteObject"], "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "storages3myvillageprojectadminportalorgprofilelogoBucketName"}, "/*"]]}]}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}