/**
 * Wallet and Transaction Module Integration Test Suite
 * Tests for interactions between wallet and transaction modules
 */

// Create mock DynamoDB instance
const mockDynamoDB = {
  getItem: jest.fn(),
  putItem: jest.fn(),
  updateItem: jest.fn(),
  scan: jest.fn(),
  query: jest.fn(),
  describeTable: jest.fn(),
  batchGetItem: jest.fn(),
  batchWriteItem: jest.fn()
};

const mockData = require('../shared/mockData');

// Mock AWS and shared utilities
jest.mock('../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

const walletService = require('../../modules/wallet/wallet.service');
const transactionService = require('../../modules/transaction/transaction.service');

jest.mock('../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../shared/blockchain/contractService', () => ({
  getContractUSDCBalance: jest.fn().mockResolvedValue('10000000000'), // 10,000 USDC
  getWalletAddress: jest.fn().mockReturnValue('******************************************'),
  isBlockchainConnected: jest.fn().mockReturnValue(true),
  depositUSDCToContract: jest.fn().mockResolvedValue(
    mockData.createMockBlockchainTransaction('deposit')
  ),
  transferUSDCToUser: jest.fn().mockResolvedValue(
    mockData.createMockBlockchainTransaction('transfer')
  )
}));

// Make constants available globally
global.TOKEN_TYPES = mockData.MOCK_CONSTANTS.TOKEN_TYPES;
global.TRANSACTION_TYPES = mockData.MOCK_CONSTANTS.TRANSACTION_TYPES;
global.TRANSACTION_STATUS = mockData.MOCK_CONSTANTS.TRANSACTION_STATUS;
global.SWAP_STATUS = mockData.MOCK_CONSTANTS.SWAP_STATUS;
global.STATUS_CODES = mockData.MOCK_CONSTANTS.STATUS_CODES;
global.CENTRAL_WALLET_ID = mockData.MOCK_CONSTANTS.CENTRAL_WALLET_ID;

jest.mock('../../shared/constants/index', () => mockData.MOCK_CONSTANTS);

// Set up global test utilities
global.testUtils = {
  createMockEvent: mockData.createMockEvent,
  createMockArgs: mockData.createMockArgs,
  createMockUser: mockData.createMockUser,
  createMockSwapRequest: mockData.createMockSwapRequest,
  createMockWalletBalance: mockData.createMockWalletBalance,
  mockDynamoDBSuccess: mockData.mockDynamoDBSuccess,
  mockDynamoDBError: mockData.mockDynamoDBError
};

describe('Wallet and Transaction Module Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default successful DynamoDB responses
    mockDynamoDB.getItem.mockReturnValue(mockData.mockDynamoDBSuccess({
      Item: mockData.TEST_WALLETS.USER_WITH_BALANCE
    }));
    mockDynamoDB.putItem.mockReturnValue(mockData.mockDynamoDBSuccess());
    mockDynamoDB.updateItem.mockReturnValue(mockData.mockDynamoDBSuccess());
    mockDynamoDB.scan.mockReturnValue(mockData.mockDynamoDBSuccess({ Items: [] }));
  });

  describe('Token Minting Integration', () => {
    test('should mint tokens and update wallet balance atomically', async () => {
      // Arrange
      const amount = 1000;
      const description = 'Integration test mint';
      const adminUserId = 'admin-123';

      // Mock central wallet balance with proper DynamoDB format
      mockDynamoDB.getItem.mockReturnValue(mockData.mockDynamoDBSuccess({
        Item: {
          id: { S: 'central-mvt-wallet' },
          balance: { N: '100000' },
          totalMinted: { N: '100000' },
          totalTransferred: { N: '50000' },
          lastMintedAt: { S: '2024-01-01T00:00:00.000Z' },
          createdAt: { S: '2024-01-01T00:00:00.000Z' },
          updatedAt: { S: '2024-01-01T00:00:00.000Z' }
        }
      }));

      // Act
      const transaction = await transactionService.mintMVTTokens(amount, description, adminUserId);

      // Assert
      expect(transaction.transactionType).toBe('ADMIN_MINT');
      expect(transaction.amount).toBe(amount);
      expect(transaction.status).toBe('COMPLETED');

      // Verify wallet service was called to update balance (using actual table name)
      expect(mockDynamoDB.updateItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTTokenWallet',
          Key: { id: { S: 'central-mvt-wallet' } }
        })
      );

      // Verify transaction was logged
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTWalletTransaction',
          Item: expect.objectContaining({
            transactionType: 'ADMIN_MINT',
            amount: amount
          })
        })
      );
    });
  });

  describe('Token Transfer Integration', () => {
    test('should transfer tokens between wallets with proper balance updates', async () => {
      // Arrange
      const fromUserId = 'sender-123';
      const toUserId = 'recipient-123';
      const amount = 500;
      const description = 'Integration test transfer';

      // Mock sender and recipient balances with proper DynamoDB format
      mockDynamoDB.getItem
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: `user-wallet-${fromUserId}` },
            userId: { S: fromUserId },
            balance: { N: '1000' },
            lockedBalance: { N: '0' },
            totalReceived: { N: '1000' },
            totalSent: { N: '0' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }))
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: `user-wallet-${toUserId}` },
            userId: { S: toUserId },
            balance: { N: '200' },
            lockedBalance: { N: '0' },
            totalReceived: { N: '200' },
            totalSent: { N: '0' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }));

      // Act
      const transaction = await transactionService.transferMVTBetweenUsers(fromUserId, toUserId, amount, description);

      // Assert
      expect(transaction.transactionType).toBe('USER_TO_USER_TRANSFER');
      expect(transaction.fromUserId).toBe(fromUserId);
      expect(transaction.toUserId).toBe(toUserId);
      expect(transaction.amount).toBe(amount);

      // Verify both wallet updates occurred
      expect(mockDynamoDB.updateItem).toHaveBeenCalledTimes(2);
    });

    test('should validate transfer amounts and user balances', async () => {
      // Arrange
      const fromUserId = 'sender-123';
      const toUserId = 'recipient-123';
      const amount = 500;
      const description = 'Validation test transfer';

      // Mock sender and recipient balances for validation
      mockDynamoDB.getItem
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: `user-wallet-${fromUserId}` },
            userId: { S: fromUserId },
            balance: { N: '1000' },
            lockedBalance: { N: '0' },
            totalReceived: { N: '1000' },
            totalSent: { N: '0' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }))
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: `user-wallet-${toUserId}` },
            userId: { S: toUserId },
            balance: { N: '200' },
            lockedBalance: { N: '0' },
            totalReceived: { N: '200' },
            totalSent: { N: '0' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }));

      // Act
      const transaction = await transactionService.transferMVTBetweenUsers(fromUserId, toUserId, amount, description);

      // Assert
      expect(transaction.transactionType).toBe('USER_TO_USER_TRANSFER');
      expect(transaction.fromUserId).toBe(fromUserId);
      expect(transaction.toUserId).toBe(toUserId);
      expect(transaction.amount).toBe(amount);
      expect(transaction.status).toBe('COMPLETED');
    });
  });

  describe('Data Consistency Integration', () => {
    test('should maintain consistent state across wallet and transaction modules', async () => {
      // Arrange
      const userId = 'test-user-123';
      const amount = 500;
      const adminUserId = 'admin-123';

      // Mock central and user wallet balances with sufficient funds
      mockDynamoDB.getItem
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: 'central-mvt-wallet' },
            balance: { N: '100000' }, // Sufficient balance
            totalMinted: { N: '100000' },
            totalTransferred: { N: '50000' },
            lastMintedAt: { S: '2024-01-01T00:00:00.000Z' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }))
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: `user-wallet-${userId}` },
            userId: { S: userId },
            balance: { N: '0' },
            lockedBalance: { N: '0' },
            totalReceived: { N: '0' },
            totalSent: { N: '0' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }));

      // Act
      const transaction = await transactionService.transferMVTToUser(userId, amount, 'test', adminUserId);

      // Assert
      expect(transaction.transactionType).toBe('CENTRAL_TO_USER_TRANSFER');
      expect(transaction.amount).toBe(amount);

      // Verify both central and user wallet updates
      expect(mockDynamoDB.updateItem).toHaveBeenCalledTimes(2);

      // Verify transaction logging
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTWalletTransaction',
          Item: expect.objectContaining({
            transactionType: 'CENTRAL_TO_USER_TRANSFER',
            amount: amount
          })
        })
      );
    });
  });

  describe('Central Wallet Balance Verification', () => {
    test('should verify central wallet balance updates during MVT transfer', async () => {
      // Arrange
      const userId = 'test-user-123';
      const transferAmount = 100;
      const initialCentralBalance = 50000;
      const initialUserBalance = 1000;

      // Mock initial central wallet balance
      mockDynamoDB.getItem
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: 'central-mvt-wallet' },
            balance: { N: initialCentralBalance.toString() },
            totalReceived: { N: '10000' },
            totalTransferred: { N: '5000' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }))
        // Mock user wallet balance
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: `user-wallet-${userId}` },
            userId: { S: userId },
            balance: { N: initialUserBalance.toString() },
            lockedBalance: { N: transferAmount.toString() },
            totalReceived: { N: initialUserBalance.toString() },
            totalSent: { N: '0' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }));

      // Act - Simulate locked MVT transfer to central wallet
      const result = await walletService.transferLockedMVTToCentral(userId, transferAmount);

      // Assert - Verify balance changes
      expect(result.success).toBe(true);
      expect(result.previousCentralBalance).toBe(initialCentralBalance);
      expect(result.newCentralBalance).toBe(initialCentralBalance + transferAmount);
      expect(result.previousUserBalance).toBe(initialUserBalance);
      expect(result.newUserBalance).toBe(initialUserBalance - transferAmount);

      // Verify central wallet update was called
      expect(mockDynamoDB.updateItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTTokenWallet',
          Key: { id: { S: 'central-mvt-wallet' } },
          UpdateExpression: expect.stringContaining('ADD balance :amount, totalReceived :amount')
        })
      );

      // Verify user wallet update was called
      expect(mockDynamoDB.updateItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTTokenWallet',
          Key: { id: { S: `user-wallet-${userId}` } },
          UpdateExpression: expect.stringContaining('ADD balance :negativeAmount, totalSent :amount')
        })
      );
    });

    test('should maintain token accounting consistency during swap approval', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mvtAmount = 100;
      const initialCentralBalance = 50000;
      const initialUserBalance = 1000;
      const lockedBalance = mvtAmount;

      // Mock wallet balances for atomic transfer
      mockDynamoDB.getItem
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: 'central-mvt-wallet' },
            balance: { N: initialCentralBalance.toString() },
            totalReceived: { N: '10000' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }))
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: {
            id: { S: `user-wallet-${userId}` },
            userId: { S: userId },
            balance: { N: initialUserBalance.toString() },
            lockedBalance: { N: lockedBalance.toString() },
            totalReceived: { N: initialUserBalance.toString() },
            totalSent: { N: '0' },
            createdAt: { S: '2024-01-01T00:00:00.000Z' },
            updatedAt: { S: '2024-01-01T00:00:00.000Z' }
          }
        }));

      // Act
      const transferResult = await walletService.transferLockedMVTToCentral(userId, mvtAmount);

      // Assert - Token accounting consistency
      const totalTokensBefore = initialCentralBalance + initialUserBalance;
      const totalTokensAfter = transferResult.newCentralBalance + transferResult.newUserBalance;

      expect(totalTokensBefore).toBe(totalTokensAfter);
      expect(transferResult.newCentralBalance - transferResult.previousCentralBalance).toBe(mvtAmount);
      expect(transferResult.previousUserBalance - transferResult.newUserBalance).toBe(mvtAmount);
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle wallet service failures during transaction creation', async () => {
      // Arrange
      const amount = 1000;
      const adminUserId = 'admin-123';

      // Mock central wallet balance first
      mockDynamoDB.getItem.mockReturnValue(mockData.mockDynamoDBSuccess({
        Item: {
          id: { S: 'central-mvt-wallet' },
          balance: { N: '100000' },
          totalMinted: { N: '100000' },
          totalTransferred: { N: '50000' },
          lastMintedAt: { S: '2024-01-01T00:00:00.000Z' },
          createdAt: { S: '2024-01-01T00:00:00.000Z' },
          updatedAt: { S: '2024-01-01T00:00:00.000Z' }
        }
      }));

      // Mock wallet update failure
      mockDynamoDB.updateItem.mockReturnValue(
        mockData.mockDynamoDBError(new Error('Wallet update failed'))
      );

      // Act & Assert
      await expect(
        transactionService.mintMVTTokens(amount, 'test', adminUserId)
      ).rejects.toThrow();
    });
  });
});
