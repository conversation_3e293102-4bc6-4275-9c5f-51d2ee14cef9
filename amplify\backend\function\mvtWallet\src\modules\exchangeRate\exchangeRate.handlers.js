const exchangeRateService = require('./exchangeRate.service');
const responseUtils = require('../../shared/utils/responseUtils');
const standardizedErrorUtils = require('../../shared/utils/standardizedErrorUtils');
const { createLogger } = require('../../shared/utils/logger');

/**
 * Handle getMVTWalletExchangeRate request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleGetMVTWalletExchangeRate(event, args) {
  console.log("Processing getMVTWalletExchangeRate request...");

  try {
    const exchangeRateData = await exchangeRateService.getExchangeRateSummary();

    if (!exchangeRateData) {
      console.error("Exchange rate service returned null/undefined data");
      return standardizedErrorUtils.createSystemError(
        'getMVTWalletExchangeRate',
        new Error("Exchange rate service is currently unavailable. Please try again later."),
        'exchange rate retrieval'
      );
    }

    // Determine response based on liquidity status
    const status = exchangeRateData.liquidityStatus?.status;
    
    const logger = createLogger(event.requestContext || {}, { operation: 'getMVTWalletExchangeRate' });

    switch (status) {
      case "FALLBACK_MODE":
        logger.info({
          operation: 'getMVTWalletExchangeRate',
          status: 'FALLBACK_MODE',
          exchangeRateData: exchangeRateData
        }, "Returning fallback exchange rate due to liquidity issues");

        return responseUtils.createSuccessResponse(
          exchangeRateData,
          "Exchange rate retrieved successfully (using fallback values due to liquidity constraints)"
        );

      case "UNAVAILABLE":
        logger.error({
          operation: 'getMVTWalletExchangeRate',
          status: 'UNAVAILABLE',
          reason: 'insufficient_liquidity'
        }, "Exchange rate service indicates rates are unavailable");

        return standardizedErrorUtils.createBusinessLogicError(
          'getMVTWalletExchangeRate',
          "Exchange rates are currently unavailable. Please check system liquidity and try again.",
          'exchange rate calculation',
          { status: 'UNAVAILABLE', reason: 'insufficient_liquidity' }
        );
        
      default:
        console.log("Exchange rate retrieved successfully:", exchangeRateData);
        return responseUtils.createSuccessResponse(
          exchangeRateData,
          "Exchange rate retrieved successfully"
        );
    }
  } catch (error) {
    console.error("Error fetching exchange rate:", error);
    return standardizedErrorUtils.createAutoDetectedError(
      'getMVTWalletExchangeRate',
      error,
      'exchange rate retrieval'
    );
  }
}

module.exports = {
  handleGetMVTWalletExchangeRate
};
