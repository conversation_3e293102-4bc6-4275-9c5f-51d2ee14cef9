// HYBRID ARCHITECTURE: Exchange Rate Service
// This service calculates exchange rates locally using:
// - Local MVT wallet balances (not from blockchain)
// - USDC contract balance from blockchain
// - Local dynamic pricing algorithms
const walletService = require('../wallet/wallet.service');
const usdcService = require('../usdc/usdc.service');
const contractService = require('../../shared/blockchain/contractService');
const validationUtils = require('../../shared/utils/validationUtils');
const { createLogger } = require('../../shared/utils/logger');

/**
 * Calculate exchange rate for MVT to USDC using liquidity pool formula
 * Formula: Rate = USDC Reserve / MVT Reserve
 * @returns {Promise<object>} - Exchange rate data
 */
async function calculateExchangeRate() {
  const logger = createLogger({}, { operation: 'calculateExchangeRate' });

  try {
    // Get USDC liquidity pool data from blockchain contract
    const usdcPool = await usdcService.getUSDCLiquidityPool();

    const usdcReserves = usdcPool?.availableBalance ?? 0;

    // Get central wallet balance (represents MVT reserves in the pool)
    logger.debug({
      operation: 'calculateExchangeRate',
      step: 'fetching_central_wallet'
    }, "Fetching central wallet balance");

    const centralWallet = await walletService.getCentralWalletBalance();

    logger.debug({
      operation: 'calculateExchangeRate',
      centralWallet: centralWallet
    }, "Central wallet data received");

    const mvtReserves = centralWallet?.balance ?? 0; // Current MVT balance in central wallet

    logger.info({
      operation: 'calculateExchangeRate',
      usdcReserves: usdcReserves,
      mvtReserves: mvtReserves,
      formula: 'Rate = USDC_Reserve / MVT_Reserve'
    }, `Exchange rate calculation using liquidity pool formula - USDC Reserves: ${usdcReserves}, MVT Reserves: ${mvtReserves}`);

    let exchangeRate = 0;
    let liquidityRatio = 0;

    // Apply liquidity pool formula: Rate = USDC Reserve / MVT Reserve
    if (mvtReserves > 0 && usdcReserves > 0) {
      exchangeRate = usdcReserves / mvtReserves;
      liquidityRatio = usdcReserves / mvtReserves;

      logger.info({
        operation: 'calculateExchangeRate',
        exchangeRate: exchangeRate,
        mvtReserves: mvtReserves,
        usdcReserves: usdcReserves
      }, `Calculated exchange rate: 1 MVT = ${exchangeRate} USDC`);
    } else if (usdcReserves === 0) {
      logger.warn({
        operation: 'calculateExchangeRate',
        reason: 'no_usdc_reserves',
        usdcReserves: usdcReserves
      }, "No USDC reserves available - swaps not possible");
      // exchangeRate remains 0
    } else if (mvtReserves === 0) {
      logger.warn({
        operation: 'calculateExchangeRate',
        reason: 'no_mvt_reserves',
        mvtReserves: mvtReserves,
        fallbackRate: 0.1
      }, "No MVT reserves available - using fallback rate");
      // If no MVT reserves, use a conservative fallback rate
      exchangeRate = 0.1; // Conservative rate when MVT reserves are low
    }

    // Apply safety buffer (2% reduction) to protect against slippage
    const finalRate = exchangeRate * 0.98;

    const result = {
      rate: parseFloat(finalRate.toFixed(6)),
      baseRate: parseFloat(exchangeRate.toFixed(6)),
      liquidityRatio: parseFloat(liquidityRatio.toFixed(6)),
      usdcReserves: Number(usdcReserves) || 0,
      mvtReserves: Number(mvtReserves) || 0,
      formula: "Rate = USDC_Reserve / MVT_Reserve",
      safetyBuffer: 0.02, // 2% safety buffer applied
      lastCalculated: new Date().toISOString()
    };

    // Validate result before returning
    if (typeof result.rate !== 'number' || isNaN(result.rate)) {
      logger.error({
        operation: 'calculateExchangeRate',
        result: result,
        reason: 'invalid_rate_calculated'
      }, "Invalid exchange rate calculated, using fallback");
      throw new Error("Invalid exchange rate calculated");
    }

    return result;
  } catch (error) {
    logger.error({
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      operation: 'calculateExchangeRate'
    }, "Error calculating exchange rate");

    const errorMessage = error.message || "";

    // Determine if this should use fallback or throw error
    if (errorMessage.includes("ResourceNotFoundException") ||
        errorMessage.includes("table does not exist") ||
        errorMessage.includes("No USDC reserves") ||
        errorMessage.includes("No MVT reserves")) {
      // These are data/liquidity issues that can use fallback
      logger.warn({
        operation: 'calculateExchangeRate',
        errorMessage: errorMessage,
        fallbackMode: true
      }, "Using fallback exchange rate due to data/liquidity issues");

      const fallbackResult = {
        rate: 0.5, // Conservative fallback rate
        baseRate: 0.5,
        liquidityRatio: 0.5,
        usdcReserves: 500.0, // Fallback USDC amount
        mvtReserves: 1000.0, // Fallback MVT reserves
        formula: "Rate = USDC_Reserve / MVT_Reserve (Fallback)",
        safetyBuffer: 0.02,
        lastCalculated: new Date().toISOString(),
        error: error.message,
        fallbackMode: true
      };

      logger.info({
        operation: 'calculateExchangeRate',
        fallbackResult: fallbackResult
      }, "Returning fallback exchange rate");
      return fallbackResult;
    } else {
      // Network errors, timeout errors, or other genuine service failures
      logger.error({
        operation: 'calculateExchangeRate',
        errorMessage: errorMessage,
        errorType: 'genuine_service_error'
      }, "Genuine service error in exchange rate calculation, throwing error");
      throw new Error(`Exchange rate calculation failed: ${errorMessage}`);
    }
  }
}

/**
 * Calculate USDC amount for given MVT amount
 * @param {number} mvtAmount - Amount of MVT tokens
 * @returns {Promise<object>} - Conversion data
 */
async function calculateUSDCAmount(mvtAmount) {
  try {
    // Validate MVT amount is integer
    const mvtValidation = validationUtils.validateMVTAmount(mvtAmount);
    if (!mvtValidation.isValid) {
      throw new Error(mvtValidation.error);
    }

    const exchangeData = await calculateExchangeRate();
    
    if (exchangeData.rate === 0) {
      throw new Error("No USDC liquidity available for swaps");
    }

    const usdcAmount = mvtAmount * exchangeData.rate;
    
    // Check if there's enough USDC in the pool
    if (usdcAmount > exchangeData.usdcReserves) {
      throw new Error(`Insufficient USDC liquidity. Available: ${exchangeData.usdcReserves}, Required: ${usdcAmount.toFixed(6)}`);
    }

    return {
      mvtAmount: mvtAmount,
      usdcAmount: parseFloat(usdcAmount.toFixed(6)),
      exchangeRate: exchangeData.rate,
      liquidityCheck: {
        sufficient: usdcAmount <= exchangeData.usdcReserves,
        available: exchangeData.usdcReserves,
        required: parseFloat(usdcAmount.toFixed(6))
      },
      calculatedAt: new Date().toISOString()
    };
  } catch (error) {
    const logger = createLogger({}, { operation: 'calculateUSDCAmount' });
    logger.error({
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      operation: 'calculateUSDCAmount'
    }, "Error calculating USDC amount");
    throw new Error(error.message || "Failed to calculate USDC amount");
  }
}

/**
 * Validate swap feasibility
 * @param {number} mvtAmount - Amount of MVT to swap
 * @param {string} userId - User ID
 * @returns {Promise<object>} - Validation result
 */
async function validateSwapFeasibility(mvtAmount, userId) {
  try {
    // First validate MVT amount is integer
    const mvtValidation = validationUtils.validateMVTAmount(mvtAmount);
    if (!mvtValidation.isValid) {
      return {
        isValid: false,
        error: mvtValidation.error,
        errorCode: "INVALID_MVT_AMOUNT"
      };
    }

    // Check user's available MVT balance (excluding locked tokens)
    const userBalance = await walletService.getUserBalance(userId);

    if (userBalance.availableBalance < mvtAmount) {
      return {
        isValid: false,
        error: `Insufficient available MVT balance. Available: ${userBalance.availableBalance}, Requested: ${mvtAmount}, Locked: ${userBalance.lockedBalance}`,
        errorCode: "INSUFFICIENT_MVT_BALANCE"
      };
    }

    // Calculate required USDC
    const conversionData = await calculateUSDCAmount(mvtAmount);
    
    if (!conversionData.liquidityCheck.sufficient) {
      return {
        isValid: false,
        error: `Insufficient USDC liquidity. Available: ${conversionData.liquidityCheck.available}, Required: ${conversionData.liquidityCheck.required}`,
        errorCode: "INSUFFICIENT_USDC_LIQUIDITY"
      };
    }

    return {
      isValid: true,
      conversionData: conversionData,
      userBalance: userBalance.balance,
      availableBalance: userBalance.availableBalance,
      lockedBalance: userBalance.lockedBalance
    };
  } catch (error) {
    const logger = createLogger({}, { operation: 'validateSwapFeasibility' });
    logger.error({
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      operation: 'validateSwapFeasibility'
    }, "Error validating swap feasibility");
    return {
      isValid: false,
      error: error.message || "Failed to validate swap feasibility",
      errorCode: "VALIDATION_ERROR"
    };
  }
}

/**
 * Get exchange rate summary for display
 * @returns {Promise<object>} - Exchange rate summary
 */
async function getExchangeRateSummary() {
  const logger = createLogger({}, { operation: 'getExchangeRateSummary' });

  try {
    logger.debug({
      operation: 'getExchangeRateSummary'
    }, "Starting exchange rate summary calculation");

    const exchangeData = await calculateExchangeRate();

    if (!exchangeData) {
      logger.error({
        operation: 'getExchangeRateSummary',
        reason: 'null_exchange_data'
      }, "calculateExchangeRate returned null/undefined");
      throw new Error("Exchange rate calculation returned no data");
    }

    logger.debug({
      operation: 'getExchangeRateSummary',
      exchangeData: exchangeData
    }, "Exchange rate calculation successful");

    // Ensure all required fields have valid values
    const currentRate = exchangeData.rate ?? 0;
    const usdcReserves = exchangeData.usdcReserves ?? 0;
    const mvtReserves = exchangeData.mvtReserves ?? 0;
    const liquidityRatio = exchangeData.liquidityRatio ?? 0;

    // Format the rate display to avoid scientific notation
    const formatRate = (rate) => {
      if (rate >= 1) {
        return rate.toFixed(2);
      } else if (rate >= 0.01) {
        return rate.toFixed(4);
      } else if (rate >= 0.0001) {
        return rate.toFixed(6);
      } else {
        return rate.toFixed(8);
      }
    };

    const result = {
      currentRate: parseFloat(currentRate.toFixed(8)), // Ensure no scientific notation
      rateDisplay: `1 MVT = ${formatRate(currentRate)} USDC`,
      liquidityStatus: {
        usdcReserves: usdcReserves,
        mvtSupply: mvtReserves, // Map mvtReserves to mvtSupply for schema compatibility
        liquidityRatio: liquidityRatio,
        status: currentRate > 0 ? "AVAILABLE" : "UNAVAILABLE"
      },
      lastUpdated: exchangeData.lastCalculated || new Date().toISOString()
    };

    logger.debug({
      operation: 'getExchangeRateSummary',
      result: result
    }, "Exchange rate summary result calculated");

    return result;
  } catch (error) {
    logger.error({
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      operation: 'getExchangeRateSummary'
    }, "Error getting exchange rate summary");

    // Determine if this is a recoverable error that should use fallback or a genuine error
    const errorMessage = error.message || "";

    // Check for specific error types that should use fallback vs throw errors
    if (errorMessage.includes("ResourceNotFoundException") ||
        errorMessage.includes("table does not exist") ||
        errorMessage.includes("liquidity") ||
        errorMessage.includes("reserves")) {
      // These are system/data issues that can use fallback
      logger.warn({
        operation: 'getExchangeRateSummary',
        errorMessage: errorMessage,
        fallbackMode: true
      }, "Using fallback exchange rate due to system/liquidity issues");

      const fallbackRate = 0.5;
      const fallbackResult = {
        currentRate: parseFloat(fallbackRate.toFixed(8)),
        rateDisplay: `1 MVT = ${fallbackRate.toFixed(2)} USDC (Fallback Rate)`,
        liquidityStatus: {
          usdcReserves: 500.0,
          mvtSupply: 1000.0,
          liquidityRatio: 0.5,
          status: "FALLBACK_MODE"
        },
        lastUpdated: new Date().toISOString()
      };

      logger.info({
        operation: 'getExchangeRateSummary',
        fallbackResult: fallbackResult
      }, "Returning fallback exchange rate summary");
      return fallbackResult;
    } else {
      // These are genuine errors that should be propagated
      logger.error({
        operation: 'getExchangeRateSummary',
        errorMessage: errorMessage,
        errorType: 'genuine_service_error'
      }, "Genuine error in exchange rate service, throwing error");
      throw new Error(`Exchange rate service error: ${errorMessage}`);
    }
  }
}

/**
 * Calculate minimum MVT amount for swap (to avoid dust transactions)
 * @returns {number} - Minimum MVT amount (integer)
 */
function getMinimumSwapAmount() {
  return 1; // Minimum 1 MVT token (integer)
}

/**
 * Calculate maximum MVT amount for swap based on available USDC
 * @returns {Promise<number>} - Maximum MVT amount (integer)
 */
async function getMaximumSwapAmount() {
  try {
    const exchangeData = await calculateExchangeRate();

    if (exchangeData.rate === 0) {
      return 0;
    }

    // Maximum based on available USDC reserves (floor to ensure integer)
    const maxFromLiquidity = exchangeData.usdcReserves / exchangeData.rate;

    return Math.floor(maxFromLiquidity); // Return integer MVT amount
  } catch (error) {
    const logger = createLogger({}, { operation: 'getMaximumSwapAmount' });
    logger.error({
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      operation: 'getMaximumSwapAmount'
    }, "Error calculating maximum swap amount");
    return 0;
  }
}

module.exports = {
  calculateExchangeRate,
  calculateUSDCAmount,
  validateSwapFeasibility,
  getExchangeRateSummary,
  getMinimumSwapAmount,
  getMaximumSwapAmount
};
