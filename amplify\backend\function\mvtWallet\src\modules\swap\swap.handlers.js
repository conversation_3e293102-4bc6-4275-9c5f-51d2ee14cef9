const swapService = require('./swap.service');
const authService = require('../../shared/services/authService');
const swapValidation = require('./swap.validation');
const { AuthorizationTypes, executeHandler, handleSwapRequestListAuthorization } = require('../../shared/utils/handlerUtils');
const responseUtils = require('../../shared/utils/responseUtils');
const { createLogger, logError } = require('../../shared/utils/logger');
const standardizedErrorUtils = require('../../shared/utils/standardizedErrorUtils');

/**
 * Handle requestMVTWalletSwap request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleRequestMVTWalletSwap(event, args) {
  try {
    // Get user authentication
    const currentUserDatabaseId = await authService.getCurrentUserDatabaseId(event);
    if (!currentUserDatabaseId) {
      return responseUtils.createUnauthorizedResponse("Authentication required");
    }

    // Validate input
    const validation = swapValidation.validateSwapRequestInput(args.input);
    if (!validation.isValid) {
      return responseUtils.createBadRequestResponse(validation.error);
    }

    const { mvtAmount, description } = args.input;

    const swapRequestData = await swapService.createSwapRequest(
      currentUserDatabaseId,
      mvtAmount,
      description
    );

    return responseUtils.createSuccessResponse(
      swapRequestData,
      `Swap request created successfully for ${mvtAmount} MVT tokens`
    );
  } catch (error) {
    console.error("Error in requestMVTWalletSwap:", error);
    return responseUtils.handleSwapError(error, "create", "requestMVTWalletSwap");
  }
}

/**
 * Handle getMVTWalletSwapRequests request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleGetMVTWalletSwapRequests(event, args) {
  try {
    const authResult = await handleSwapRequestListAuthorization(event, args);
    if (authResult.error) {
      return authResult.error;
    }

    const { userId, isAdmin } = authResult.context;
    const { limit = 50, excludeFailed = true } = args;

    const swapRequests = await swapService.getSwapRequestsList(userId, isAdmin, limit, excludeFailed);
    return responseUtils.createSuccessResponse(
      swapRequests,
      "Swap requests retrieved successfully"
    );
  } catch (error) {
    const logger = createLogger({}, { fieldName: 'getMVTWalletSwapRequests' });
    logError(logger, error, 'getMVTWalletSwapRequests');
    return responseUtils.handleServiceError(error, "Failed to retrieve swap requests");
  }
}

/**
 * Handle approveMVTWalletSwap request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleApproveMVTWalletSwap(event, args) {
  try {
    // Manual authorization check for better error handling
    const isAuthorized = await authService.checkAdminAuthorization(event);
    if (!isAuthorized) {
      return {
        statusCode: 403,
        message: 'Unauthorized: Admin access required for swap approval',
        data: null
      };
    }

    // Validate input
    const validation = swapValidation.validateSwapApprovalInput(args.input);
    if (!validation.isValid) {
      return {
        statusCode: 400,
        message: `Invalid input: ${validation.error}`,
        data: null
      };
    }

    const { swapRequestId } = args.input;
    const adminUserId = await authService.getCurrentUserDatabaseId(event);
    if (!adminUserId) {
      return {
        statusCode: 403,
        message: 'Admin user not found in database',
        data: null
      };
    }

    let approvalResult;
    try {
      approvalResult = await swapService.approveSwapRequest(swapRequestId, adminUserId);
      console.log('Swap approval service result:', JSON.stringify(approvalResult, null, 2));
    } catch (serviceError) {
      console.error('Swap approval service error:', serviceError);
      throw serviceError; // Re-throw to be caught by outer catch block
    }

    // Validate approval result structure
    if (!approvalResult) {
      throw new Error('Approval service returned null result');
    }

    // Handle both swapRequestId and id fields for compatibility
    const resultSwapRequestId = approvalResult.swapRequestId || approvalResult.id;
    if (!resultSwapRequestId) {
      throw new Error('Approval result missing swapRequestId or id');
    }

    // Construct response data with all required fields
    const responseData = {
      success: true,
      swapRequestId: resultSwapRequestId,
      mvtAmount: approvalResult.mvtAmount || 0,
      usdcAmount: approvalResult.usdcAmount || 0,
      userWalletAddress: approvalResult.userWalletAddress || '',
      transactionHash: approvalResult.transactionHash || '',
      newUserBalance: approvalResult.newUserBalance || 0,
      processedAt: approvalResult.approvedAt || approvalResult.processedAt || new Date().toISOString(),
      status: approvalResult.status || 'APPROVED'
    };

    console.log('Final response data:', JSON.stringify(responseData, null, 2));

    // Return standardized GraphQL success response with required fields
    const response = {
      statusCode: 200,
      message: `Swap request ${swapRequestId} approved successfully`,
      data: responseData
    };

    console.log('Complete handler response:', JSON.stringify(response, null, 2));
    return response;
  } catch (error) {
    console.error('Error in handleApproveMVTWalletSwap:', error);
    const logger = createLogger({}, { fieldName: 'approveMVTWalletSwap' });
    logError(logger, error, 'approveMVTWalletSwap');

    // Ensure we return a proper error response that matches the GraphQL schema
    const errorResponse = responseUtils.handleSwapError(error, "approve", "approveMVTWalletSwap");
    console.log('Error response:', JSON.stringify(errorResponse, null, 2));

    return errorResponse;
  }
}

/**
 * Handle rejectMVTWalletSwap request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleRejectMVTWalletSwap(event, args) {
  try {
    // Manual authorization check for better error handling
    const isAuthorized = await authService.checkAdminAuthorization(event);
    if (!isAuthorized) {
      return {
        statusCode: 403,
        message: 'Unauthorized: Admin access required for swap rejection',
        data: null
      };
    }

    // Validate input
    const validation = swapValidation.validateSwapRejectionInput(args.input);
    if (!validation.isValid) {
      return {
        statusCode: 400,
        message: `Invalid input: ${validation.error}`,
        data: null
      };
    }

    const { swapRequestId, rejectionReason } = args.input;
    const adminUserId = await authService.getCurrentUserDatabaseId(event);
    if (!adminUserId) {
      return {
        statusCode: 403,
        message: 'Admin user not found in database',
        data: null
      };
    }

    const rejectionResult = await swapService.rejectSwapRequest(
      swapRequestId,
      adminUserId,
      rejectionReason
    );

    // Return standardized GraphQL success response with required fields
    return {
      statusCode: 200,
      message: `Swap request ${swapRequestId} rejected successfully`,
      data: {
        success: true,
        swapRequestId: rejectionResult.swapRequestId || rejectionResult.id,
        mvtAmount: rejectionResult.mvtAmount,
        usdcAmount: rejectionResult.usdcAmount,
        userWalletAddress: rejectionResult.userWalletAddress,
        processedAt: rejectionResult.rejectedAt || rejectionResult.processedAt,
        status: rejectionResult.status,
        rejectionReason: rejectionResult.rejectionReason
      }
    };
  } catch (error) {
    const logger = createLogger({}, { fieldName: 'rejectMVTWalletSwap' });
    logError(logger, error, 'rejectMVTWalletSwap');
    return responseUtils.handleSwapError(error, "reject", "rejectMVTWalletSwap");
  }
}

/**
 * Handle recoverMVTWalletSwap request - Admin function to recover from partial swap failures
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleRecoverMVTWalletSwap(event, args) {
  try {
    // Manual authorization check for better error handling
    const isAuthorized = await authService.checkAdminAuthorization(event);
    if (!isAuthorized) {
      return {
        statusCode: 403,
        message: 'Unauthorized: Admin access required for swap recovery',
        data: null
      };
    }

    // Validate input
    const { swapRequestId, recoveryAction } = args.input;
    if (!swapRequestId) {
      return {
        statusCode: 400,
        message: 'swapRequestId is required',
        data: null
      };
    }

    if (!recoveryAction || !['COMPLETE_MVT_TRANSFER', 'REVERSE_USDC_TRANSFER'].includes(recoveryAction)) {
      return {
        statusCode: 400,
        message: 'recoveryAction must be either COMPLETE_MVT_TRANSFER or REVERSE_USDC_TRANSFER',
        data: null
      };
    }

    const adminUserId = await authService.getCurrentUserDatabaseId(event);
    if (!adminUserId) {
      return {
        statusCode: 403,
        message: 'Admin user not found in database',
        data: null
      };
    }

    const recoveryResult = await swapService.recoverPartialSwap(
      swapRequestId,
      adminUserId,
      recoveryAction
    );

    // Return standardized GraphQL success response
    return {
      statusCode: 200,
      message: `Swap request ${swapRequestId} recovery completed: ${recoveryAction}`,
      data: {
        success: true,
        swapRequestId: recoveryResult.swapRequestId,
        recoveryAction: recoveryResult.recoveryAction,
        status: recoveryResult.status,
        message: recoveryResult.message,
        manualActionRequired: recoveryResult.manualActionRequired || null,
        recoveredBy: recoveryResult.recoveredBy,
        recoveredAt: recoveryResult.recoveredAt
      }
    };
  } catch (error) {
    const logger = createLogger({}, { fieldName: 'recoverMVTWalletSwap' });
    logError(logger, error, 'recoverMVTWalletSwap');
    return responseUtils.handleSwapError(error, "recover", "recoverMVTWalletSwap");
  }
}

module.exports = {
  handleRequestMVTWalletSwap,
  handleGetMVTWalletSwapRequests,
  handleApproveMVTWalletSwap,
  handleRejectMVTWalletSwap,
  handleRecoverMVTWalletSwap
};
