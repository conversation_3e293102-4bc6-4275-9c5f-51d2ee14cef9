{"name": "mvtwallet", "version": "2.0.0", "description": "MVT Token Wallet Management Lambda function", "main": "index.js", "license": "Apache-2.0", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:wallet": "jest modules/wallet", "test:transaction": "jest modules/transaction", "test:usdc": "jest modules/usdc", "test:exchangeRate": "jest modules/exchangeRate", "test:swap": "jest modules/swap", "test:integration": "jest __tests__/integration", "test:scenarios": "jest __tests__/scenarios", "test:user-journey": "jest __tests__/scenarios/user-journey", "test:admin-workflow": "jest __tests__/scenarios/admin-workflow", "test:error-recovery": "jest __tests__/scenarios/error-recovery", "test:data-integrity": "jest __tests__/scenarios/data-integrity", "test:performance-load": "jest __tests__/scenarios/performance-load", "test:locked-balance": "jest __tests__/scenarios/locked-balance", "test:wallet-transaction": "jest __tests__/integration/wallet-transaction", "test:exchange-rate-usdc": "jest __tests__/integration/exchange-rate-usdc", "test:swap-wallet": "jest __tests__/integration/swap-wallet", "test:auth-validation": "jest __tests__/integration/auth-validation", "test:admin-usdc-transfer": "jest __tests__/scenarios/admin-usdc-transfer", "test:swap-approval": "jest __tests__/scenarios/swap-approval-process", "test:runner": "node run-tests.js"}, "dependencies": {"aws-sdk": "^2.1691.0", "axios": "^1.8.4", "dotenv": "^16.5.0", "ethers": "^6.13.5", "pino": "^8.17.2", "stripe": "^18.0.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/aws-lambda": "^8.10.92", "jest": "^29.7.0"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/__tests__/setup.js"], "collectCoverageFrom": ["modules/**/*.js", "shared/**/*.js", "!**/*.test.js", "!**/node_modules/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/__tests__/**/*.test.js"], "testTimeout": 2000, "maxWorkers": "50%", "verbose": true}}