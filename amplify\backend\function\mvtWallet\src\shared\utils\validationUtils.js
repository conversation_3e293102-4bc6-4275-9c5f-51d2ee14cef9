/**
 * Validate if a value is a positive number
 * @param {any} value - Value to validate
 * @returns {boolean} - True if valid positive number
 */
function isPositiveNumber(value) {
  return typeof value === 'number' && value > 0 && !isNaN(value);
}

/**
 * Validate if a value is a positive integer (whole number)
 * @param {any} value - Value to validate
 * @returns {boolean} - True if valid positive integer
 */
function isPositiveInteger(value) {
  return typeof value === 'number' && value > 0 && Number.isInteger(value) && !isNaN(value);
}

/**
 * Validate MVT amount (must be positive integer)
 * @param {any} amount - MVT amount to validate
 * @returns {object} - Validation result with isValid and error message
 */
function validateMVTAmount(amount) {
  if (!amount || typeof amount !== 'number') {
    return {
      isValid: false,
      error: "MVT amount is required and must be a number"
    };
  }

  if (amount <= 0) {
    return {
      isValid: false,
      error: "MVT amount must be greater than 0"
    };
  }

  if (!Number.isInteger(amount)) {
    return {
      isValid: false,
      error: "MVT amount must be a whole number (integer). Decimal amounts are not allowed."
    };
  }

  return { isValid: true };
}

/**
 * Validate USDC amount (can be float)
 * @param {any} amount - USDC amount to validate
 * @returns {object} - Validation result with isValid and error message
 */
function validateUSDCAmount(amount) {
  if (!isPositiveNumber(amount)) {
    return {
      isValid: false,
      error: "USDC amount must be a positive number greater than 0"
    };
  }

  return { isValid: true };
}

/**
 * Validate if a string is not empty
 * @param {any} value - Value to validate
 * @returns {boolean} - True if valid non-empty string
 */
function isNonEmptyString(value) {
  return typeof value === 'string' && value.trim().length > 0;
}

/**
 * Validate user ID format
 * @param {any} userId - User ID to validate
 * @returns {boolean} - True if valid user ID
 */
function isValidUserId(userId) {
  return isNonEmptyString(userId);
}

/**
 * Validate Ethereum wallet address format
 * @param {any} address - Wallet address to validate
 * @returns {boolean} - True if valid Ethereum address
 */
function isValidEthereumAddress(address) {
  if (!isNonEmptyString(address)) {
    return false;
  }
  // Ethereum address: 0x followed by 40 hexadecimal characters
  const ethereumAddressRegex = /^0x[a-fA-F0-9]{40}$/;
  return ethereumAddressRegex.test(address);
}

/**
 * Validate transaction amount
 * @param {any} amount - Amount to validate
 * @returns {object} - Validation result with isValid and error message
 */
function validateTransactionAmount(amount) {
  if (!isPositiveNumber(amount)) {
    return {
      isValid: false,
      error: "Amount must be a positive number greater than 0"
    };
  }
  return { isValid: true };
}

/**
 * Validate mint input (MVT amounts must be integers)
 * @param {object} input - Mint input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateMintInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { amount, description } = input;

  // Use strict MVT integer validation for minting
  const amountValidation = validateMVTAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate transfer input (MVT amounts must be integers)
 * @param {object} input - Transfer input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateTransferInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { userId, amount, description } = input;

  if (!isValidUserId(userId)) {
    return {
      isValid: false,
      error: "Valid user ID is required"
    };
  }

  // Use strict MVT integer validation for transfers
  const amountValidation = validateMVTAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate user-to-user transfer input (MVT amounts must be integers)
 * @param {object} input - User transfer input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateUserTransferInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { recipientUserId, amount, description } = input;

  if (!isValidUserId(recipientUserId)) {
    return {
      isValid: false,
      error: "Valid recipient user ID is required"
    };
  }

  // Use strict MVT integer validation for user transfers
  const amountValidation = validateMVTAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate USDC deposit input (USDC amounts can be floats)
 * @param {object} input - USDC deposit input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateUSDCDepositInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { amount, description } = input;

  // Use USDC amount validation (allows floats)
  const amountValidation = validateUSDCAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate USDC withdrawal input (USDC amounts can be floats)
 * @param {object} input - USDC withdrawal input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateUSDCWithdrawalInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { amount, description } = input;

  // Use USDC amount validation (allows floats)
  const amountValidation = validateUSDCAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate USDC transfer input (USDC amounts can be floats)
 * @param {object} input - USDC transfer input object
 * @returns {object} - Validation result with isValid and error message
 */
function validateUSDCTransferInput(input) {
  if (!input) {
    return {
      isValid: false,
      error: "Input is required"
    };
  }

  const { userId, amount, description } = input;

  // Validate userId
  if (!userId || !isValidUserId(userId)) {
    return {
      isValid: false,
      error: "Valid user ID is required"
    };
  }

  // Use USDC amount validation (allows floats)
  const amountValidation = validateUSDCAmount(amount);
  if (!amountValidation.isValid) {
    return amountValidation;
  }

  if (description && !isNonEmptyString(description)) {
    return {
      isValid: false,
      error: "Description must be a non-empty string if provided"
    };
  }

  return { isValid: true };
}

/**
 * Validate wallet address format and existence
 * @param {string} walletAddress - Wallet address to validate
 * @returns {object} - Validation result with isValid and error message
 */
function validateWalletAddress(walletAddress) {
  if (!walletAddress || !isNonEmptyString(walletAddress)) {
    return {
      isValid: false,
      error: "Please add a valid blockchain wallet address to your profile before requesting withdrawals",
      errorCode: "MISSING_WALLET_ADDRESS"
    };
  }

  if (!isValidEthereumAddress(walletAddress)) {
    return {
      isValid: false,
      error: "Invalid wallet address format. Please provide a valid Ethereum address (0x...)",
      errorCode: "INVALID_WALLET_ADDRESS_FORMAT"
    };
  }

  return { isValid: true };
}

module.exports = {
  isPositiveNumber,
  isPositiveInteger,
  isNonEmptyString,
  isValidUserId,
  isValidEthereumAddress,
  validateTransactionAmount,
  validateMVTAmount,
  validateUSDCAmount,
  validateMintInput,
  validateTransferInput,
  validateUserTransferInput,
  validateUSDCDepositInput,
  validateUSDCWithdrawalInput,
  validateUSDCTransferInput,
  validateWalletAddress
};
