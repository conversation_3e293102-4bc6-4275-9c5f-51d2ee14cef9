<!-- -------------------- Dashboard Separator -------------------- -->
<div class="menu-item">
  <div class="menu-content pt-8 pb-2">
    <span class="text-muted text-uppercase fs-8 ls-1"> Dashboard </span>
  </div>
</div>
<!-- -------------------- Default -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/dashboard']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen025"></app-svg-general>
      </span>
    </span>
    <span class="menu-title"> Home </span>
  </a>
</div>

<!-- -------------------- Community Separator -------------------- -->
<div class="menu-item">
  <div class="menu-content pt-8 pb-2">
    <span class="text-muted text-uppercase fs-8 ls-1"> Community </span>
  </div>
</div>
<!-- -------------------- Members -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/members']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <img alt="" style="height: 1.5rem !important;width: 1.5rem !important" src="./assets/media/logos/Layout.png"
          class="h-50 align-self-center" />
      </span>
    </span>
    <span class="menu-title"> Members </span>
  </a>
</div>
<!-- -------------------- Stakeholders -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/stakeholders']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-communication svg="com013"></app-svg-communication>
      </span>
    </span>
    <span class="menu-title"> Stakeholders </span>
  </a>
</div>

<!-- -------------------- Students -------------------- -->
<ng-container *ngIf="_sharedService?.userRole?.value === 'SUPER_ADMIN'">
  <div class="menu-item">
    <a class="menu-link without-sub" [routerLink]="['/students']" routerLinkActive="active">
      <span class="menu-icon">
        <span class="svg-icon svg-icon-2">
          <app-svg-communication svg="com013"></app-svg-communication>
        </span>
      </span>
      <span class="menu-title"> Students </span>
    </a>
  </div>
</ng-container>

<!-- -------------------- Schools -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/schools']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-files svg="fil025"></app-svg-files>
      </span>
    </span>
    <span class="menu-title"> Schools </span>
  </a>
</div>

<!-- -------------------- Associations -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/families']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-communication svg="com014"></app-svg-communication>
      </span>
    </span>
    <span class="menu-title"> Associations </span>
  </a>
</div>

<!-- -------------------- Events -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/events']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen014"></app-svg-general>
      </span>
    </span>
    <span class="menu-title"> Events </span>
  </a>
</div>

<!-- -------------------- Activities -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/logs']" (click)="clickOnActivity()" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-files svg="fil025"></app-svg-files>
      </span>
    </span>
    <span class="menu-title"> Logs </span>
  </a>
</div>


<!-- -------------------- Learning Separator -------------------- -->
<div class="menu-item">
  <div class="menu-content pt-8 pb-2">
    <span class="text-muted text-uppercase fs-8 ls-1"> Learning </span>
  </div>
</div>

<!---------------------- Projects ---------------------->

<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/project-overview']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-communication svg="com001"></app-svg-communication>
      </span>
    </span>
    <span class="menu-title"> Projects </span>
  </a>
</div>

<!-- -------------------- Knowledge -------------------- -->
<ng-container>
  <div class="menu-item">
    <a class="menu-link without-sub" (click)="clickOnFeedback()" routerLinkActive="active"
      [ngClass]="{'active': tasksActive.isActive || settingsActive?.isActive}">
      <span class="menu-icon">
        <span class="svg-icon svg-icon-2">
          <app-svg-general svg="gen022"></app-svg-general>
        </span>
      </span>
      <span class="menu-title"> Knowledge </span>
    </a>
    <a routerLink="/feedbacks" routerLinkActive #tasksActive="routerLinkActive" style="display: none" aria-label="Navigate to feedbacks"></a>
    <a routerLink="/feedback-dashboard" routerLinkActive #settingsActive="routerLinkActive" style="display: none" aria-label="Navigate to feedback dashboard"></a>
  </div>
</ng-container>


<!-- -------------------- Assignments -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/activities']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-files svg="fil025"></app-svg-files>
      </span>
    </span>
    <span class="menu-title"> Activities </span>
  </a>
</div>
<!-- -------------------- Submissions -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/activity-submission']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-ecommerce svg="ecm009"></app-svg-ecommerce>
      </span>
    </span>
    <span class="menu-title"> Submissions </span>
  </a>
</div>

<!-- -------------------- Assessments -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/assessment']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen005"></app-svg-general>
      </span>
    </span>
    <span class="menu-title"> Assessments </span>
  </a>
</div>

<!-- -------------------- Funding -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" (click)="clickOnFunding()" routerLinkActive="active"
    [ngClass]="{'active': fundingActive.isActive || transactionActive?.isActive}">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-ecommerce svg="ecm007"></app-svg-ecommerce>
      </span>
    </span>
    <span class="menu-title"> Funding </span>
  </a>
  <a routerLink="/funding-dashboard" routerLinkActive #fundingActive="routerLinkActive" style="display: none" aria-label="Navigate to funding dashboard"></a>
  <a routerLink="/transactions" routerLinkActive #transactionActive="routerLinkActive" style="display: none" aria-label="Navigate to transaction"></a>
</div>

<!-- -------------------- Admin Separator -------------------- -->
<div class="menu-item">
  <div class="menu-content pt-8 pb-2">
    <span class="text-muted text-uppercase fs-8 ls-1"> Admin </span>
  </div>
</div>
<!-- -------------------- Innovation Center -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/innovation-center']">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-technology svg="teh004"></app-svg-technology>
      </span>
    </span>
    <span class="menu-title"> Innovation Center </span>
  </a>
</div>
<!-- -------------------- Educational Products -------------------- -->
<div class="menu-item border-bottom-1" *ngIf="!isProduction">
  <a class="menu-link without-sub" [routerLink]="['/customers-sales']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-ecommerce svg="ecm002"></app-svg-ecommerce>
      </span>
    </span>
    <span class="menu-title"> MVP Box Deliveries </span>
  </a>
</div>
<!-- -------------------- Village Mathematics -------------------- -->
<div class="menu-item" *ngIf="!isProduction">
  <a class="menu-link without-sub" [routerLink]="['/village-mathematics']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-art svg="art009"></app-svg-art>
      </span>
    </span>
    <span class="menu-title"> Village Mathematics </span>
  </a>
</div>
<!-- -------------------- Communications -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/notifications']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-communication svg="com011"></app-svg-communication>
      </span>
    </span>
    <span class="menu-title"> Notifications </span>
  </a>
</div>
<!-- -------------------- Chatbot Analytics -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/chatbot-analytics']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen022"></app-svg-general>
      </span>
    </span>
    <span class="menu-title"> Chatbot Analytics </span>
  </a>
</div>
<!-- -------------------- Chat Village -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/chat-village']" routerLinkActive="active">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-communication svg="com007"></app-svg-communication>
      </span>
    </span>
    <span class="menu-title"> MyVillage Chats </span>
  </a>
</div>
<!-- -------------------- Settings Separator -------------------- -->
<div class="menu-item" *ngIf="!isProduction">
  <div class="menu-content pt-8 pb-2">
    <span class="text-muted text-uppercase fs-8 ls-1"> Settings </span>
  </div>
</div>
<!-- -------------------- User Permissions -------------------- -->
<div class="menu-item" *ngIf="!isProduction">
  <a class="menu-link without-sub" [routerLink]="['/']">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen051"></app-svg-general>
      </span>
    </span>
    <span class="menu-title"> User Permissions </span>
  </a>
</div>
<!-- -------------------- Account Settings -------------------- -->
<div class="menu-item">
  <a class="menu-link without-sub" [routerLink]="['/account-settings']">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen019"></app-svg-general>
      </span>
    </span>
    <span class="menu-title"> Account Settings </span>
  </a>
</div>
<!-- -------------------- Notification Tester -------------------- -->
<div class="menu-item" *ngIf="isAmplify">
  <a class="menu-link without-sub" [routerLink]="['/send-notifications']">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen021"></app-svg-general>
      </span>
    </span>
    <span class="menu-title"> Check Notifications </span>
  </a>
</div>

<!-- -------------------- Separator -------------------- -->
<div class="menu-item" *ngIf="!isProduction">
  <div class="menu-content">
    <div class="separator mx-1 my-4"></div>
  </div>
</div>

<!-- -------------------- Support Center -------------------- -->
<div class="menu-item" *ngIf="!isProduction">
  <a class="menu-link without-sub" [routerLink]="['/support-center']">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-graphs svg="gra006"></app-svg-graphs>
      </span>
    </span>
    <span class="menu-title"> Support Center </span>
  </a>
</div>
<!-- -------------------- Changelog -------------------- -->
<div class="menu-item" *ngIf="!isProduction">
  <a class="menu-link" [routerLink]="['/']">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-coding svg="cod003"></app-svg-coding>
      </span>
    </span>
    <span class="menu-title"> Changelog </span>
  </a>
</div>
<!-- -------------------- Chatbot Prompts -------------------- -->
<div class="menu-item" *ngIf="!isProduction">
  <a class="menu-link" [routerLink]="['/chatbot-prompts']">
    <span class="menu-icon">
      <span class="svg-icon svg-icon-2">
        <app-svg-communication svg="com007"></app-svg-communication>
      </span>
    </span>
    <span class="menu-title"> Chatbot Prompts </span>
  </a>
</div>