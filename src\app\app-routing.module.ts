import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AuthGuard } from './shared/guards/auth.guard.service';
import { UnauthGuard } from './shared/guards/unauth.guard.service';
import { PrivacyPolicyComponent } from './modules/privacy-policy/privacy-policy.component';
import { TermsComponent } from './modules/terms/terms.component';
import { CryptoOnrampComponent } from './pages/payment/crypto-onramp/crypto-onramp.component';
import { PaymentSuccessComponent } from './pages/payment/payment-success/payment-success.component';
import { PaymentFailedComponent } from './pages/payment/payment-failed/payment-failed.component';
import { ChatbotPromptsListComponent } from './pages/chatbot-prompts-list/chatbot-prompts-list.component';

export const routes: Routes = [
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth.module').then((m) => m.AuthModule),
    canActivate: [UnauthGuard],
  },
  {
    path: '',
    loadChildren: () =>
      import('./_metronic/layout/layout.module').then((m) => m.LayoutModule),
    canActivate: [AuthGuard],
  },
  {
    path: 'error',
    loadChildren: () =>
      import('./modules/errors/errors.module').then((m) => m.ErrorsModule),
  },
  {
    path: 'privacy-policy',
    component: PrivacyPolicyComponent,
  },
  {
    path: 'terms',
    component: TermsComponent,
  },
  {
    path: 'crypto-onramp',
    component: CryptoOnrampComponent,
    data: { public: true }
  },
  {
    path: 'crypto-onramp/success',
    component: PaymentSuccessComponent,
    data: { public: true }
  },
  {
    path: 'crypto-onramp/failed',
    component: PaymentFailedComponent,
    data: { public: true }
  },
  { path: '**', redirectTo: 'error/404' },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'enabled',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
