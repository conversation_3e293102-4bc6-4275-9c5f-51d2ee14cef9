import { Component, HostListener } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';

import { filter, map, Subject } from 'rxjs';
import { AuthService } from '../app/auth/services/auth.service';
@Component({
  selector: 'body[root]',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  userActivity: any;

  userInactive: Subject<any> = new Subject();
  constructor(
    private readonly router: Router,
    private readonly titleService: Title,
    private readonly activatedRoute: ActivatedRoute,
    private readonly authService: AuthService,
    private readonly toastr: ToastrService,
    private readonly spinner: NgxSpinnerService,
    private readonly modalService: NgbModal
  ) {
    this.setTitle();
    this.setTimeout();
    this.userInactive.subscribe(() => {
      if (!this.router.url.includes('/auth')) {
        this.spinner.show();
        this.modalService.dismissAll();
        this.toastr.error('Session has been expired!');
        this.authService.signOut().then(() => {
          localStorage.clear();
          this.router.navigate(['auth/signin']);
          this.spinner.hide();
        });
      }
    });
  }

  setTimeout() {
    this.userActivity = setTimeout(
      () => this.userInactive.next(undefined),
      1800000
    );
  }

  @HostListener('window:mousemove')
  @HostListener('window:keyup')
  @HostListener('window:keydown')
  @HostListener('window:keypress')
  refreshUserState() {
    clearTimeout(this.userActivity);
    this.setTimeout();
  }

  private setTitle() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => {
          let child: any = this.activatedRoute.firstChild;
          while (child.firstChild) {
            child = child.firstChild;
          }

          if (child.snapshot.data['title']) {
            return child.snapshot.data['title'];
          }

          return this.titleService.getTitle();
        })
      )
      .subscribe((title: string) => {
        if (this.router.url === '/stakeholders') {
          title = 'Stakeholders'
        }
        this.titleService.setTitle(title);
      });
  }
}
