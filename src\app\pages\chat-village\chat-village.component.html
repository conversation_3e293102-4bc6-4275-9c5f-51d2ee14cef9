<app-toolbar class="toolbar"></app-toolbar>
<div class="content flex-column-fluid" id="kt_content">
  <!--begin::Layout-->
  <div class="d-flex flex-column flex-lg-row">
    <!--begin::Sidebar-->
    <div class="flex-column flex-lg-row-auto w-100 w-lg-300px w-xl-400px mb-10 mb-lg-0">

      <!--begin::Contacts-->
      <div class="card card-flush">
        <!--begin::Card header-->
        <ngx-spinner name="cv-side-spinner" [fullScreen]="false" type="ball-clip-rotate" bdColor="white" size="medium"
          color="#354029"></ngx-spinner>
        <div class="card-header pt-7" id="kt_chat_contacts_header h-280px">
          <a (click)="refreshUserList(true)" class="btn btn-primary fw-bold w-100 mb-8">New
            Message</a>
          <!--begin::Form-->
          <form class="w-100 position-relative" autocomplete="off">
            <!--begin::Icon-->
            <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
              <app-svg-general svg="gen021"></app-svg-general>
            </span> <!--end::Icon-->

            <!--begin::Input-->
            <input type="text" class="form-control form-control-solid px-13" name="search"
              placeholder="Search by name..." autocomplete="new-password" (keyup)="applyFilter()"
              [formControl]="searchBar">
            <!--end::Input-->
          </form>
          <!--end::Form-->
        </div>
        <!--end::Card header-->

        <!--begin::Card body-->
        <div class="card-body pt-5" id="kt_chat_contacts_body">
          <!--begin::List-->
          <div class="scroll-y me-n5 pe-5 h-200px h-lg-auto" data-kt-scroll="true"
            data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto"
            data-kt-scroll-dependencies="#kt_header, #kt_app_header, #kt_toolbar, #kt_app_toolbar, #kt_footer, #kt_app_footer, #kt_chat_contacts_header"
            data-kt-scroll-wrappers="#kt_content, #kt_app_content, #kt_chat_contacts_body" data-kt-scroll-offset="5px"
            style="max-height: 280px;">

            <!--begin::User-->
            <div class="d-flex flex-stack py-4 cursor-pointer" (click)="channelChange(item)"
              (keydown.enter)="channelChange(item)" *ngFor="let item of channelList| filter:filters;let index">
              <!--begin::Details-->
              <div class="d-flex align-items-center">
                <!--begin::Avatar-->
                <div class="symbol symbol-45px symbol-circle">
                  <img class="cursor-pointer" *ngIf="!!item?.imagePreview" [src]="item?.imagePreview"
                    (error)="sharedService.onImgError($event)" alt="Organization">
                  <span class="symbol-label fs-2x fw-bold text-primary bg-light-primary" *ngIf="!item?.imagePreview">
                    {{ (item?.givenName?.trim().charAt(0) | titlecase) || "M" }}
                  </span>
                </div>
                <!--end::Avatar-->
                <!--begin::Details-->
                <div class="ms-4">
                  <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">{{item?.givenName + '
                    '+item?.familyName}}</a>
                  <div class="fw-semibold text-muted last-message">{{item?.content||""}}</div>
                </div>
                <!--end::Details-->
              </div>
              <!--end::Details-->

              <!--begin::Lat seen-->
              <div class="d-flex flex-column align-items-end ms-2">
                <span class="text-muted fs-9 mb-1">{{getDateDifference(item.createdAt) || ''}}</span>
                <span class="text-muted fs-7 mb-1 notification-count" *ngIf="item?.count>0">{{item?.count}}</span>
              </div>
              <!--end::Lat seen-->
            </div>
            <!--end::User-->

            <!--begin::Separator-->
            <div class="separator separator-dashed d-none"></div>
            <!--end::Separator-->
            <!--end::User-->
          </div>
          <!--end::List-->
        </div>
        <!--end::Card body-->
      </div>
      <!--end::Contacts-->
    </div>
    <!--end::Sidebar-->

    <!--begin::Content-->
    <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">

      <ng-container *ngIf="showNewMessage && !initCard">

        <div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
          <!--begin::Card-->
          <div class="card">
            <div class="card-header align-items-center">
              <div class="card-title">
                <h2>Compose Message</h2>
              </div>
            </div>

            <div class="card-body p-0">
              <!--begin::Form-->
              <form id="kt_inbox_compose_form " [formGroup]="notificationForm">
                <!--begin::Body-->
                <div class="d-block">
                  <!--begin::To-->
                  <div class="d-flex align-items-center border-bottom px-8 min-h-50px">
                    <!--begin::Label-->
                    <div class="text-dark fw-bold w-75px">
                      To:
                    </div>
                    <!--end::Label-->
                    <div class="example-chip-list">
                      <ng-select [compareWith]="compareWith" labelForId="relations" bindLabel="name"
                        (change)="selectedRecepient($event)" formControlName='recepientId'>
                        <ng-option *ngFor="let list of allUsers" [value]="list?.id">
                          {{ list?.name | titlecase }}
                        </ng-option>
                      </ng-select>
                    </div>

                  </div>
                  <!--end::To-->

                  <!--begin::CC-->
                  <div class="d-none align-items-center border-bottom ps-8 pe-5 min-h-50px" data-kt-inbox-form="cc">
                    <!--begin::Label-->
                    <div class="text-dark fw-bold w-75px">
                      Cc:
                    </div>
                    <!--end::Label-->
                    <!--begin::Close-->
                    <span class="btn btn-clean btn-xs btn-icon" data-kt-inbox-form="cc_close">
                      <i class="la la-close "></i>
                    </span>
                    <!--end::Close-->
                  </div>
                  <!--end::CC-->

                  <!--begin::BCC-->
                  <div class="d-none align-items-center border-bottom inbox-to-bcc ps-8 pe-5 min-h-50px"
                    data-kt-inbox-form="bcc">
                    <!--begin::Label-->
                    <div class="text-dark fw-bold w-75px">
                      Bcc:
                    </div>
                    <!--end::Label-->

                    <!--begin::Input-->
                    <input type="text" class="form-control form-control-transparent border-0" name="compose_bcc"
                      value="" data-kt-inbox-form="tagify" tabindex="-1">
                    <!--end::Input-->

                    <!--begin::Close-->
                    <span class="btn btn-clean btn-xs btn-icon" data-kt-inbox-form="bcc_close">
                      <i class="la la-close "></i>
                    </span>
                    <!--end::Close-->
                  </div>
                  <!--end::BCC-->

                  <!--begin::Subject-->
                  <textarea placeholder="Type your text here..." rows="8" class="textarea-custom"
                    formControlName="body"></textarea>
                  <!--end::Subject-->
                </div>
                <!--end::Body-->

                <!--begin::Footer-->
                <div class="d-flex flex-stack flex-wrap gap-2 py-5 ps-8 pe-5 border-top">
                  <!--begin::Actions-->
                  <div class="d-flex align-items-center me-3">
                    <!--begin::Send-->
                    <div class="btn-group me-4">
                      <!--begin::Submit-->
                      <span class="btn btn-primary fs-bold px-6" data-kt-inbox-form="send" (click)="composeMessage()"
                        (keydown.enter)="composeMessage()">
                        <span class="indicator-label">
                          Send
                        </span>
                        <span class="indicator-progress">
                          Please wait... <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                        </span>
                      </span>
                      <!--end::Submit-->
                    </div>
                    <!--end::Send-->


                    <!--begin::Pin-->
                    <span class="btn btn-icon btn-sm btn-clean btn-active-light-primary">
                      <!--begin::Svg Icon | path: icons/duotune/general/gen018.svg-->
                      <span class="svg-icon svg-icon-2 m-0"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path opacity="0.3"
                            d="M18.0624 15.3453L13.1624 20.7453C12.5624 21.4453 11.5624 21.4453 10.9624 20.7453L6.06242 15.3453C4.56242 13.6453 3.76242 11.4453 4.06242 8.94534C4.56242 5.34534 7.46242 2.44534 11.0624 2.04534C15.8624 1.54534 19.9624 5.24534 19.9624 9.94534C20.0624 12.0453 19.2624 13.9453 18.0624 15.3453Z"
                            fill="currentColor"></path>
                          <path
                            d="M12.0624 13.0453C13.7193 13.0453 15.0624 11.7022 15.0624 10.0453C15.0624 8.38849 13.7193 7.04535 12.0624 7.04535C10.4056 7.04535 9.06241 8.38849 9.06241 10.0453C9.06241 11.7022 10.4056 13.0453 12.0624 13.0453Z"
                            fill="currentColor"></path>
                        </svg>
                      </span>
                      <!--end::Svg Icon--> </span>
                    <!--end::Pin-->
                  </div>
                  <!--end::Actions-->

                  <!--begin::Toolbar-->
                  <div class="d-flex align-items-center">
                    <!--begin::More actions-->
                    <span class="btn btn-icon btn-sm btn-clean btn-active-light-primary me-2" data-toggle="tooltip"
                      title="More actions">
                      <!--begin::Svg Icon | path: icons/duotune/coding/cod001.svg-->
                      <span class="svg-icon svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path opacity="0.3"
                            d="M22.1 11.5V12.6C22.1 13.2 21.7 13.6 21.2 13.7L19.9 13.9C19.7 14.7 19.4 15.5 18.9 16.2L19.7 17.2999C20 17.6999 20 18.3999 19.6 18.7999L18.8 19.6C18.4 20 17.8 20 17.3 19.7L16.2 18.9C15.5 19.3 14.7 19.7 13.9 19.9L13.7 21.2C13.6 21.7 13.1 22.1 12.6 22.1H11.5C10.9 22.1 10.5 21.7 10.4 21.2L10.2 19.9C9.4 19.7 8.6 19.4 7.9 18.9L6.8 19.7C6.4 20 5.7 20 5.3 19.6L4.5 18.7999C4.1 18.3999 4.1 17.7999 4.4 17.2999L5.2 16.2C4.8 15.5 4.4 14.7 4.2 13.9L2.9 13.7C2.4 13.6 2 13.1 2 12.6V11.5C2 10.9 2.4 10.5 2.9 10.4L4.2 10.2C4.4 9.39995 4.7 8.60002 5.2 7.90002L4.4 6.79993C4.1 6.39993 4.1 5.69993 4.5 5.29993L5.3 4.5C5.7 4.1 6.3 4.10002 6.8 4.40002L7.9 5.19995C8.6 4.79995 9.4 4.39995 10.2 4.19995L10.4 2.90002C10.5 2.40002 11 2 11.5 2H12.6C13.2 2 13.6 2.40002 13.7 2.90002L13.9 4.19995C14.7 4.39995 15.5 4.69995 16.2 5.19995L17.3 4.40002C17.7 4.10002 18.4 4.1 18.8 4.5L19.6 5.29993C20 5.69993 20 6.29993 19.7 6.79993L18.9 7.90002C19.3 8.60002 19.7 9.39995 19.9 10.2L21.2 10.4C21.7 10.5 22.1 11 22.1 11.5ZM12.1 8.59998C10.2 8.59998 8.6 10.2 8.6 12.1C8.6 14 10.2 15.6 12.1 15.6C14 15.6 15.6 14 15.6 12.1C15.6 10.2 14 8.59998 12.1 8.59998Z"
                            fill="currentColor"></path>
                          <path
                            d="M17.1 12.1C17.1 14.9 14.9 17.1 12.1 17.1C9.30001 17.1 7.10001 14.9 7.10001 12.1C7.10001 9.29998 9.30001 7.09998 12.1 7.09998C14.9 7.09998 17.1 9.29998 17.1 12.1ZM12.1 10.1C11 10.1 10.1 11 10.1 12.1C10.1 13.2 11 14.1 12.1 14.1C13.2 14.1 14.1 13.2 14.1 12.1C14.1 11 13.2 10.1 12.1 10.1Z"
                            fill="currentColor"></path>
                        </svg>
                      </span>
                      <!--end::Svg Icon--> </span>
                    <!--end::More actions-->

                    <!--begin::Dismiss reply-->
                    <span class="btn btn-icon btn-sm btn-clean btn-active-light-primary" data-inbox="dismiss"
                      data-toggle="tooltip" title="Dismiss reply">
                      <!--begin::Svg Icon | path: icons/duotune/general/gen027.svg-->
                      <span class="svg-icon svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z"
                            fill="currentColor"></path>
                          <path opacity="0.5"
                            d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z"
                            fill="currentColor"></path>
                          <path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z"
                            fill="currentColor"></path>
                        </svg>
                      </span>
                      <!--end::Svg Icon--> </span>
                    <!--end::Dismiss reply-->
                  </div>
                  <!--end::Toolbar-->
                </div>
                <!--end::Footer-->
              </form>
              <!--end::Form-->
            </div>
          </div>
          <!--end::Card-->

        </div>
      </ng-container>

      <ng-container *ngIf="initCard">
        <div class="card custom-card">
          <h3>Please select a user to start chating!</h3>
        </div>
      </ng-container>

      <!-- Show message if user not selected for chatbot -->
      <ng-container *ngIf="!selectedChannel && !showNewMessage && !initCard">
        <div class="card custom-card chatbot-card mt-4 mb-4 border border-info shadow-sm">
          <div class="card-body text-center">
            <i class="bi bi-robot display-3 text-info mb-2"></i>
            <h3 class="text-info">Chatbot</h3>
            <p class="lead">Please select a user for chatbot to continue.</p>
          </div>
        </div>
      </ng-container>

      <!--begin::Messenger-->
      <div class="card" id="kt_chat_messenger" *ngIf="!showNewMessage && !initCard">
        <!--begin::Card header-->
        <div class="card-header" id="kt_chat_messenger_header">
          <!--begin::Title-->
          <div class="card-title">
            <!--begin::User-->
            <div class="d-flex justify-content-center flex-column me-3">
              <a class="fs-4 fw-bold text-gray-900 text-hover-primary me-1 mb-2 lh-1">{{((selectedChannel?.givenName +'
                '+selectedChannel?.familyName)|
                titlecase)||""}}</a>

              <!--begin::Info-->
              <div class="mb-0 lh-1">
                <span class="badge badge-success badge-circle w-10px h-10px me-1"></span>
                <span class="fs-7 fw-semibold text-muted">Active</span>
              </div>
              <!--end::Info-->
            </div>
            <!--end::User-->
          </div>
          <!--end::Title-->

          <!--begin::Card toolbar-->
          <div class="card-toolbar">
            <!--begin::Menu-->
            <div class="me-n3">
              <button class="btn" data-kt-menu-trigger="click" (click)="clearChat()"
                data-kt-menu-placement="bottom-end">
                Clear Chat
              </button>

              <!--begin::Menu 3-->
              <div
                class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px py-3"
                data-kt-menu="true">
                <!--begin::Heading-->
                <div class="menu-item px-3">
                  <div class="menu-content text-muted pb-2 px-3 fs-7 text-uppercase">
                    Contacts
                  </div>
                </div>
                <!--end::Heading-->

                <!--begin::Menu item-->
                <div class="menu-item px-3">
                  <a class="menu-link px-3" data-bs-toggle="modal" data-bs-target="#kt_modal_users_search">
                    Add Contact
                  </a>
                </div>
                <!--end::Menu item-->

                <!--begin::Menu item-->
                <div class="menu-item px-3">
                  <a class="menu-link flex-stack px-3" data-bs-toggle="modal" data-bs-target="#kt_modal_invite_friends">
                    Invite Contacts

                    <span class="ms-2" data-bs-toggle="tooltip"
                      aria-label="Specify a contact email to send an invitation"
                      data-bs-original-title="Specify a contact email to send an invitation" data-kt-initialized="1">
                      <i class="ki-duotone ki-information fs-7"><span class="path1"></span><span
                          class="path2"></span><span class="path3"></span></i> </span>
                  </a>
                </div>
                <!--end::Menu item-->

                <!--begin::Menu item-->
                <div class="menu-item px-3" data-kt-menu-trigger="hover" data-kt-menu-placement="right-start">
                  <a class="menu-link px-3">
                    <span class="menu-title">Groups</span>
                    <span class="menu-arrow"></span>
                  </a>

                  <!--begin::Menu sub-->
                  <div class="menu-sub menu-sub-dropdown w-175px py-4">
                    <!--begin::Menu item-->
                    <div class="menu-item px-3">
                      <a class="menu-link px-3" data-bs-toggle="tooltip" data-bs-original-title="Coming soon"
                        data-kt-initialized="1">
                        Create Group
                      </a>
                    </div>
                    <!--end::Menu item-->

                    <!--begin::Menu item-->
                    <div class="menu-item px-3">
                      <a class="menu-link px-3" data-bs-toggle="tooltip" data-bs-original-title="Coming soon"
                        data-kt-initialized="1">
                        Invite Members
                      </a>
                    </div>
                    <!--end::Menu item-->

                    <!--begin::Menu item-->
                    <div class="menu-item px-3">
                      <a class="menu-link px-3" data-bs-toggle="tooltip" data-bs-original-title="Coming soon"
                        data-kt-initialized="1">
                        Settings
                      </a>
                    </div>
                    <!--end::Menu item-->
                  </div>
                  <!--end::Menu sub-->
                </div>
                <!--end::Menu item-->

                <!--begin::Menu item-->
                <div class="menu-item px-3 my-1">
                  <a class="menu-link px-3" data-bs-toggle="tooltip" data-bs-original-title="Coming soon"
                    data-kt-initialized="1">
                    Settings
                  </a>
                </div>
                <!--end::Menu item-->
              </div>
              <!--end::Menu 3-->
            </div>
            <!--end::Menu-->
          </div>
          <!--end::Card toolbar-->
        </div>
        <!--end::Card header-->

        <!--begin::Card body-->
        <div class="card-body" id="kt_chat_messenger_body">
          <!-- spinner : Start -->
          <ngx-spinner name="chat_village_spinner" [fullScreen]="false" type="ball-clip-rotate" bdColor="#f5f8fa"
            size="medium" color="#354029"></ngx-spinner>
          <!-- spinner: End -->
          <!--begin::Messages-->
          <div class="scroll-y me-n5 pe-5 h-400px" data-kt-element="messages" data-kt-scroll="true"
            id="kt_scroll_change_pos" data-kt-scroll-activate="{default: false, lg: true}"
            data-kt-scroll-max-height="auto"
            data-kt-scroll-dependencies="#kt_header, #kt_app_header, #kt_app_toolbar, #kt_toolbar, #kt_footer, #kt_app_footer, #kt_chat_messenger_header, #kt_chat_messenger_footer"
            data-kt-scroll-wrappers="#kt_content, #kt_app_content, #kt_chat_messenger_body" data-kt-scroll-offset="5px"
            style="max-height: 400px;">


            <div *ngFor="let item of messagePipeline">
              <!--begin::Message(out)-->
              <div class="d-flex justify-content-start mb-10 " *ngIf="item.messageBy==='USER'">
                <!--begin::Wrapper-->
                <div class="d-flex flex-column align-items-start">
                  <!--begin::User-->
                  <div class="d-flex align-items-center mb-2">
                    <!--begin::Avatar-->
                    <div class="symbol symbol-45px symbol-circle">
                      <img class="cursor-pointer" *ngIf="!!item?.imagePreview" [src]="item?.imagePreview"
                        (error)="sharedService.onImgError($event)" alt="Profile">
                      <span class="symbol-label fs-2x fw-bold text-primary bg-light-primary"
                        *ngIf="!item?.imagePreview">
                        {{ (item?.givenName?.trim().charAt(0) | titlecase) || "M" }}
                      </span>
                    </div>
                    <!--end::Avatar-->
                    <!--begin::Details-->
                    <div class="ms-3">
                      <a class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">{{item?.givenName}}</a>
                      <span class="text-muted fs-7 mb-1">{{formatDateString(item?.createdAt)}}</span>
                    </div>
                    <!--end::Details-->

                  </div>
                  <!--end::User-->

                  <!--begin::Text-->
                  <div class="p-5 rounded bg-light-info text-gray-900 fw-semibold mw-lg-400px text-start capitalize"
                    data-kt-element="message-text">
                    {{item.content}} </div>
                  <!--end::Text-->
                  <small class="text-muted mt-2" *ngIf="item?.seenByAdminName?.length>0||false">Seen by
                    <b>{{item?.seenByAdminName|titlecase}}</b></small>
                </div>
                <!--end::Wrapper-->
              </div>
              <!--end::Message(out)-->

              <!--begin::Message(in)-->
              <div class="d-flex justify-content-end mb-10 " *ngIf="item.messageBy==='SUPERADMIN'">
                <!--begin::Wrapper-->
                <div class="d-flex flex-column align-items-end">
                  <!--begin::User-->
                  <div class="d-flex align-items-center mb-2">
                    <!--begin::Details-->
                    <div class="me-3">
                      <span class="text-muted fs-7 mb-1">{{formatDateString(item?.createdAt)}}</span>
                      <a class="fs-5 fw-bold text-gray-900 text-hover-primary ms-1">MyVillage Project Support</a>
                    </div>
                    <!--end::Details-->

                    <!--begin::Avatar-->
                    <div class="symbol symbol-45px symbol-circle">
                      <img class="cursor-pointer" *ngIf="!!item?.imagePreview" [src]="item?.imagePreview"
                        (error)="sharedService.onImgError($event)" alt="Profile">
                      <span class="symbol-label fs-2x fw-bold text-primary bg-light-primary"
                        *ngIf="!item?.imagePreview">
                        {{ (item?.givenName?.trim().charAt(0) | titlecase) || "M" }}
                      </span>
                    </div>
                    <!--end::Avatar-->
                  </div>
                  <!--end::User-->

                  <!--begin::Text-->
                  <div class="p-5 rounded bg-light-primary text-gray-900 fw-semibold mw-lg-400px text-end capitalize"
                    data-kt-element="message-text">
                    {{item.content}} </div>
                  <!--end::Text-->
                </div>
                <!--end::Wrapper-->
              </div>
              <!--end::Message(in)-->
            </div>
          </div>
          <!--end::Messages-->
        </div>
        <!--end::Card body-->

        <!--begin::Card footer-->
        <div class="card-footer pt-4" id="kt_chat_messenger_footer">
          <!--begin::Input-->
          <textarea class="form-control form-control-flush mb-3" rows="1" [formControl]="chatBox"
            placeholder="Type a message" (keydown.enter)="sendMessage()">
        </textarea>
          <!--end::Input-->

          <!--begin:Toolbar-->
          <div class="d-flex justify-content-end">
            <!--begin::Send-->
            <button class="btn btn-primary" type="button" data-kt-element="send" (click)="sendMessage()">Send</button>
            <!--end::Send-->
          </div>
          <!--end::Toolbar-->
        </div>
        <!--end::Card footer-->
      </div>
      <!--end::Messenger-->
    </div>
    <!--end::Content-->
  </div>
  <!--end::Layout-->
  <!--begin::Modals-->

  <!--begin::Modal - View Users-->
  <div class="modal fade" id="kt_modal_view_users" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog mw-650px">
      <!--begin::Modal content-->
      <div class="modal-content">
        <!--begin::Modal header-->
        <div class="modal-header pb-0 border-0 justify-content-end">
          <!--begin::Close-->
          <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
            <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
          </div>
          <!--end::Close-->
        </div>
        <!--begin::Modal header-->

        <!--begin::Modal body-->
        <div class="modal-body scroll-y mx-5 mx-xl-18 pt-0 pb-15">
          <!--begin::Heading-->
          <div class="text-center mb-13">
            <!--begin::Title-->
            <h1 class="mb-3">Browse Users</h1>
            <!--end::Title-->

            <!--begin::Description-->
            <div class="text-muted fw-semibold fs-5">
              If you need more info, please check out our
              <a class="link-primary fw-bold">Users Directory</a>.
            </div>
            <!--end::Description-->
          </div>
          <!--end::Heading-->

          <!--begin::Users-->
          <div class="mb-15">
            <!--begin::List-->
            <div class="mh-375px scroll-y me-n7 pe-7">
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <img alt="Pic" src="../../../../../assets/media/avatars/300-6.jpg">
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Emma Smith

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Art Director </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">smith&#64;kpmg.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$23,000</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <span class="symbol-label bg-light-danger text-danger fw-semibold">
                      M </span>
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Melody Macy

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Marketing Analytic </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">melody&#64;altbox.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$50,500</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <img alt="Pic" src="../../../../../assets/media/avatars/300-1.jpg">
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Max Smith

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Software Enginer </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">max&#64;kt.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$75,900</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <img alt="Pic" src="../../../../../assets/media/avatars/300-5.jpg">
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Sean Bean

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Web Developer </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">sean&#64;dellito.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$10,500</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <div class="symbol  symbol-45px symbol-circle "><img alt="Pic"
                        src="../../../../../assets/media/avatars/profile-1.png">
                    </div>
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Academic

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        UI/UX Designer </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">{{currentUser?.email}}</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$20,000</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <span class="symbol-label bg-light-warning text-warning fw-semibold">
                      C </span>
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Mikaela Collins

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Head Of Marketing </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">mik&#64;pex.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$9,300</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <img alt="Pic" src="../../../../../assets/media/avatars/300-9.jpg">
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Francis Mitcham

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Software Arcitect </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">f.mit&#64;kpmg.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$15,000</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <span class="symbol-label bg-light-danger text-danger fw-semibold">
                      O </span>
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Olivia Wild

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        System Admin </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">olivia&#64;corpmail.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$23,000</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <span class="symbol-label bg-light-primary text-primary fw-semibold">
                      N </span>
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Neil Owen

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Account Manager </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">owen.neil&#64;gmail.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$45,800</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <img alt="Pic" src="../../../../../assets/media/avatars/300-23.jpg">
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Dan Wilson

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Web Desinger </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">dam&#64;consilting.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$90,500</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <span class="symbol-label bg-light-danger text-danger fw-semibold">
                      E </span>
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Emma Bold

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Corporate Finance </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">emma&#64;intenso.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$5,000</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 border-bottom border-gray-300 border-bottom-dashed">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <img alt="Pic" src="../../../../../assets/media/avatars/300-12.jpg">
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Ana Crown

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Customer Relationship </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">ana.cf&#64;limtel.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$70,000</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
              <!--begin::User-->
              <div class="d-flex flex-stack py-5 ">
                <!--begin::Details-->
                <div class="d-flex align-items-center">
                  <!--begin::Avatar-->
                  <div class="symbol symbol-35px symbol-circle">
                    <div class="symbol  symbol-45px symbol-circle "><img alt="Pic"
                        src="../../../../../assets/media/avatars/profile-1.png">
                    </div>
                  </div>
                  <!--end::Avatar-->

                  <!--begin::Details-->
                  <div class="ms-6">
                    <!--begin::Name-->
                    <a class="d-flex align-items-center fs-5 fw-bold text-gray-900 text-hover-primary">
                      Robert Doe

                      <span class="badge badge-light fs-8 fw-semibold ms-2">
                        Marketing Executive </span>
                    </a>
                    <!--end::Name-->

                    <!--begin::Email-->
                    <div class="fw-semibold text-muted">robert&#64;benko.com</div>
                    <!--end::Email-->
                  </div>
                  <!--end::Details-->
                </div>
                <!--end::Details-->

                <!--begin::Stats-->
                <div class="d-flex">
                  <!--begin::Sales-->
                  <div class="text-end">
                    <div class="fs-5 fw-bold text-gray-900">$45,500</div>

                    <div class="fs-7 text-muted">Sales</div>
                  </div>
                  <!--end::Sales-->
                </div>
                <!--end::Stats-->
              </div>
              <!--end::User-->
            </div>
            <!--end::List-->
          </div>
          <!--end::Users-->

          <!--begin::Notice-->
          <div class="d-flex justify-content-between">
            <!--begin::Label-->
            <div class="fw-semibold">
              <label class="fs-6" for="">Adding Users by Team Members</label>

              <div class="fs-7 text-muted">If you need more info, please check budget planning</div>
            </div>
            <!--end::Label-->

            <!--begin::Switch-->
            <label class="form-check form-switch form-check-custom form-check-solid">
              <input class="form-check-input" type="checkbox" value="" checked="checked">

              <span class="form-check-label fw-semibold text-muted">
                Allowed
              </span>
            </label>
            <!--end::Switch-->
          </div>
          <!--end::Notice-->
        </div>
        <!--end::Modal body-->
      </div>
      <!--end::Modal content-->
    </div>
    <!--end::Modal dialog-->
  </div>
  <!--end::Modal - View Users--><!--begin::Modal - Users Search-->
  <div class="modal fade" id="kt_modal_users_search" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog modal-dialog-centered mw-650px">
      <!--begin::Modal content-->
      <div class="modal-content">
        <!--begin::Modal header-->
        <div class="modal-header pb-0 border-0 justify-content-end">
          <!--begin::Close-->
          <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
            <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
          </div>
          <!--end::Close-->
        </div>
        <!--begin::Modal header-->

        <!--begin::Modal body-->
        <div class="modal-body scroll-y mx-5 mx-xl-18 pt-0 pb-15">
          <!--begin::Content-->
          <div class="text-center mb-13">
            <h1 class="mb-3">Search Users</h1>

            <div class="text-muted fw-semibold fs-5">
              Invite Collaborators To Your Project
            </div>
          </div>
          <!--end::Content-->

          <!--begin::Search-->
          <div id="kt_modal_users_search_handler" data-kt-search-keypress="true" data-kt-search-min-length="2"
            data-kt-search-enter="enter" data-kt-search-layout="inline" data-kt-search="true">

            <!--begin::Form-->
            <form data-kt-search-element="form" class="w-100 position-relative mb-5" autocomplete="off">
              <!--begin::Hidden input(Added to disable form autocomplete)-->
              <input type="hidden">
              <!--end::Hidden input-->

              <!--begin::Icon-->
              <i
                class="ki-duotone ki-magnifier fs-2 fs-lg-1 text-gray-500 position-absolute top-50 ms-5 translate-middle-y"><span
                  class="path1"></span><span class="path2"></span></i> <!--end::Icon-->

              <!--begin::Input-->
              <input type="text" class="form-control form-control-lg form-control-solid px-15" name="search" value=""
                placeholder="Search by username, full name or email..." data-kt-search-element="input">
              <!--end::Input-->

              <!--begin::Spinner-->
              <span class="position-absolute top-50 end-0 translate-middle-y lh-0 d-none me-5"
                data-kt-search-element="spinner">
                <span class="spinner-border h-15px w-15px align-middle text-muted"></span>
              </span>
              <!--end::Spinner-->

              <!--begin::Reset-->
              <span
                class="btn btn-flush btn-active-color-primary position-absolute top-50 end-0 translate-middle-y lh-0 me-5 d-none"
                data-kt-search-element="clear">
                <i class="ki-duotone ki-cross fs-2 fs-lg-1 me-0"><span class="path1"></span><span
                    class="path2"></span></i> </span>
              <!--end::Reset-->
            </form>
            <!--end::Form-->

            <!--begin::Wrapper-->
            <div class="py-5">
              <!--begin::Suggestions-->
              <div data-kt-search-element="suggestions">
                <!--begin::Heading-->
                <h3 class="fw-semibold mb-5">Recently searched:</h3>
                <!--end::Heading-->

                <!--begin::Users-->
                <div class="mh-375px scroll-y me-n7 pe-7">
                  <!--begin::User-->
                  <a class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">
                    <!--begin::Avatar-->
                    <div class="symbol symbol-35px symbol-circle me-5">
                      <img alt="Pic" src="../../../../../assets/media/avatars/300-6.jpg">
                    </div>
                    <!--end::Avatar-->

                    <!--begin::Info-->
                    <div class="fw-semibold">
                      <span class="fs-6 text-gray-800 me-2">Emma Smith</span>
                      <span class="badge badge-light">Art Director</span>
                    </div>
                    <!--end::Info-->
                  </a>
                  <!--end::User-->
                  <!--begin::User-->
                  <a class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">
                    <!--begin::Avatar-->
                    <div class="symbol symbol-35px symbol-circle me-5">
                      <span class="symbol-label bg-light-danger text-danger fw-semibold">
                        M </span>
                    </div>
                    <!--end::Avatar-->

                    <!--begin::Info-->
                    <div class="fw-semibold">
                      <span class="fs-6 text-gray-800 me-2">Melody Macy</span>
                      <span class="badge badge-light">Marketing Analytic</span>
                    </div>
                    <!--end::Info-->
                  </a>
                  <!--end::User-->
                  <!--begin::User-->
                  <a class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">
                    <!--begin::Avatar-->
                    <div class="symbol symbol-35px symbol-circle me-5">
                      <img alt="Pic" src="../../../../../assets/media/avatars/300-1.jpg">
                    </div>
                    <!--end::Avatar-->

                    <!--begin::Info-->
                    <div class="fw-semibold">
                      <span class="fs-6 text-gray-800 me-2">Max Smith</span>
                      <span class="badge badge-light">Software Enginer</span>
                    </div>
                    <!--end::Info-->
                  </a>
                  <!--end::User-->
                  <!--begin::User-->
                  <a class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">
                    <!--begin::Avatar-->
                    <div class="symbol symbol-35px symbol-circle me-5">
                      <img alt="Pic" src="../../../../../assets/media/avatars/300-5.jpg">
                    </div>
                    <!--end::Avatar-->

                    <!--begin::Info-->
                    <div class="fw-semibold">
                      <span class="fs-6 text-gray-800 me-2">Sean Bean</span>
                      <span class="badge badge-light">Web Developer</span>
                    </div>
                    <!--end::Info-->
                  </a>
                  <!--end::User-->
                  <!--begin::User-->
                  <a class="d-flex align-items-center p-3 rounded bg-state-light bg-state-opacity-50 mb-1">
                    <!--begin::Avatar-->
                    <div class="symbol symbol-35px symbol-circle">
                      <div class="symbol  symbol-45px symbol-circle "><img alt="Pic"
                          src="../../../../../assets/media/avatars/profile-1.png">
                      </div>
                    </div>
                    <!--end::Avatar-->

                    <!--begin::Info-->
                    <div class="fw-semibold">
                      <span class="fs-6 text-gray-800 me-2">Academic</span>
                      <span class="badge badge-light">UI/UX Designer</span>
                    </div>
                    <!--end::Info-->
                  </a>
                  <!--end::User-->
                </div>
                <!--end::Users-->
              </div>
              <!--end::Suggestions-->

              <!--begin::Results(add d-none to below element to hide the users list by default)-->
              <div data-kt-search-element="results" class="d-none">
                <!--begin::Users-->
                <div class="mh-375px scroll-y me-n7 pe-7">
                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="0">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='0']" value="0">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-6.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Emma
                          Smith</a>

                        <div class="fw-semibold text-muted">smith&#64;kpmg.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-9-fgfp"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2" selected="" data-select2-id="select2-data-11-gldt">
                          Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-10-xc6r" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-cp50-container" aria-controls="select2-cp50-container"><span
                              class="select2-selection__rendered" id="select2-cp50-container" aria-readonly="true"
                              title="Owner">Owner</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="1">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='1']" value="1">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <span class="symbol-label bg-light-danger text-danger fw-semibold">
                          M </span>
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Melody
                          Macy</a>

                        <div class="fw-semibold text-muted">melody&#64;altbox.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-12-6f87"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1" selected="" data-select2-id="select2-data-14-7h94">
                          Guest</option>
                        <option value="2">Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-13-j1wf" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-u2st-container" aria-controls="select2-u2st-container"><span
                              class="select2-selection__rendered" id="select2-u2st-container" aria-readonly="true"
                              title="Guest">Guest</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="2">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='2']" value="2">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-1.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Max
                          Smith</a>

                        <div class="fw-semibold text-muted">max&#64;kt.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-15-8rrw"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2">Owner</option>
                        <option value="3" selected="" data-select2-id="select2-data-17-jusc">Can
                          Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-16-pywy" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-fmo7-container" aria-controls="select2-fmo7-container"><span
                              class="select2-selection__rendered" id="select2-fmo7-container" aria-readonly="true"
                              title="Can Edit">Can
                              Edit</span><span class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="3">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='3']" value="3">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-5.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Sean
                          Bean</a>

                        <div class="fw-semibold text-muted">sean&#64;dellito.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-18-atfe"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2" selected="" data-select2-id="select2-data-20-zslz">
                          Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-19-hxq5" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-0pkv-container" aria-controls="select2-0pkv-container"><span
                              class="select2-selection__rendered" id="select2-0pkv-container" aria-readonly="true"
                              title="Owner">Owner</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="4">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='4']" value="4">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-25.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Academic</a>

                        <div class="fw-semibold text-muted">{{currentUser?.email}}</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-21-iufb"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2">Owner</option>
                        <option value="3" selected="" data-select2-id="select2-data-23-q3gt">Can
                          Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-22-461q" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-xfhc-container" aria-controls="select2-xfhc-container"><span
                              class="select2-selection__rendered" id="select2-xfhc-container" aria-readonly="true"
                              title="Can Edit">Can
                              Edit</span><span class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="5">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='5']" value="5">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <span class="symbol-label bg-light-warning text-warning fw-semibold">
                          C </span>
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Mikaela
                          Collins</a>

                        <div class="fw-semibold text-muted">mik&#64;pex.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-24-1c2n"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2" selected="" data-select2-id="select2-data-26-kvlo">
                          Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-25-vmil" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-ypo2-container" aria-controls="select2-ypo2-container"><span
                              class="select2-selection__rendered" id="select2-ypo2-container" aria-readonly="true"
                              title="Owner">Owner</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="6">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='6']" value="6">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-9.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Francis
                          Mitcham</a>

                        <div class="fw-semibold text-muted">f.mit&#64;kpmg.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-27-ay1l"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2">Owner</option>
                        <option value="3" selected="" data-select2-id="select2-data-29-sxmm">Can
                          Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-28-jmmx" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-d7ve-container" aria-controls="select2-d7ve-container"><span
                              class="select2-selection__rendered" id="select2-d7ve-container" aria-readonly="true"
                              title="Can Edit">Can
                              Edit</span><span class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="7">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='7']" value="7">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <span class="symbol-label bg-light-danger text-danger fw-semibold">
                          O </span>
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Olivia
                          Wild</a>

                        <div class="fw-semibold text-muted">olivia&#64;corpmail.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-30-w5j0"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2" selected="" data-select2-id="select2-data-32-lnh7">
                          Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-31-ke9e" style="width: 100%;"><span class="selection">
                          <span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-780x-container" aria-controls="select2-780x-container">
                            <span class="select2-selection__rendered" id="select2-780x-container" aria-readonly="true"
                              title="Owner">Owner</span>
                            <span class="select2-selection__arrow"><b></b>
                            </span>
                          </span>
                        </span>
                        <span class="dropdown-wrapper" aria-hidden="true"></span>
                      </span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="8">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='8']" value="8">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <span class="symbol-label bg-light-primary text-primary fw-semibold">
                          N </span>
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Neil
                          Owen</a>

                        <div class="fw-semibold text-muted">owen.neil&#64;gmail.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-33-6ahm"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1" selected="" data-select2-id="select2-data-35-jbs3">
                          Guest</option>
                        <option value="2">Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-34-lg8q" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-b6t3-container" aria-controls="select2-b6t3-container"><span
                              class="select2-selection__rendered" id="select2-b6t3-container" aria-readonly="true"
                              title="Guest">Guest</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="9">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='9']" value="9">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-23.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Dan
                          Wilson</a>

                        <div class="fw-semibold text-muted">dam&#64;consilting.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-36-181r"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2">Owner</option>
                        <option value="3" selected="" data-select2-id="select2-data-38-j0dr">Can
                          Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-37-jhtq" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-cs5p-container" aria-controls="select2-cs5p-container"><span
                              class="select2-selection__rendered" id="select2-cs5p-container" aria-readonly="true"
                              title="Can Edit">Can
                              Edit</span><span class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="10">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='10']" value="10">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <span class="symbol-label bg-light-danger text-danger fw-semibold">
                          E </span>
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Emma
                          Bold</a>

                        <div class="fw-semibold text-muted">emma&#64;intenso.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-39-qrld"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2" selected="" data-select2-id="select2-data-41-f7nz">
                          Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-40-5i08" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-k55w-container" aria-controls="select2-k55w-container"><span
                              class="select2-selection__rendered" id="select2-k55w-container" aria-readonly="true"
                              title="Owner">Owner</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="11">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='11']" value="11">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-12.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Ana
                          Crown</a>

                        <div class="fw-semibold text-muted">ana.cf&#64;limtel.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-42-ce13"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1" selected="" data-select2-id="select2-data-44-uuhw">
                          Guest</option>
                        <option value="2">Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-43-wjn8" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-cgj5-container" aria-controls="select2-cgj5-container"><span
                              class="select2-selection__rendered" id="select2-cgj5-container" aria-readonly="true"
                              title="Guest">Guest</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="12">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='12']" value="12">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <div class="symbol  symbol-45px symbol-circle "><img alt="Pic"
                            src="../../../../../assets/media/avatars/profile-1.png">
                        </div>
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Robert
                          Doe</a>

                        <div class="fw-semibold text-muted">robert&#64;benko.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-45-thn2"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2">Owner</option>
                        <option value="3" selected="" data-select2-id="select2-data-47-trsi">Can
                          Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-46-o4t4" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-4tp7-container" aria-controls="select2-4tp7-container"><span
                              class="select2-selection__rendered" id="select2-4tp7-container" aria-readonly="true"
                              title="Can Edit">Can
                              Edit</span><span class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="13">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='13']" value="13">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-13.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">John
                          Miller</a>

                        <div class="fw-semibold text-muted">miller&#64;mapple.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-48-hr2k"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2">Owner</option>
                        <option value="3" selected="" data-select2-id="select2-data-50-gjhf">Can
                          Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-49-oja4" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-wwzj-container" aria-controls="select2-wwzj-container"><span
                              class="select2-selection__rendered" id="select2-wwzj-container" aria-readonly="true"
                              title="Can Edit">Can
                              Edit</span><span class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="14">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='14']" value="14">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <span class="symbol-label bg-light-success text-success fw-semibold">
                          L </span>
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Lucy
                          Kunic</a>

                        <div class="fw-semibold text-muted">lucy.m&#64;fentech.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-51-63u9"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2" selected="" data-select2-id="select2-data-53-fvgp">
                          Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-52-1dyq" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-a7v0-container" aria-controls="select2-a7v0-container"><span
                              class="select2-selection__rendered" id="select2-a7v0-container" aria-readonly="true"
                              title="Owner">Owner</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="15">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='15']" value="15">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-21.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Ethan
                          Wilder</a>

                        <div class="fw-semibold text-muted">ethan&#64;loop.com.au</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-54-j27p"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1" selected="" data-select2-id="select2-data-56-gnsb">
                          Guest</option>
                        <option value="2">Owner</option>
                        <option value="3">Can Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-55-3w3c" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-s6sc-container" aria-controls="select2-s6sc-container"><span
                              class="select2-selection__rendered" id="select2-s6sc-container" aria-readonly="true"
                              title="Guest">Guest</span><span
                              class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->

                  <!--begin::Separator-->
                  <div class="border-bottom border-gray-300 border-bottom-dashed"></div>
                  <!--end::Separator-->

                  <!--begin::User-->
                  <div class="rounded d-flex flex-stack bg-active-lighten p-4" data-user-id="16">
                    <!--begin::Details-->
                    <div class="d-flex align-items-center">
                      <!--begin::Checkbox-->
                      <label class="form-check form-check-custom form-check-solid me-5">
                        <input class="form-check-input" type="checkbox" name="users" data-kt-check="true"
                          data-kt-check-target="[data-user-id='16']" value="16">
                      </label>
                      <!--end::Checkbox-->

                      <!--begin::Avatar-->
                      <div class="symbol symbol-35px symbol-circle">
                        <img alt="Pic" src="../../../../../assets/media/avatars/300-5.jpg">
                      </div>
                      <!--end::Avatar-->

                      <!--begin::Details-->
                      <div class="ms-5">
                        <a class="fs-5 fw-bold text-gray-900 text-hover-primary mb-2">Sean
                          Bean</a>

                        <div class="fw-semibold text-muted">sean&#64;dellito.com</div>
                      </div>
                      <!--end::Details-->
                    </div>
                    <!--end::Details-->

                    <!--begin::Access menu-->
                    <div class="ms-2 w-100px">
                      <select class="form-select form-select-solid form-select-sm select2-hidden-accessible"
                        data-control="select2" data-hide-search="true" data-select2-id="select2-data-57-5p5n"
                        tabindex="-1" aria-hidden="true" data-kt-initialized="1">
                        <option value="1">Guest</option>
                        <option value="2">Owner</option>
                        <option value="3" selected="" data-select2-id="select2-data-59-kbkn">Can
                          Edit</option>
                      </select><span class="select2 select2-container select2-container--bootstrap5" dir="ltr"
                        data-select2-id="select2-data-58-gvnp" style="width: 100%;"><span class="selection"><span
                            class="select2-selection select2-selection--single form-select form-select-solid form-select-sm"
                            aria-haspopup="true" aria-expanded="false" aria-disabled="false"
                            aria-labelledby="select2-t1s8-container" aria-controls="select2-t1s8-container"><span
                              class="select2-selection__rendered" id="select2-t1s8-container" aria-readonly="true"
                              title="Can Edit">Can
                              Edit</span><span class="select2-selection__arrow"><b></b></span></span></span><span
                          class="dropdown-wrapper" aria-hidden="true"></span></span>
                    </div>
                    <!--end::Access menu-->
                  </div>
                  <!--end::User-->


                </div>
                <!--end::Users-->

                <!--begin::Actions-->
                <div class="d-flex flex-center mt-15">
                  <button type="reset" id="kt_modal_users_search_reset" data-bs-dismiss="modal"
                    class="btn btn-active-light me-3">
                    Cancel
                  </button>

                  <button type="submit" id="kt_modal_users_search_submit" class="btn btn-primary">
                    Add Selected Users
                  </button>
                </div>
                <!--end::Actions-->
              </div>
              <!--end::Results-->
              <!--begin::Empty-->
              <div data-kt-search-element="empty" class="text-center d-none">
                <!--begin::Message-->
                <div class="fw-semibold py-10">
                  <div class="text-gray-600 fs-3 mb-2">No users found</div>

                  <div class="text-muted fs-6">Try to search by username, full name or email...</div>
                </div>
                <!--end::Message-->

                <!--begin::Illustration-->
                <div class="text-center px-5">
                  <img src="../../../../../assets/media/illustrations/sketchy-1/1.png" alt=""
                    class="w-100 h-200px h-sm-325px">
                </div>
                <!--end::Illustration-->
              </div>
              <!--end::Empty-->
            </div>
            <!--end::Wrapper-->
          </div>
          <!--end::Search-->
        </div>
        <!--end::Modal body-->
      </div>
      <!--end::Modal content-->
    </div>
    <!--end::Modal dialog-->
  </div>
  <!--end::Modal - Users Search--><!--end::Modals-->
</div>