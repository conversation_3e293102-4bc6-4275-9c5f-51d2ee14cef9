<app-toolbar class="toolbar" [showBack]="false">
  <div class="d-flex align-items-center">
    <h2 class="text-gray-800 fw-bold m-0">Chatbot Analytics</h2>
  </div>
</app-toolbar>

<div class="card border border-dashed border-gray-300 mb-5 mb-xl-10">
  <!-- Error <PERSON> -->
  <div *ngIf="error" class="alert alert-dismissible bg-light-danger d-flex flex-column flex-sm-row p-5 m-5">
    <i class="bi bi-exclamation-triangle-fill fs-2hx text-danger me-4 mb-5 mb-sm-0"></i>
    <div class="d-flex flex-column flex-grow-1">
      <h4 class="mb-1 text-danger">Error loading analytics data</h4>
      <p class="mb-2">{{ error.message || 'An unexpected error occurred. Please try again.' }}</p>

      <div *ngIf="error.details" class="bg-white p-3 rounded-2 mb-3">
        <small class="text-muted d-block mb-1">Error details:</small>
        <code class="text-danger">{{ error.details }}</code>
      </div>

      <div class="d-flex gap-2 mt-2">
        <button class="btn btn-sm btn-danger" (click)="onRetry()" [disabled]="loading">
          <i class="bi bi-arrow-clockwise me-1" [class.spin]="loading"></i>
          {{ loading ? 'Retrying...' : 'Retry' }}
        </button>
        <button class="btn btn-sm btn-outline-secondary" (click)="error = null">
          <i class="bi bi-x-lg me-1"></i>
          Dismiss
        </button>
      </div>
    </div>
    <button type="button" class="position-absolute position-sm-relative m-2 m-sm-0 top-0 end-0 btn btn-icon ms-sm-auto"
      (click)="error = null">
      <i class="bi bi-x fs-1"></i>
    </button>
  </div>

  <!--begin::KPIs-->
  <div class="card-body pt-9 pb-0">
    <!--begin::Container-->
    <div class="d-flex flex-wrap flex-stack">
      <!--begin::Wrapper-->
      <div class="d-flex flex-wrap">
        <!--begin::Total Conversations-->
        <div class="card card-flush border border-dashed border-gray-300 flex-row-fluid flex-grow-1 p-4 me-6 mb-3">
          <div class="d-flex align-items-center">
            <i class="ki-duotone ki-chat-text fs-2x text-primary me-3">
              <span class="path1"></span>
              <span class="path2"></span>
              <span class="path3"></span>
            </i>
            <div>
              <div class="fs-2 fw-bold text-gray-800">{{ totalItems | number }}</div>
              <div class="fw-semibold text-muted">Total Conversations</div>
            </div>
          </div>
        </div>
        <!--end::Total Conversations-->

        <!--begin::Unique Users-->
        <div class="card card-flush border border-dashed border-gray-300 flex-row-fluid flex-grow-1 p-4 me-6 mb-3">
          <div class="d-flex align-items-center">
            <i class="ki-duotone ki-profile-user fs-2x text-success me-3">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            <div>
              <div class="fs-2 fw-bold text-gray-800">{{ kpis.uniqueUsers | number }}</div>
              <div class="fw-semibold text-muted">Unique Users</div>
            </div>
          </div>
        </div>
        <!--end::Unique Users-->

        <!--begin::Flagged-->
        <div class="card card-flush border border-dashed border-gray-300 flex-row-fluid flex-grow-1 p-4 me-6 mb-3">
          <div class="d-flex align-items-center">
            <i class="ki-duotone ki-flag fs-2x text-danger me-3">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            <div>
              <div class="fs-2 fw-bold text-gray-800">{{ kpis.flaggedUsers | number }}</div>
              <div class="fw-semibold text-muted">Flagged</div>
            </div>
          </div>
        </div>
        <!--end::Flagged-->
      </div>
      <!--end::Wrapper-->

      <!--begin::Last Updated-->
      <div class="d-flex align-items-center w-lg-200px">
        <i class="ki-duotone ki-information-5 fs-2 text-muted me-2">
          <span class="path1"></span>
          <span class="path2"></span>
          <span class="path3"></span>
        </i>
        <div class="fw-semibold text-muted">
          <div class="fs-7">Last updated</div>
          <div class="fs-6 text-gray-800">{{ lastUpdated | date:'short' }}</div>
        </div>
      </div>
      <!--end::Last Updated-->
    </div>
    <!--end::Container-->
  </div>
  <!--end::KPIs-->

  <!--begin::Tabs-->
  <div class="card-px-0">
    <!--begin::Tabs-->
    <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fw-bold fs-5">
      <!--begin::Tab item-->
      <li class="nav-item">
        <a class="nav-link text-active-primary pb-5 px-5 cursor-pointer" [class.active]="currentTab === 'conversations'" 
          (click)="onTabChange('conversations')" (keydown.enter)="onTabChange('conversations')" role="button" tabindex="0">
          <i class="ki-duotone ki-message-text-2 fs-2 me-2">
            <span class="path1"></span>
            <span class="path2"></span>
            <span class="path3"></span>
          </i>
          Conversations
        </a>
      </li>
      <!--end::Tab item-->
      
      <!--begin::Tab item-->
      <li class="nav-item">
        <a class="nav-link text-active-primary pb-5 px-5 cursor-pointer" [class.active]="currentTab === 'analytics'" 
          (click)="onTabChange('analytics')" (keydown.enter)="onTabChange('analytics')" role="button" tabindex="0">
          <i class="ki-duotone ki-chart-line fs-2 me-2">
            <span class="path1"></span>
            <span class="path2"></span>
            <span class="path3"></span>
          </i>
          Analytics
        </a>
      </li>
      <!--end::Tab item-->
      <!--begin::Tab item-->
      <li class="nav-item">
        <a class="nav-link text-active-primary pb-5 px-5 cursor-pointer" [class.active]="currentTab === 'userChatbot'" 
          (click)="onTabChange('userChatbot')" (keydown.enter)="onTabChange('userChatbot')" role="button" tabindex="0">
          <i class="ki-duotone ki-message-text-2 fs-2 me-2">
            <span class="path1"></span>
            <span class="path2"></span>
            <span class="path3"></span>
          </i>
          User Chatbot
        </a>
      </li>
      <!--end::Tab item-->
    </ul>
    <!--end::Tabs-->
  </div>
  <!--end::Tabs-->
</div>

<!--begin::Conversations Tab-->
<div *ngIf="currentTab === 'conversations'" class="d-flex flex-column flex-lg-row gap-6">
  <!--begin::Sidebar-->
  <div *ngIf="showFilterPanel" class="flex-column flex-lg-row-auto w-100 w-lg-250px w-xxl-300px">
    <!--begin::Card-->
    <div class="card card-flush border border-dashed border-gray-300 mb-6 mb-lg-0">
      <!--begin::Card header-->
      <div class="card-header">
        <!--begin::Card title-->
        <h3 class="card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">Filter</span>
          <span class="text-muted pt-2 fw-semibold fs-7">Filter conversations</span>
        </h3>
        <!--end::Card title-->
        
        <!--begin::Card toolbar-->
        <div class="card-toolbar">
          <button type="button" class="btn btn-sm btn-icon btn-active-color-primary" (click)="toggleFilterPanel()">
            <i class="ki-duotone ki-cross fs-1">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
          </button>
        </div>
        <!--end::Card toolbar-->
      </div>
      <!--end::Card header-->
      
      <!--begin::Card body-->
      <div class="card-body pt-0">
        <!-- Loading state -->
        <div *ngIf="!isFormInitialized" class="d-flex justify-content-center py-10">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
        
        <!-- Form content -->
        <ng-container *ngIf="isFormInitialized && filterForm">
        <!--begin::User Filter-->
        <div class="mb-10">
          <label class="form-label fw-semibold">Filter by User</label>
          <app-user-search-dropdown 
            [users]="users"
            [selectedUserId]="filterForm.get('userId')?.value"
            (userChange)="onUserFilterChange($event)">
          </app-user-search-dropdown>
        </div>
        <!--end::User Filter-->
        
        <!--begin::Chat Type-->
        <div class="mb-10">
          <label class="form-label fw-semibold">Chat Type</label>
          <select class="form-select form-select-solid" [formControl]="filterForm.get('chatType')">
            <option *ngFor="let type of chatTypes" [ngValue]="type.value">{{type.label}}</option>
          </select>
        </div>
        <!--end::Chat Type-->
        
        <!--begin::Date Range-->
        <div class="mb-10">
          <label class="form-label fw-semibold">Date Range</label>
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>Select a date range</mat-label>
            <mat-date-range-input [rangePicker]="picker">
              <input matStartDate placeholder="Start date" [formControl]="filterForm.get('searchStartDate')">
              <input matEndDate placeholder="End date" [formControl]="filterForm.get('searchEndDate')">
            </mat-date-range-input>
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-date-range-picker #picker></mat-date-range-picker>
            
            <button mat-icon-button matSuffix *ngIf="(filterForm.get('searchStartDate')?.value || filterForm.get('searchEndDate')?.value)" 
                   (click)="clearDateRange()" class="me-2">
              <i class="ki-duotone ki-cross fs-2">
                <span class="path1"></span>
                <span class="path2"></span>
              </i>
            </button>
          </mat-form-field>
        </div>
        <!--end::Date Range-->
        
        <!--begin::Actions-->
        <div class="d-flex justify-content-between">
          <button type="button" class="btn btn-light" (click)="clearFilters()">
            <i class="ki-duotone ki-arrow-rotate-left fs-2">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            Reset
          </button>
          <button type="button" class="btn btn-primary" (click)="applyFilters()">
            <i class="ki-duotone ki-filter fs-2">
              <span class="path1"></span>
              <span class="path2"></span>
            </i>
            Apply
          </button>
        </div>
      </ng-container> 
      </div>
      <!--end::Card body-->
    </div>
    <!--end::Card-->
  </div>
  <!--end::Sidebar-->

  <!-- Main Content -->
  <div class="flex-lg-row-fluid">
    <!--begin::Card-->
    <div class="card border border-dashed border-gray-300">
      <!--begin::Card header-->
      <div class="card-header border-0 pt-6">
        <!--begin::Card title-->
        <div class="card-title">
          <h3 class="card-label fw-bolder text-dark">
            {{ loading ? 'Loading...' : totalItems + ' Conversations' }}
          </h3>
        </div>
        <!--end::Card title-->
        
        <!--begin::Card toolbar-->
        <div class="card-toolbar">
          <!--begin::Toolbar-->
          <div class="d-flex justify-content-end">
            <!--begin::Refresh-->
            <button type="button" class="btn btn-icon btn-light-primary me-3" 
                    (click)="onRefresh()" 
                    [disabled]="loading"
                    title="Refresh">
              <i class="ki-duotone ki-arrows-circle fs-2" [class.rotate]="loading">
                <span class="path1"></span>
                <span class="path2"></span>
              </i>
            </button>
            <!--end::Refresh-->
            
            <!--begin::Filter Toggle-->
            <button type="button" class="btn btn-icon btn-light-primary me-3" 
                    (click)="toggleFilterPanel()"
                    title="Toggle filter">
              <i class="ki-duotone ki-filter fs-2">
                <span class="path1"></span>
                <span class="path2"></span>
              </i>
            </button>
            <!--end::Filter Toggle-->
            
            <!--begin::Sort-->
            <div class="d-flex align-items-center">
              <label class="form-label fw-semibold me-3 mb-0">Sort By:</label>
              <select class="form-select form-select-sm form-select-solid w-150px" 
                      [(ngModel)]="sortBy" 
                      (ngModelChange)="onSortChange($event)">
                <option *ngFor="let option of sortOptions" [ngValue]="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>
            <!--end::Sort-->
          </div>
          <!--end::Toolbar-->
        </div>
        <!--end::Card toolbar-->
      </div>
      <!--end::Card header-->
      
      <!--begin::Card body-->
      <div class="card-body pt-0">
        <!--begin::Loading-->
        <div *ngIf="loading" class="d-flex justify-content-center py-10">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
        <!--end::Loading-->
        
        <!--begin::Error-->
        <div *ngIf="error" class="alert alert-danger d-flex align-items-center p-5 mb-10">
          <i class="ki-duotone ki-cross-circle fs-2hx text-danger me-4">
            <span class="path1"></span>
            <span class="path2"></span>
          </i>
          <div class="d-flex flex-column">
            <h4 class="mb-1 text-danger">Error loading conversations</h4>
            <span>{{ error.message || 'An error occurred while loading conversations.' }}</span>
          </div>
          <button type="button" class="btn-close" (click)="error = null" aria-label="Close"></button>
        </div>
        <!--end::Error-->
        
        <!--begin::Table wrapper-->
        <div class="table-responsive" *ngIf="!loading">
          <!--begin::Table-->
          <table class="table align-middle table-row-dashed fs-6 gy-5 w-100">
            <!--begin::Table head-->
            <thead>
              <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                <th *ngFor="let column of tableColumns" 
                  [class.sorting]="false" 
                  [style.minWidth]="column.width || 'auto'"
                  [class.text-end]="column.align === 'end'"
                  [class.text-center]="column.align === 'center'"
                  (click)="column.sortable && onSort(column)">
                  <div class="d-flex align-items-center">
                    <span class="me-2">{{ column.label }}</span>
                    <i *ngIf="column.sortable" class="ki-duotone ki-arrow-down fs-2"
                      [class.rotate-180]="sortField === column.key && sortDirection === 'asc'"
                      [class.text-primary]="sortField === column.key">
                      <span class="path1"></span>
                      <span class="path2"></span>
                    </i>
                  </div>
                </th>
              </tr>
            </thead>
            <!--end::Table head-->
            
            <!--begin::Table body-->
            <tbody class="fw-semibold text-gray-600">
              <tr *ngFor="let conv of formattedConversations; trackBy: trackByConversationId" 
                  class="cursor-pointer" 
                  (click)="onConversationClick(conv)">
                <td class="text-nowrap">
                  <div class="d-flex align-items-center">
                    <div class="d-flex flex-column">
                      {{ tableColumns[0].render(conv) }}
                    </div>
                  </div>
                </td>
                <td [innerHTML]="tableColumns[1].render ? tableColumns[1].render(conv) : ''"></td>
                <td>
                  <div class="d-flex flex-column" style="max-width: 400px;">
                    <!-- Question -->
                    <div class="d-flex mb-1">
                      <i class="ki-duotone ki-question fs-3 text-primary me-3 mt-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                      </i>
                      <div class="text-gray-800 flex-grow-1">
                        <div class="mb-1">{{ conv.message || 'No message' }}</div>
                      </div>
                    </div>
                    <!-- Answer -->
                    <div *ngIf="conv.response !== null && conv.response !== undefined && conv.response !== ''" class="d-flex align-items-start">
                      <i class="ki-duotone ki-message-text-2 fs-3 text-success me-3 mt-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                      </i>
                      <div class="bg-light-success bg-opacity-10 p-3 rounded flex-grow-1 fs-7">
                        {{ conv.response }}
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              
              <!-- Empty state row -->
              <tr *ngIf="!loading && !error && formattedConversations.length === 0" class="odd">
                <td [attr.colspan]="tableColumns.length" class="text-center py-10">
                  <div class="d-flex flex-column align-items-center">
                    <i class="ki-duotone ki-document-remove fs-4x text-muted mb-4">
                      <span class="path1"></span>
                      <span class="path2"></span>
                    </i>
                    <span class="text-muted fw-semibold">No conversations found</span>
                    <button class="btn btn-sm btn-light-primary mt-3" (click)="clearFilters()">
                      Clear filters
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
            <!--end::Table body-->
          </table>
          <!--end::Table-->
        </div>
        <!--end::Table wrapper-->
        
        <!--begin::Pagination-->
        <div *ngIf="!loading && formattedConversations.length > 0" class="d-flex flex-stack flex-wrap pt-5">
          <!--begin::Info-->
          <div class="fs-6 fw-semibold text-gray-700">
            Showing {{ (currentPage - 1) * ITEMS_PER_PAGE + 1 }} to {{ Math.min(currentPage * ITEMS_PER_PAGE, totalItems) }} of {{ totalItems }} entries
          </div>
          <!--end::Info-->
          
          <!--begin::Pages-->
          <ul class="pagination">
            <!--begin::Previous page-->
            <li class="page-item previous" [class.disabled]="currentPage === 1">
              <a href="javascript:;" class="page-link" (click)="$event.preventDefault(); onPageChange(currentPage - 1)">
                <i class="ki-duotone ki-arrow-left fs-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </a>
            </li>
            <!--end::Previous page-->
            
            <!-- Page numbers with ellipsis -->
            <ng-container *ngFor="let page of getPageNumbers()">
              <ng-container *ngIf="page > 0">
                <li class="page-item" [class.active]="page === currentPage">
                  <a href="javascript:;" class="page-link" (click)="$event.preventDefault(); onPageChange(page)">
                    {{ page }}
                  </a>
                </li>
              </ng-container>
              <ng-container *ngIf="page === -1">
                <li class="page-item disabled">
                  <span class="page-link">...</span>
                </li>
              </ng-container>
            </ng-container>
            
            <!--begin::Next page-->
            <li class="page-item next" [class.disabled]="currentPage === totalPages">
              <a href="javascript:;" class="page-link" (click)="$event.preventDefault(); onPageChange(currentPage + 1)">
                <i class="ki-duotone ki-arrow-right fs-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </a>
            </li>
            <!--end::Next page-->
          </ul>
          <!--end::Pages-->
        </div>
        <!--end::Pagination-->
      </div>
    </div>
  </div>
  <!-- End Conversations Tab -->
</div>

<!-- Analytics Tab Content -->
<div *ngIf="currentTab === 'analytics'" class="d-flex flex-column flex-lg-row gap-6">
  <div class="flex-lg-row-fluid">
    <div class="card mb-5 mb-xl-10">
      <!--begin::Card header-->
      <div class="card-header border-0 pt-6">
        <!--begin::Card title-->
        <div class="card-title">
          <h3 class="card-label fw-bolder text-dark">Analytics Dashboard</h3>
        </div>
        <!--end::Card title-->
        
        <!--begin::Card toolbar-->
        <div class="card-toolbar">
          <div class="d-flex align-items-center flex-wrap gap-4">
            <!-- Date Range Selector -->
            <!-- <div class="d-flex align-items-center">
              <label class="form-label fw-semibold me-3 mb-0">Date Range:</label>
              <select class="form-select form-select-sm form-select-solid w-150px" [(ngModel)]="analyticsDateRange" (change)="onAnalyticsDateRangeChange()">
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
                <option value="custom">Custom Range</option>
              </select>
            </div> -->
            
            <!-- Refresh Button -->
            <!-- <button class="btn btn-sm btn-light-primary" (click)="loadData()" [disabled]="loading">
              <span class="indicator-label" *ngIf="!loading">
                <i class="ki-duotone ki-arrows-circle fs-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                Refresh
              </span>
              <span class="indicator-progress" *ngIf="loading">
                Please wait...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
              </span>
            </button> -->
          </div>
        </div>
        <!--end::Card toolbar-->
      </div>
      <!--end::Card header-->
      
      <!--begin::Card body-->
      <div class="card-body pt-0">
        <!-- Loading State -->
        <div *ngIf="loading" class="d-flex justify-content-center py-10">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
        
        <!-- Misuse Detection Section -->
        <div *ngIf="!loading" class="row g-5 g-xl-10 mb-6">
          <div class="col-12">
            <div class="card card-flush border border-dashed border-gray-300">
              <div class="card-header pt-5">
                <h3 class="card-title text-gray-800 fw-bold">Misuse Detection</h3>
              </div>
              <div class="card-body pt-0">
                <app-misuse-detection [conversations]="allConversations" (flaggedUsersChange)="onFlaggedUsersChange($event)"></app-misuse-detection>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Charts Grid -->
        <div class="row g-5 g-xl-8">
          <!-- Conversation Volume Chart -->
          <div class="col-12">
            <div class="card card-flush h-md-100">
              <div class="card-header border-0 pt-5">
                <h3 class="card-title align-items-start flex-column">
                  <span class="card-label fw-bold text-gray-800">Conversation Volume</span>
                  <span class="text-muted mt-1 fw-semibold fs-7">Number of conversations over time</span>
                </h3>
                <div class="card-toolbar">
                  <button class="btn btn-sm btn-icon btn-color-gray-400 btn-active-icon-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                    <i class="ki-duotone ki-dots-vertical fs-2">
                      <span class="path1"></span>
                      <span class="path2"></span>
                      <span class="path3"></span>
                    </i>
                  </button>
                  <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
                    <div class="menu-item px-3">
                      <a href="#" class="menu-link px-3">Export</a>
                    </div>
                    <div class="menu-item px-3">
                      <a href="#" class="menu-link px-3">Settings</a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-body pt-0">
                <div #volumeChartEl class="min-h-300px"></div>
              </div>
            </div>
          </div>
          <!-- Chat Type Distribution -->
          <div class="col-md-6">
            <div class="card card-flush h-md-100">
              <div class="card-header border-0 pt-5">
                <h3 class="card-title align-items-start flex-column">
                  <span class="card-label fw-bold text-gray-800">Chat Type Distribution</span>
                  <span class="text-muted mt-1 fw-semibold fs-7">Breakdown by conversation type</span>
                </h3>
              </div>
              <div class="card-body pt-0">
                <div #chatTypeChartEl class="min-h-300px"></div>
              </div>
            </div>
          </div>
        </div>
        <!--end::Charts Grid-->
      </div>
      <!--end::Card body-->
    </div>
    <!--end::Card-->
  </div>
  <!--end::Analytics Tab-->
</div>
<div *ngIf="currentTab === 'userChatbot'" class="w-100">
  <app-user-chatbot></app-user-chatbot>
</div>