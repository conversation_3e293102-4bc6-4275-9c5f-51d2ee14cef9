import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  AfterViewInit,
  ChangeDetectorRef
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { ChatAnalyticsService } from './services/chat-analytics.service';
import { UsersService } from 'src/app/pages/organizations/services/users.service';
import { MisuseDetectionService } from './services/misuse-detection.service';
import {
  Conversation,
  ChatAnalyticsKPIs,
  TableColumn,
  DateRange,
  ErrorDetails,
  ChatType,
  ChatTypeOption
} from './models/chat-analytics.model';
import {
  formatDateForDisplay,
  formatDateForInput,
  formatDateForApi,
  getMessageTime,
  getCurrentTimestamp
} from './utils/date.utils';
import ApexCharts from 'apexcharts';

// Define types for analytics
type AnalyticsDateRange = '24h' | '7d' | '30d' | '90d' | 'custom';
type TrendMetric = 'volume' | 'duration' | 'satisfaction';

// Define interface for chart data
interface ChartData {
  volumeData?: number[];
  chatTypeData?: {
    series: number[];
    labels: string[];
  };
  sentimentData?: number[];
  trendData?: {
    values: number[];
    categories: string[];
  };
}

// Fix for Math in template
declare const Math: any;

@Component({
  selector: 'app-chatbot-analytics',
  templateUrl: './chatbot-analytics.component.html',
  styleUrls: []
})
export class ChatbotAnalyticsComponent implements OnInit, OnDestroy, AfterViewInit {
  // Component state
  loading = false;
  usersLoading = false; // Separate loading flag for users
  showFilterPanel = true;
  lastUpdated = getCurrentTimestamp();
  sortBy = 'Most Recent';
  analyticsDateRange: AnalyticsDateRange = '7d';
  trendMetric: TrendMetric = 'volume';

  // Chart instances and references
  private volumeChart: ApexCharts | null = null;
  private chatTypeChart: ApexCharts | null = null;
  private sentimentChart: ApexCharts | null = null;

  // ViewChild references for chart containers
  @ViewChild('volumeChartEl') private readonly volumeChartEl!: ElementRef;
  @ViewChild('chatTypeChartEl') private readonly chatTypeChartEl!: ElementRef;
  @ViewChild('sentimentChartEl') private readonly sentimentChartEl!: ElementRef;

  // Lifecycle hooks
  async ngOnInit(): Promise<void> {
    this.initializeDateRange();
    this.setDefaultFilters();
    try {
      // First load conversations (KPIs and table will be updated by applyFilters inside loadConversations)
      await this.loadConversations(true);
      // Then load analytics data
      await this.loadData();
      // If we're on the analytics tab, initialize charts after a small delay
      // to ensure DOM is ready
      if (this.currentTab === 'analytics') {
        setTimeout(() => {
          this.initCharts();
        }, 100);
      }
    } catch (error) {
      console.error('Error initializing component:', error);
    }
  }

  ngAfterViewInit(): void {
    if (this.currentTab === 'analytics') {
      this.initCharts();
    }
  }

  // Initialize chat type chart with data from conversations
  private initChatTypeChart(): void {
    try {
      // Make sure the chart container exists
      if (!this.chatTypeChartEl?.nativeElement) {
        console.error('Chat type chart container not found');
        return;
      }

      // Process conversation data to get chat type distribution
      const { series, labels } = this.processChatTypeData(this.conversations);
      
      // Define colors for each chat type (matching the existing color scheme)
      const colors = [
        '#50CD89', // Success (green) - Unify
        '#F1416C', // Danger (red) - Community
        '#FFC700', // Warning (yellow) - Family
        '#7239EA', // Primary (purple) - Knowledge
        '#009EF7', // Info (blue) - Dreams
        '#FF5E5E', // Alternative red - Spirituality
        '#A1A5B7'  // Gray - General
      ];
      
      const options: ApexCharts.ApexOptions = {
        series: series,
        chart: {
          type: 'donut',
          height: 300,
          width: '100%',
          toolbar: { show: false },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          },
          events: {
            mounted: (chart) => {
              console.log('Chat type chart mounted');
            },
            updated: (chart) => {
              console.log('Chat type chart updated');
            }
          }
        },
        labels: labels,
        colors: colors.slice(0, Math.max(labels.length, 1)),
        dataLabels: { 
          enabled: false 
        },
        legend: {
          position: 'right',
          horizontalAlign: 'center',
          fontSize: '12px',
          itemMargin: {
            horizontal: 8,
            vertical: 4
          },
          onItemClick: {
            toggleDataSeries: true
          },
          onItemHover: {
            highlightDataSeries: true
          },
          formatter: (legendName: string, opts: any) => {
            const series = opts.w.config.series[opts.seriesIndex];
            const total = opts.w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0);
            const percentage = total > 0 ? Math.round((series / total) * 100) : 0;
            return `${legendName}: ${series} (${percentage}%)`;
          }
        },
        responsive: [{
          breakpoint: 768,
          options: {
            chart: {
              height: 350
            },
            legend: {
              position: 'bottom',
              horizontalAlign: 'center',
              fontSize: '12px'
            }
          }
        }],
        noData: {
          text: 'Loading data...',
          align: 'center',
          verticalAlign: 'middle',
          offsetX: 0,
          offsetY: 0,
          style: {
            color: '#6c757d',
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif'
          }
        }
      };

      // Create and render new chart with a small delay to ensure DOM is ready
      setTimeout(() => {
        try {
          this.chatTypeChart = new ApexCharts(this.chatTypeChartEl.nativeElement, options);
          this.chatTypeChart.render();
          console.log('Chat type chart initialized');
        } catch (e) {
          console.error('Error initializing chat type chart:', e);
        }
      }, 100);
    } catch (e) {
      console.error('Error in initChatTypeChart:', e);
    }
  }

  // Initialize sentiment chart
  private initSentimentChart(): void {
    const options = {
      series: [{
        name: 'Sentiment',
        data: [30, 50, 20]
      }],
      chart: {
        type: 'bar',
        height: 300,
        toolbar: { show: false }
      },
      colors: ['#50CD89', '#FFC700', '#F1416C'],
      plotOptions: {
        bar: {
          columnWidth: '60%',
          distributed: true,
          borderRadius: 6
        }
      },
      dataLabels: { enabled: false },
      xaxis: {
        categories: ['Positive', 'Neutral', 'Negative'],
        axisBorder: { show: false },
        axisTicks: { show: false },
        labels: {
          style: {
            colors: '#A1A5B7',
            fontSize: '12px'
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: '#A1A5B7',
            fontSize: '12px'
          }
        }
      },
      grid: {
        borderColor: '#EBEDF3',
        strokeDashArray: 4,
        yaxis: { lines: { show: true } }
      }
    };

    this.sentimentChart = new ApexCharts(this.sentimentChartEl.nativeElement, options);
    this.sentimentChart.render();
  }

  // Component state
  currentTab: 'conversations' | 'analytics' | 'userChatbot' = 'conversations';
  searchQuery = '';
  dateRange: DateRange = {
    start: null,
    end: null
  };
  selectedChatTypes: string[] = [];

  // Sort and filter options
  sortOptions = [
    { value: 'Most Recent', label: 'Most Recent' },
    { value: 'Oldest', label: 'Oldest' },
    { value: 'User Name', label: 'User Name' }
  ];

  // Chat type options
  chatTypeOptions: ChatTypeOption[] = [
    { value: ChatType.GENERAL, label: 'General', badgeClass: 'badge badge-light-primary' },
    { value: ChatType.UNIFY, label: 'Unify', badgeClass: 'badge badge-light-primary' },
    { value: ChatType.COMMUNITY, label: 'Community', badgeClass: 'badge badge-light-success' },
    { value: ChatType.FAMILY, label: 'Family', badgeClass: 'badge badge-light-info' },
    { value: ChatType.KNOWLEDGE, label: 'Knowledge', badgeClass: 'badge badge-light-warning' },
    { value: ChatType.DREAMS, label: 'Dreams', badgeClass: 'badge badge-light-danger' },
    { value: ChatType.SPIRITUALITY, label: 'Spirituality', badgeClass: 'badge badge-light-dark' }
  ];

  // Date range options
  dateRangeOptions = [
    { value: '24h', label: 'Last 24 hours' },
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: 'custom', label: 'Custom Range' }
  ];

  filterForm: FormGroup | null = null;
  isFormInitialized = false;
  users: any[] = [];
  selectedUser: string = '';

  constructor(
    private readonly chatAnalyticsService: ChatAnalyticsService,
    private readonly usersService: UsersService,
    private readonly fb: FormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly misuseDetectionService: MisuseDetectionService // Inject the service
  ) {
    this.initializeForm();
    this.selectedChatType = '';
  }

  private initializeForm(): void {
    try {
      // First create the form group
      const formGroup = this.fb.group({
        searchStartDate: [null],
        searchEndDate: [null],
        chatType: [''],
        search: [''],
        userId: ['']
      });

      // Then assign it to the form
      this.filterForm = formGroup;

      // Mark form as initialized
      this.isFormInitialized = true;

      // Set default values after form is initialized
      this.initializeDateRange();

      // Load users for the dropdown
      this.loadUsers();
    } catch (error) {
      console.error('Error initializing form:', error);
      this.filterForm = this.fb.group({}); // Fallback empty form
      this.isFormInitialized = true;
    }
  }

  // Load users for the user search dropdown
  public loadUsers(): void {
    this.usersLoading = true;
    this.usersService.getExistingUsers().subscribe({
      next: (response) => {
        if (response?.data?.userByDate?.items) {
          this.users = response.data.userByDate.items.map((user: any) => ({
            id: user.id,
            name: (user.name ?? user.email) ?? 'Unknown User',
            email: user.email ?? ''
          }));
        } else {
          this.users = [];
        }
        this.usersLoading = false;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.users = []; // Ensure users array is always defined
        this.usersLoading = false;
      }
    });
  }

  // Initialize date range with null values
  private initializeDateRange(): void {
    this.dateRange = { start: null, end: null };
    if (this.filterForm) {
      this.filterForm.patchValue({
        searchStartDate: null,
        searchEndDate: null
      });
    }
  }

  // Set default filters
  private setDefaultFilters(): void {
    this.selectedChatTypes = this.chatTypeOptions.map(option => option.value);
  }

  // Get date range based on selected range
  private getDateRange(): { start: Date; end: Date } {
    let end = new Date();
    let start = new Date();

    if (this.analyticsDateRange === 'custom') {
      // For custom range, use the provided dates
      start = this.dateRange.start ? new Date(this.dateRange.start) : new Date();
      end = this.dateRange.end ? new Date(this.dateRange.end) : new Date();
    } else {
      // For preset ranges, calculate start date based on range
      switch (this.analyticsDateRange) {
        case '24h':
          start.setDate(end.getDate() - 1);
          break;
        case '7d':
          start.setDate(end.getDate() - 7);
          break;
        case '30d':
          start.setDate(end.getDate() - 30);
          break;
        case '90d':
          start.setDate(end.getDate() - 90);
          break;
        default:
          start.setDate(end.getDate() - 7);
      }
    }

    return { start, end };
  }

  // Load data based on current filters
  async loadData(): Promise<void> {
    // Don't start a new load if already loading
    if (this.loading) return;
    
    try {
      this.loading = true;
      this.error = null;

      // Get date range
      const { start, end } = this.getDateRange();

      // Call the analytics service
      const data = await this.chatAnalyticsService
        .getAnalyticsData({
          startDate: start.toISOString(),
          endDate: end.toISOString(),
          types: this.selectedChatType,
          sort: this.sortBy,
          search: this.searchQuery,
          userId: this.selectedUser || undefined
        })
        .toPromise();

      // Update charts with new data
      if (data) {
        this.updateCharts(data);
      }
      
      // If we're on the analytics tab, ensure charts are properly initialized
      if (this.currentTab === 'analytics' && !this.volumeChart) {
        setTimeout(() => {
          this.initCharts();
        }, 100);
      }
      
    } catch (err) {
      console.error('Error loading analytics data:', err);
      this.error = {
        message: 'Failed to load analytics data',
        details: err instanceof Error ? err.message : 'Unknown error',
        status: 500
      };
    } finally {
      this.loading = false;
    }
  }

  // Update charts with new data
  updateCharts(data: any): void {
    if (!data) return;

    // Update volume chart if data exists
    if (data.volumeData) {
      if (this.volumeChart) {
        this.volumeChart.updateOptions({
          series: [{
            name: 'Conversation Volume',
            data: data.volumeData
          }],
          xaxis: {
            categories: data.trendData?.categories ?? []
          }
        });
      }
    }

    // Update chat type chart if data exists
    if (data.chatTypeData && this.chatTypeChart) {
      try {
        const { series, labels } = data.chatTypeData;
        
        // Ensure we have valid data
        if (series && series.length > 0 && labels && labels.length > 0) {
          this.chatTypeChart.updateOptions({
            series: series,
            labels: labels,
            colors: this.getChatTypeColors(labels),
            chart: {
              animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
              }
            },
            noData: {
              text: 'No data available',
              align: 'center',
              verticalAlign: 'middle',
              offsetX: 0,
              offsetY: 0,
              style: {
                color: '#6c757d',
                fontSize: '14px',
                fontFamily: 'Inter, sans-serif'
              }
            }
          }, false, true); // The third parameter (true) is for redraw
          
          console.log('Chat type chart updated with data:', { series, labels });
        } else {
          console.warn('No valid chat type data to display');
        }
      } catch (e) {
        console.error('Error updating chat type chart:', e);
      }
    }
  }

  /**
   * Process chat type data for the chart
   */
  processChatTypeData(conversations: any[]): { series: number[]; labels: string[] } {
    // Count occurrences of each chat type
    const typeCounts = new Map<string, number>();
    
    // Initialize with all possible chat types
    Object.values(ChatType).forEach(type => {
      if (typeof type === 'string') { // Only process string values from enum
        typeCounts.set(type, 0);
      }
    });
    
    // Count actual occurrences
    conversations.forEach(conv => {
      if (conv.chatType && typeCounts.has(conv.chatType)) {
        typeCounts.set(conv.chatType, (typeCounts.get(conv.chatType) || 0) + 1);
      }
    });
    
    // Convert to arrays for the chart
    const labels: string[] = [];
    const series: number[] = [];
    
    // Maintain consistent order based on ChatType enum
    Object.values(ChatType).forEach(type => {
      if (typeof type === 'string') { // Only process string values from enum
        const count = typeCounts.get(type) || 0;
        if (count > 0) {
          labels.push(this.getChatTypeLabel(type));
          series.push(count);
        }
      }
    });
    
    // If no data, return empty arrays to prevent chart errors
    if (series.length === 0) {
      return { series: [1], labels: ['No Data'] };
    }
    
    return { series, labels };
  }
  
  /**
   * Process sentiment data for the chart
   */
  private processSentimentData(conversations: any[]): { series: number[]; labels: string[]; colors: string[] } {
    const sentimentCounts = {
      positive: 0,
      neutral: 0,
      negative: 0
    };
    
    // Count sentiment occurrences
    conversations.forEach(conv => {
      const sentiment = (conv.sentiment ?? 'neutral').toLowerCase();
      if (sentiment.includes('pos') || sentiment === 'positive') {
        sentimentCounts.positive++;
      } else if (sentiment.includes('neg') || sentiment === 'negative') {
        sentimentCounts.negative++;
      } else {
        sentimentCounts.neutral++;
      }
    });
    
    return {
      series: [sentimentCounts.positive, sentimentCounts.neutral, sentimentCounts.negative],
      labels: ['Positive', 'Neutral', 'Negative'],
      colors: ['#50CD89', '#F5F8FA', '#F1416C']
    };
  }
  
  /**
   * Get colors for chat type chart
   */
  private getChatTypeColors(labels: string[]): string[] {
    const colorMap: { [key: string]: string } = {
      'General': '#6993FF',
      'Unify': '#1BC5BD',
      'Community': '#8950FC',
      'Family': '#FFA800',
      'Knowledge': '#F64E60',
      'Dreams': '#F3F6F9',
      'Spirituality': '#181C32',
      'Unknown': '#E4E6EF'
    };
    
    return labels.map(label => colorMap[label] || '#E4E6EF');
  }

  /**
   * Handle tab changes between conversations and analytics
   */
  async onTabChange(selectedTab: 'conversations' | 'analytics' | 'userChatbot'): Promise<void> {
    if (this.currentTab === selectedTab) {
      return; // Don't do anything if clicking the same tab
    }
    
    // Update the current tab
    this.currentTab = selectedTab;
    
    try {
      // Small delay to ensure view is updated
      await new Promise(resolve => setTimeout(resolve, 0));
      
      if (selectedTab === 'analytics') {
        // Ensure we have data
        if (this.allConversations.length === 0) {
          await this.loadConversations();
        }
        // Log flagged user info when switching to analytics tab
        console.debug('[MISUSE FLAGGED USERS] [TAB CHANGE TO ANALYTICS] Current flagged user IDs:', this.flaggedUserIds);
        console.debug('[MISUSE FLAGGED USERS] [TAB CHANGE TO ANALYTICS] Current flagged user count:', this.flaggedUserIds.length);
        // Initialize charts with DOM readiness delay
        setTimeout(() => {
          this.initCharts();
        }, 50);
      }
    } catch (error) {
      console.error('Error during tab change:', error);
    }
  }

  /**
   * Handle date range change for analytics
   */
  onAnalyticsDateRangeChange(): void {
    if (this.analyticsDateRange === 'custom') {
      // Handle custom date range selection
      // You can implement a date picker modal here
      return;
    }
    
    // Reload data with the new date range
    this.loadData();
  }

  /**
   * Handle date range change for conversations
   */
  onDateRangeChange(): void {
    if (this.filterForm) {
      const formValue = this.filterForm.value;
      if (formValue.searchStartDate && formValue.searchEndDate) {
        this.dateRange = { 
          start: formValue.searchStartDate, 
          end: formValue.searchEndDate 
        };
        this.currentPage = 1;
        this.loadConversations(true);
      }
    }
  }

  // Clear date range selection
  clearDateRange(): void {
    this.filterForm.patchValue({
      searchStartDate: null,
      searchEndDate: null
    });
    this.dateRange = { start: null, end: null };
    this.loadData();
  }

  // Chat type change handler - now handled by form
  onChatTypeChange(types: string[]): void {
    // Chat type selection is now handled by the form and applied with applyFilters
  }

  // Search handler - now handled by applyFilters
  onSearch(): void {
    // Search is now handled by the form and applied with applyFilters
  }

  // Handle sort change
  onSortChange(sortBy: string): void {
    this.sortBy = sortBy;
    this.currentPage = 1;
    
    // Map sort option to field and direction
    switch(sortBy) {
      case 'Most Recent':
        this.sortField = 'timestamp';
        this.sortDirection = 'desc';
        break;
      case 'Oldest':
        this.sortField = 'timestamp';
        this.sortDirection = 'asc';
        break;
      case 'User Name':
        this.sortField = 'userName';
        this.sortDirection = 'asc';
        break;
    }
    
    // Apply the filters with the new sorting
    this.applyFilters();
  }

  // Update KPIs based on current filters
  private async updateKPIs(): Promise<void> {
    try {
      // Use the already loaded conversations to ensure consistency with the table
      const filteredConversations = this.allConversations.filter(conv => {
        // Apply the same filters as in loadConversations
        if (this.selectedChatType && conv.chatType !== this.selectedChatType) {
          return false;
        }

        if (this.searchQuery) {
          const searchLower = this.searchQuery.toLowerCase();
          const matchesSearch = conv.messages?.some(msg =>
            msg.content?.toLowerCase().includes(searchLower)
          );
          if (!matchesSearch) return false;
        }

        if (this.dateRange.start || this.dateRange.end) {
          const convDate = new Date(conv.timestamp).getTime();
          const startDate = this.dateRange.start ? new Date(this.dateRange.start).setHours(0, 0, 0, 0) : -Infinity;
          const endDate = this.dateRange.end ? new Date(this.dateRange.end).setHours(23, 59, 59, 999) : Infinity;

          if (convDate < startDate || convDate > endDate) return false;
        }

        return true;
      });

      // Calculate KPIs from the filtered conversations
      const uniqueUsers = new Set(filteredConversations.map(conv => conv.userId));
      const flaggedUsers = filteredConversations.filter(conv =>
        conv.messages?.some(msg => msg.isDeleted || false)
      ).length;

      this.kpis = {
        totalConversations: filteredConversations.length,
        uniqueUsers: uniqueUsers.size,
        flaggedUsers
      };
    } catch (error) {
      console.error('Error updating KPIs:', error);
    }
  }

  // Handle page change for pagination
  onPageChange(page: number): void {
    if (page < 1 || page > this.totalPages || page === this.currentPage) {
      return;
    }

    this.currentPage = page;
    this.updateDisplayedConversations();

    // Optional: Scroll to top of the table
    const tableElement = document.querySelector('.table-responsive');
    if (tableElement) {
      tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  // Chat type label mapping
  private readonly chatTypeLabels: Record<string, string> = {
    [ChatType.UNIFY]: 'Unify',
    [ChatType.COMMUNITY]: 'Community',
    [ChatType.FAMILY]: 'Family',
    [ChatType.KNOWLEDGE]: 'Knowledge',
    [ChatType.DREAMS]: 'Dreams',
    [ChatType.SPIRITUALITY]: 'Spirituality',
    'UNIFY': 'Unify',
    'COMMUNITY': 'Community',
    'FAMILY': 'Family',
    'KNOWLEDGE': 'Knowledge',
    'DREAMS': 'Dreams',
    'SPIRITUALITY': 'Spirituality'
  };

  // Get display label for chat type
  getChatTypeLabel(type: string): string {
    return this.chatTypeLabels[type] || type || 'Unknown';
  }
  error: ErrorDetails | null = null;
  conversations: Conversation[] = [];
  formattedConversations: any[] = [];
  // KPIs for the analytics dashboard
  kpis: ChatAnalyticsKPIs = {
    totalConversations: 0,
    uniqueUsers: 0,
    flaggedUsers: 0,
    totalMessages: 0,
    avgResponseTime: 0,
    satisfactionRate: 0,
    successRate: 0
  };

  // Table data
  tableColumns: TableColumn[] = [
    {
      key: 'timestamp',
      label: 'Date/Time',
      sortable: false, // Disable sorting on date/time column
      width: '180px',
      render: (item) => {
        if (!item.timestamp) return '-';
        const date = new Date(item.timestamp);
        if (isNaN(date.getTime())) return '-';

        const months = ['January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'];

        const month = months[date.getMonth()];
        const day = date.getDate();
        const year = date.getFullYear();

        let hours = date.getHours();
        const ampm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12;
        hours = hours ? hours : 12; // the hour '0' should be '12'
        const minutes = date.getMinutes().toString().padStart(2, '0');

        return `${month} ${day}, ${year} ${hours}:${minutes} ${ampm}`;
      }
    },
    {
      key: 'user',
      label: 'User',
      sortable: false, // Disable sorting on user column
      width: '200px',
      render: (item) => {
        return `
          <div class="d-flex align-items-center">
            <div class="symbol symbol-circle symbol-35px me-3">
              <div class="symbol-label bg-light-primary">
                <span class="text-primary fs-7 fw-bold">${(item.userName ?? '?').charAt(0).toUpperCase()}</span>
              </div>
            </div>
            <div class="d-flex flex-column">
              <span class="text-gray-800 text-hover-primary fw-bold text-nowrap overflow-hidden text-ellipsis" style="max-width: 150px;">
                ${item.userName ?? 'Unknown User'}
              </span>
              <div style="display: inline-block; margin-top: 0.25rem;">
                <span class="${this.getChatTypeBadgeClass(item.chatType)} fs-8 fw-bold" style="display: inline-block; padding: 0.25em 0.5em; border-radius: 0.25rem; line-height: 1.2;">
                  ${this.getChatTypeLabel(item.chatType)}
                </span>
              </div>
            </div>
          </div>
        `;
      }
    },
    { key: 'message', label: 'Message', sortable: false }
  ];

  // Pagination & Sorting
  currentPage = 1;

  // Using Metronic's built-in spinner animation
  refreshAnimation = false;

  // Metronic theme classes
  cardClasses = 'card card-flush mb-6';
  tableClasses = 'table table-row-dashed align-middle fs-6 gy-5';
  badgeClasses = {
    primary: 'badge-light-primary',
    success: 'badge-light-success',
    warning: 'badge-light-warning',
    danger: 'badge-light-danger',
    info: 'badge-light-info'
  };
  ITEMS_PER_PAGE = 10;
  totalItems = 0;
  showPagination = true;
  allConversations: Conversation[] = [];
  isInitialLoad = true;
  sortField = 'timestamp';
  sortDirection: 'asc' | 'desc' = 'desc';

  // Chat type filter with colors for badges
  chatTypes: ChatTypeOption[] = [
    { value: '', label: 'All Types', badgeClass: 'badge-light-secondary' },
    { value: ChatType.UNIFY, label: 'Unify', badgeClass: 'badge-light-primary' },
    { value: ChatType.COMMUNITY, label: 'Community', badgeClass: 'badge-light-success' },
    { value: ChatType.FAMILY, label: 'Family', badgeClass: 'badge-light-info' },
    { value: ChatType.KNOWLEDGE, label: 'Knowledge', badgeClass: 'badge-light-warning' },
    { value: ChatType.DREAMS, label: 'Dreams', badgeClass: 'badge-light-danger' },
    { value: ChatType.SPIRITUALITY, label: 'Spirituality', badgeClass: 'badge-light-dark' }
  ];

  // Default chat type if not specified
  defaultChatType = ChatType.UNIFY;

  /**
   * Get count of conversations for a specific chat type
   * @param chatType The chat type to count
   * @returns Number of conversations for the specified type
   */
  getChatTypeCount(chatType: string): number {
    if (!this.conversations || this.conversations.length === 0) {
      return 0;
    }
    
    // If empty string or null/undefined, return total count
    if (!chatType) {
      return this.conversations.length;
    }
    
    // Count conversations matching the specified chat type
    return this.conversations.filter(conv => conv.chatType === chatType).length;
  }

  /**
   * Get badge class for chat type
   * @param chatType The chat type to get badge class for
   * @returns CSS class for the badge
   */
  getChatTypeBadgeClass(chatType: string): string {
    if (!chatType) return 'badge badge-light-secondary';
    const type = this.chatTypeOptions.find(t => t.value === chatType);
    return type?.badgeClass || 'badge badge-light-secondary';
  }
  selectedChatType = '';

  // Cleanup
  private readonly destroy$ = new Subject<void>();

  /**
   * Toggle the filter panel visibility
   */
  toggleFilterPanel(): void {
    this.showFilterPanel = !this.showFilterPanel;
  }

  /**
   * Clear all filters and reset to defaults
   */
  clearFilters(): void {
    // Reset form values
    this.filterForm.reset({
      searchStartDate: null,
      searchEndDate: null,
      chatType: '',
      search: '',
      userId: ''
    });

    // Clear all filter values
    this.dateRange = { start: null, end: null };
    this.searchQuery = '';
    this.selectedChatType = '';
    this.selectedUser = '';
    this.currentPage = 1;

    // Reload data with cleared filters
    this.loadConversations(true);
  }

  /**
   * Handle column sorting
   */
  onSort(column: { key: string; sortable?: boolean }): void {
    if (!column.sortable) return;

    if (this.sortField === column.key) {
      // Toggle sort direction if clicking the same column
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // Default to descending for timestamp, ascending for other fields
      this.sortField = column.key;
      this.sortDirection = column.key === 'timestamp' ? 'desc' : 'asc';
    }

    this.applyFilters();
  }

  // Date utility functions
  formatDate = formatDateForDisplay;
  formatInputDate = formatDateForInput;

  // Make Math available in template
  Math = Math;

  /**
   * Initialize all charts
   */
  private initCharts(): void {
    // Destroy existing charts first
    this.destroyCharts();

    // Initialize each chart with error handling
    const initChart = (chartInitFn: () => void, chartName: string) => {
      try {
        chartInitFn();
      } catch (error) {
        console.error(`Error initializing ${chartName}:`, error);
      }
    };

    // Initialize charts with DOM check and error handling
    const initChartsWithDelay = () => {
      if (this.volumeChartEl?.nativeElement) {
        initChart(() => this.initVolumeChart(), 'volume chart');
      }
      if (this.chatTypeChartEl?.nativeElement) {
        initChart(() => this.initChatTypeChart(), 'chat type chart');
      }
      if (this.sentimentChartEl?.nativeElement) {
        initChart(() => this.initSentimentChart(), 'sentiment chart');
      }
    };

    // Small delay to ensure DOM is ready
    setTimeout(initChartsWithDelay, 50);
  }

  /**
   * Destroy all charts to prevent memory leaks
   */
  private destroyCharts(): void {
    [this.volumeChart, this.chatTypeChart, this.sentimentChart /*, this.trendChart*/].forEach(chart => {
      if (chart) {
        chart.destroy();
      }
    });
    this.volumeChart = null;
    this.chatTypeChart = null;
    this.sentimentChart = null;
  }

  /**
   * Initialize conversation volume chart
   */
  private initVolumeChart(): void {
    try {
      // Sample data - replace with actual data from your service
      const data = this.processVolumeData(this.allConversations);
      
      const options = {
        series: [{
          name: 'Conversations',
          data: data.values
        }],
        chart: {
          type: 'area',
          height: 300,
          toolbar: { show: false },
          zoom: { enabled: false },
          fontFamily: 'Inter',
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800
          }
        },
        colors: ['#7239EA'],
        dataLabels: { enabled: false },
        stroke: {
          curve: 'smooth',
          width: 2,
          lineCap: 'round'
        },
        fill: {
          type: 'gradient',
          gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.1,
            stops: [0, 80, 100]
          }
        },
        xaxis: {
          categories: data.categories,
          axisBorder: { show: false },
          axisTicks: { show: false },
          labels: {
            style: {
              colors: '#A1A5B7',
              fontSize: '12px',
              fontFamily: 'Inter'
            }
          },
          tooltip: {
            enabled: false
          }
        },
        yaxis: {
          labels: {
            style: {
              colors: '#A1A5B7',
              fontSize: '12px',
              fontFamily: 'Inter'
            },
            formatter: (value: number) => { return value % 1 === 0 ? value.toString() : ''; }
          }
        },
        grid: {
          borderColor: '#EBEDF3',
          strokeDashArray: 4,
          yaxis: { lines: { show: true } },
          padding: {
            top: 0,
            right: 10,
            bottom: 0,
            left: 10
          }
        },
        tooltip: {
          enabled: true,
          theme: 'light',
          x: { format: 'dd MMM yyyy' },
          style: {
            fontSize: '12px',
            fontFamily: 'Inter'
          },
          y: {
            formatter: (value: number) => `${value} conversations`
          }
        }
      };

      this.volumeChart = new ApexCharts(this.volumeChartEl.nativeElement, options);
      this.volumeChart.render();
    } catch (error) {
      console.error('Error initializing volume chart:', error);
    }
  }

  /**
   * Process conversation data for volume chart
   */
  private processVolumeData(conversations: any[]): { values: number[]; categories: string[] } {
    // Group conversations by day
    const dailyCounts = new Map<string, number>();
    const now = new Date();
    
    // Initialize last 7 days
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      dailyCounts.set(dateStr, 0);
    }
    
    // Count conversations per day
    conversations.forEach(conv => {
      if (conv.timestamp) {
        const date = new Date(conv.timestamp);
        const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        dailyCounts.set(dateStr, (dailyCounts.get(dateStr) || 0) + 1);
      }
    });
    
    // Convert to arrays for chart
    const categories = Array.from(dailyCounts.keys());
    const values = Array.from(dailyCounts.values());
    
    return { values, categories };
  }

  /**
   * Apply current filters to the cached data
   */
  private async applyFilters(): Promise<void> {
    if (!this.isDataLoaded || !this.filterForm) {
      return;
    }
    // Show loading state
    this.loading = true;
    try {
      const { searchStartDate, searchEndDate, chatType, search, userId } = this.filterForm.value;
      // Start with all conversations
      let filteredConversations = [...this.allConversationsCache];
      // Apply chat type filter
      if (chatType) {
        filteredConversations = filteredConversations.filter(
          conv => conv.chatType === chatType
        );
      }

      // Apply user filter
      if (userId) {
        filteredConversations = filteredConversations.filter(
          conv => conv.userId === userId
        );
      }

      // Apply date range filter
      if (searchStartDate && searchEndDate) {
        const startDate = new Date(searchStartDate).setHours(0, 0, 0, 0);
        const endDate = new Date(searchEndDate).setHours(23, 59, 59, 999);
        
        filteredConversations = filteredConversations.filter(conv => {
          const convDate = new Date(conv.timestamp).getTime();
          return convDate >= startDate && convDate <= endDate;
        });
      }

      // Apply search query last as it's the most expensive operation
      if (search?.trim()) {
        const searchLower = search.trim().toLowerCase();
        
        // First, find all conversation IDs that match the search
        const matchingConversationIds = new Set<string>();
        
        // Find all conversations with matching messages (partial word match)
        this.allConversationsCache.forEach(conv => {
          const hasMatch = conv.messages.some(msg => {
            const messageContent = (msg.message || msg.content || '').toLowerCase();
            // Split search term into words and check if any word is contained in the message
            return messageContent.split(/\s+/).some(word => 
              word.includes(searchLower) || searchLower.includes(word)
            );
          });
          
          if (hasMatch) {
            matchingConversationIds.add(conv.id);
          }
        });
        
        // Filter to only include conversations with matching messages
        filteredConversations = filteredConversations.filter(
          conv => matchingConversationIds.has(conv.id)
        );
      }

      const sortedConversations = [...filteredConversations].sort((a, b) => {
        let valueA, valueB;
        
        // Handle different sort fields
        if (this.sortField === 'timestamp') {
          valueA = new Date(a.timestamp).getTime();
          valueB = new Date(b.timestamp).getTime();
        } else if (this.sortField === 'userName') {
          // Combine givenName and familyName for sorting
          const nameA = `${a.userData?.givenName || ''} ${a.userData?.familyName || ''}`.trim().toLowerCase();
          const nameB = `${b.userData?.givenName || ''} ${b.userData?.familyName || ''}`.trim().toLowerCase();
          valueA = nameA || 'zzz'; // Default to 'zzz' if no name to push to bottom
          valueB = nameB || 'zzz'; // Default to 'zzz' if no name to push to bottom
        } else {
          // Default to timestamp if sort field is not recognized
          valueA = new Date(a.timestamp).getTime();
          valueB = new Date(b.timestamp).getTime();
        }
        
        // Apply sort direction
        if (valueA < valueB) {
          return this.sortDirection === 'asc' ? -1 : 1;
        }
        if (valueA > valueB) {
          return this.sortDirection === 'asc' ? 1 : -1;
        }
        return 0;
      });
      
      this.allConversations = sortedConversations;
      this.conversations = sortedConversations;
      this.updateDisplayedConversations();
      await this.updateKPIs();
    } catch (error) {
      console.error('Error applying filters:', error);
      this.error = {
        message: 'Failed to apply filters',
        details: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    } finally {
      // Always ensure loading is false
      this.loading = false;
    }
  }

  // Store all conversations for local filtering
  private allConversationsCache: Conversation[] = [];
  private isDataLoaded = false;

  async loadConversations(forceRefresh: boolean = false): Promise<void> {
    // Don't start a new load if already loading
    if (this.loading) {
      return;
    }
    // Only fetch from server if we don't have data or force refresh is true
    if (forceRefresh || !this.isDataLoaded) {
      this.loading = true;
      this.error = null;
      // Clear any existing data
      this.allConversations = [];
      this.formattedConversations = [];
      this.totalItems = 0;
      this.cdr.detectChanges();
      try {
        // Format dates for API
        const startDate = this.dateRange.start ? formatDateForApi(this.dateRange.start) : '';
        const endDate = this.dateRange.end ? formatDateForApi(this.dateRange.end) : '';
        const { conversations: allConversations } = await this.chatAnalyticsService.fetchConversations({
          startDate: startDate || undefined,
          endDate: endDate || undefined,
          page: 1,
          pageSize: 50000 // Get all conversations for the date range
        });
        if (allConversations && allConversations.length > 0) {
          this.allConversationsCache = allConversations;
          this.isDataLoaded = true;
          // Calculate flagged users based on misuse detection (riskScore >= 70)
          const misuseMetrics = this.misuseDetectionService.analyzeConversations(allConversations);
          const flagged = misuseMetrics.filter(m => m.metrics.riskScore >= 70);
          this.flaggedUserIds = flagged.map(m => m.userId);
          this.kpis.flaggedUsers = this.flaggedUserIds.length;
          console.debug('[MISUSE FLAGGED USERS] [CONVERSATION LOAD] Set kpis.flaggedUsers =', this.kpis.flaggedUsers);
          // --- DEBUG LOGS FOR FLAGGED COUNT ISSUE ---
          console.debug('[MISUSE FLAGGED USERS] [CONVERSATION LOAD] All misuse metrics:', misuseMetrics);
          console.debug('[MISUSE FLAGGED USERS] [CONVERSATION LOAD] Flagged user metrics:', flagged);
          console.debug('[MISUSE FLAGGED USERS] [CONVERSATION LOAD] Flagged user IDs:', this.flaggedUserIds);
          console.debug('[MISUSE FLAGGED USERS] [CONVERSATION LOAD] Flagged user count:', this.flaggedUserIds.length);
          console.debug('[MISUSE FLAGGED USERS] [CONVERSATION LOAD] All conversations count:', allConversations.length);
          console.debug('[MISUSE FLAGGED USERS] [CONVERSATION LOAD] Sample conversation user IDs:', allConversations.slice(0, 10).map(c => c.userId));
          // --- END DEBUG LOGS ---
          // Apply filters which will update the display
          await this.applyFilters();
          // After filters, ensure KPI is in sync
          this.kpis.flaggedUsers = this.flaggedUserIds.length;
          console.debug('[MISUSE FLAGGED USERS] [CONVERSATION LOAD][POST-FILTER] Set kpis.flaggedUsers =', this.kpis.flaggedUsers);
          // Ensure UI is updated
          this.cdr.markForCheck();
          this.cdr.detectChanges();
        } else {
          // No data case
          this.allConversations = [];
          this.formattedConversations = [];
          this.totalItems = 0;
          this.isDataLoaded = true;
          this.cdr.markForCheck();
          this.cdr.detectChanges();
        }
      } catch (err: any) {
        // Format the error for better display
        this.error = {
          message: err.message ?? 'Failed to load conversations. Please try again.',
          details: (err.error?.message ?? err.statusText) ?? 'Unknown error',
          status: err.status ?? 0
        };
        console.error('Error loading conversations:', err);
      } finally {
        this.loading = false;
        this.cdr.detectChanges();
      }
    } else {
      // If data is already loaded, just apply the current filters
      await this.applyFilters();
    }
  }

  // Update displayed conversations based on current pagination
  private updateDisplayedConversations(): void {
    if (!this.isDataLoaded) return;
    
    // First format all conversations
    const allFormatted = this.formatConversations(this.allConversations);
    this.totalItems = allFormatted.length;

    // Then apply pagination to the formatted conversations
    const startIndex = (this.currentPage - 1) * this.ITEMS_PER_PAGE;
    const endIndex = startIndex + this.ITEMS_PER_PAGE;

    this.formattedConversations = allFormatted.slice(startIndex, endIndex);
    this.showPagination = this.totalItems > this.ITEMS_PER_PAGE;
    
    // Update last updated timestamp
    this.lastUpdated = getCurrentTimestamp();
  }

  onItemsPerPageChange(itemsPerPage: number): void {
    this.ITEMS_PER_PAGE = itemsPerPage;
    this.currentPage = 1; // Reset to first page
    this.loadConversations();
  }

  // Calculate total pages for pagination display
  get totalPages(): number {
    return Math.max(1, Math.ceil(this.totalItems / this.ITEMS_PER_PAGE));
  }

  // Generate page numbers for pagination with ellipsis
  getPageNumbers(): number[] {
    const total = this.totalPages;
    const current = this.currentPage;
    const delta = 2; // Number of pages to show around current page
    const range: number[] = [];
    const rangeWithDots: number[] = [];
    let l: number;

    // Always show first and last page numbers
    range.push(1);

    // Add pages around current page
    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
      range.push(i);
    }

    // Add last page if not already included
    if (total > 1) {
      range.push(total);
    }

    // Add ellipsis where needed
    for (const num of range) {
      if (l) {
        if (num - l > 1) {
          rangeWithDots.push(-1); // -1 represents ellipsis
        }
      }
      rangeWithDots.push(num);
      l = num;
    }

    return rangeWithDots;
  }

  onRefresh(): void {
    this.isInitialLoad = true; // Force reload all data
    this.loadConversations(true);
  }

  // Handle conversation row click
  onConversationClick(conversation: any): void {
    console.log('Conversation clicked:', conversation);
    // Implement conversation click handler
  }

  // Track conversation by ID for ngFor
  trackByConversationId(index: number, item: any): string {
    return item.id ?? `conv-${index}`;
  }

  // Retry loading conversations on error
  onRetry(): void {
    this.loadConversations();
  }

  // Handle user filter change in conversations tab
  onUserFilterChange(userId: string): void {
    console.log('User filter changed:', userId);
    if (this.filterForm) {
      // Only update the form value, don't apply filters yet
      this.filterForm.get('userId')?.setValue(userId, { emitEvent: false });
      console.log('Filter form updated with user ID:', this.filterForm.get('userId')?.value);
      // Removed applyFilters() call - will be handled by the Apply button
    }
  }

  // Handle user filter change in analytics tab
  onAnalyticsUserFilterChange(userId: string): void {
    this.selectedUser = userId;
    // Reload analytics data with the selected user filter
    this.loadData();
  }

  // Clean up subscriptions
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Using formatDateForDisplay from date.utils.ts
  // formatDate is already defined elsewhere in the component

  /**
   * Format conversation data for the table
   */
  // Format conversation data for the table
  private isValidChatType(chatType: string | undefined): chatType is ChatType {
    if (!chatType) return false;
    return Object.values(ChatType).includes(chatType as ChatType);
  }

  private formatConversations(conversations: Conversation[]): any[] {
    // First, format all conversations without sorting
    const formatted = conversations
      .filter(conv => conv.messages && conv.messages.length > 0)
      .flatMap(conv => {
        // Get all messages sorted by timestamp
        const sortedMessages = [...conv.messages].sort((a, b) =>
          new Date(getMessageTime(a)).getTime() - new Date(getMessageTime(b)).getTime()
        );

        const userData = conv.userData || {};
        const userName = `${userData.givenName || ''} ${userData.familyName || ''}`.trim() || 'Unknown User';
        const conversationPairs = [];
        let currentUserMessage = null;

        // Process messages in order to pair questions and answers
        for (const msg of sortedMessages) {
          const role = (msg.role || '').toLowerCase();

          if (role === 'user' || role === 'human') {
            // If we have a pending user message without a response, add it
            if (currentUserMessage) {
              conversationPairs.push({
                ...currentUserMessage,
                response: null
              });
            }
            currentUserMessage = {
              message: msg.content || msg.message || '',
              timestamp: getMessageTime(msg),
              messageId: msg.id
            };
          }
          // Check for assistant/ai/bot/system response
          else if ((role === 'assistant' || role === 'ai' || role === 'bot' || role === 'system' || role === 'asistant') && currentUserMessage) {
            conversationPairs.push({
              ...currentUserMessage,
              response: msg.content || msg.message || '',
              responseId: msg.id,
              responseTimestamp: getMessageTime(msg)
            });
            currentUserMessage = null;
          }
        }

        // Add the last user message if it doesn't have a response
        if (currentUserMessage) {
          conversationPairs.push({
            ...currentUserMessage,
            response: null
          });
        }

        // Map to the final format
        return conversationPairs.map((pair, index) => ({
          id: `${conv.id}-${index}`,
          conversationId: conv.id,
          userId: conv.userId,
          userName,
          message: pair.message,
          response: pair.response,
          chatType: this.isValidChatType(conv.chatType) ? conv.chatType : this.defaultChatType,
          timestamp: formatDateForDisplay(pair.timestamp),
          createdAt: pair.timestamp,
          messageTimestamp: pair.timestamp,
          responseTimestamp: pair.responseTimestamp,
          messageId: pair.messageId,
          responseId: pair.responseId,
          hasResponse: !!pair.response,
          messages: conv.messages
        }));
      });
    
    // Don't sort here - we'll handle sorting in applyFilters
    return formatted.slice(0, 1000); // Still limit to 1000 records for performance
  }

  flaggedUserIds: string[] = [];

  onFlaggedUsersChange(flagged: string[]) {
    this.flaggedUserIds = flagged;
    this.kpis.flaggedUsers = flagged.length;
    console.debug('[MISUSE FLAGGED USERS] [ANALYTICS TAB EVENT] Set kpis.flaggedUsers =', this.kpis.flaggedUsers);
    // --- DEBUG LOGS FOR FLAGGED COUNT ISSUE (ANALYTICS TAB) ---
    console.debug('[MISUSE FLAGGED USERS] [ANALYTICS TAB EVENT] Flagged user IDs:', flagged);
    console.debug('[MISUSE FLAGGED USERS] [ANALYTICS TAB EVENT] Flagged user count:', flagged.length);
    if (this.allConversations && this.allConversations.length > 0) {
      console.debug('[MISUSE FLAGGED USERS] [ANALYTICS TAB EVENT] All conversations count:', this.allConversations.length);
      console.debug('[MISUSE FLAGGED USERS] [ANALYTICS TAB EVENT] Sample conversation user IDs:', this.allConversations.slice(0, 10).map(c => c.userId));
    }
    // --- END DEBUG LOGS ---
  }
}
