import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgApexchartsModule } from 'ng-apexcharts';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MisuseDetectionService } from './services/misuse-detection.service';

// Material modules
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatOptionModule } from '@angular/material/core';

// Custom components
import { ChatbotAnalyticsComponent } from './chatbot-analytics.component';
import { ChatbotAnalyticsRoutingModule } from './chatbot-analytics-routing.module';
import { TableCardComponent } from './shared/table-card/table-card.component';
import { LayoutModule } from '../../_metronic/layout/layout.module';
import { ChatTypePipe } from './pipes/chat-type.pipe';
import { UserSearchDropdownComponent } from '../../shared/formcontrol/user-search-dropdown/user-search-dropdown.component';
import { MisuseDetectComponent } from './components/misuse-detection/misuse-detection.component';
import { UserChatbotComponent } from '../chatbot-prompts-list/shared/user-chatbot/user-chatbot.component';

@NgModule({
  declarations: [
    ChatbotAnalyticsComponent,
    TableCardComponent,
    ChatTypePipe,
    MisuseDetectComponent
  ],
  imports: [
    CommonModule,
    ChatbotAnalyticsRoutingModule,
    NgApexchartsModule,
    FormsModule,
    ReactiveFormsModule,
    LayoutModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatOptionModule,
    UserSearchDropdownComponent,
    UserChatbotComponent
  ],
  providers: [MisuseDetectionService],
  exports: [ChatTypePipe, MisuseDetectComponent],
})
export class ChatbotAnalyticsModule {}
