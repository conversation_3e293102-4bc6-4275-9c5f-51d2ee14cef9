<!--begin::Card-->
<div class="card border border-dashed border-gray-300 mb-5">
  <!--begin::Card header-->
  <div class="card-header border-0 pt-6">
    <!--begin::Card title-->
    <div class="card-title">
      <h3 class="fw-bold m-0">Misuse Detection Analytics</h3>
    </div>
    <!--end::Card title-->
  </div>
  <!--end::Card header-->

  <!--begin::Card body-->
  <div class="card-body pt-0">
    <!--begin::Loading-->
    <div *ngIf="loading" class="d-flex justify-content-center py-10">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
    <!--end::Loading-->

    <!--begin::No data-->
    <div *ngIf="!loading && metrics.length === 0" class="text-center py-10">
      <div class="text-muted fw-semibold">No conversation data available for analysis</div>
    </div>
    <!--end::No data-->

    <!--begin::User list-->
    <div *ngIf="!loading && metrics.length > 0" class="table-responsive">
      <table class="table align-middle table-row-dashed fs-6 gy-5">
        <!--begin::Table head-->
        <thead>
          <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
            <th class="min-w-150px">User</th>
            <th class="min-w-100px">Messages</th>
            <th class="min-w-100px">Sessions</th>
            <th class="min-w-100px">Duplicates</th>
            <th class="min-w-100px">Fallback Rate</th>
            <th class="min-w-100px">Risk Score</th>
            <th class="min-w-50px"></th>
          </tr>
        </thead>
        <!--end::Table head-->

        <!--begin::Table body-->
        <tbody class="fw-semibold text-gray-600">
          <ng-container *ngFor="let user of paginatedMetrics">
            <!--begin::User row-->
            <tr (click)="selectUser(user.userId)" style="cursor: pointer;">
              <td>
                <div class="d-flex align-items-center">
                  <div class="symbol symbol-35px symbol-circle me-3">
                    <span class="symbol-label bg-light-primary">
                      <span class="text-primary fw-bold">{{ (user.userName || 'U').charAt(0).toUpperCase() }}</span>
                    </span>
                  </div>
                  <div class="d-flex flex-column">
                    <span class="text-gray-800 fw-bold">{{ user.userName }}</span>
                    <span *ngIf="user.email" class="text-muted">{{ user.email }}</span>
                  </div>
                </div>
              </td>
              <td>
                <div class="d-flex flex-column">
                  <span class="fw-bold">{{ user.metrics.messagesPerMinute | number:'1.1-1' }}/min</span>
                  <span class="text-muted">{{ user.metrics.totalMessages | number }} total</span>
                </div>
              </td>
              <td>
                <div class="d-flex flex-column">
                  <span class="fw-bold">{{ user.metrics.sessionCount | number }}</span>
                  <span class="text-muted">{{ user.metrics.avgSessionLength | number:'1.1-1' }} msgs/session</span>
                </div>
              </td>
              <td>
                <div class="d-flex flex-column">
                  <span class="fw-bold">{{ user.metrics.duplicateMessages | number }}</span>
                  <span class="text-muted">{{ (user.metrics.duplicateMessageRatio * 100) | number:'1.0-0' }}%</span>
                </div>
              </td>
              <td>
                <div class="d-flex flex-column">
                  <span class="fw-bold">{{ (user.metrics.fallbackRate * 100) | number:'1.0-0' }}%</span>
                  <span class="text-muted">{{ (user.metrics.avgConfidence * 100) | number:'1.0-0' }}% avg confidence</span>
                </div>
              </td>
              <td>
                <span class="badge {{ getRiskBadgeClass(user.metrics.riskLevel) }} fs-7 fw-bold">
                  {{ user.metrics.riskScore }}/100 - {{ getRiskText(user.metrics.riskLevel) }}
                </span>
              </td>
              <td class="text-end">
                <i class="ki-duotone ki-arrow-down fs-2" [class.rotate-180]="selectedUserId === user.userId">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </td>
            </tr>
            <!--end::User row-->

            <!--begin::Details row-->
            <tr *ngIf="selectedUserId === user.userId" class="bg-light">
              <td colspan="7" class="p-0">
                <div class="p-5">
                  <h4 class="mb-4">Detailed Analysis for {{ user.userName }}</h4>
                  
                  <!--begin::Issues-->
                  <div *ngIf="user.flaggedIssues.length > 0" class="mb-6">
                    <h5 class="text-danger mb-3">Flagged Issues</h5>
                    <div class="d-flex flex-wrap gap-2">
                      <span *ngFor="let issue of user.flaggedIssues" class="badge badge-light-danger">
                        {{ issue }}
                      </span>
                    </div>
                  </div>
                  <!--end::Issues-->
                  
                  <!--begin::Tabs-->
                  <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                    <li class="nav-item">
                      <a class="nav-link {{ activeTab === 'engagement' ? 'active' : '' }}" 
                         (click)="setActiveTab('engagement')"
                         style="cursor: pointer;">
                        Engagement Metrics
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link {{ activeTab === 'session' ? 'active' : '' }}" 
                         (click)="setActiveTab('session')"
                         style="cursor: pointer;">
                        Session Analysis
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link {{ activeTab === 'message' ? 'active' : '' }}" 
                         (click)="setActiveTab('message')"
                         style="cursor: pointer;">
                        Message Metrics
                      </a>
                    </li>
                  </ul>
                  <!--end::Tabs-->
                  
                  <!--begin::Metrics-->
                  <div class="row g-5" *ngIf="activeTab === 'engagement'">
                    <!-- Engagement Metrics Content -->
                    <div class="col-12">
                      <div class="card card-bordered">
                        <div class="card-header">
                          <h5 class="card-title">Engagement Metrics</h5>
                        </div>
                        <div class="card-body">
                          <div class="row g-6">
                            <div class="col-md-4">
                              <div class="d-flex flex-column text-center p-4 bg-light rounded">
                                <span class="text-muted">Avg. Input Length</span>
                                <span class="fs-2 fw-bold text-primary">{{ user.metrics.avgInputLength | number:'1.0-0' }} chars</span>
                              </div>
                            </div>
                            <div class="col-md-4">
                              <div class="d-flex flex-column text-center p-4 bg-light rounded">
                                <span class="text-muted">Avg. Response Length</span>
                                <span class="fs-2 fw-bold text-success">{{ user.metrics.avgResponseLength | number:'1.0-0' }} chars</span>
                              </div>
                            </div>
                            <div class="col-md-4">
                              <div class="d-flex flex-column text-center p-4 bg-light rounded">
                                <span class="text-muted">Engagement Score</span>
                                <span class="fs-2 fw-bold" [ngClass]="{
                                  'text-danger': user.metrics.engagementScore < 0.5,
                                  'text-warning': user.metrics.engagementScore >= 0.5 && user.metrics.engagementScore < 1,
                                  'text-success': user.metrics.engagementScore >= 1
                                }">
                                  {{ user.metrics.engagementScore | number:'1.2-2' }}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="row g-5" *ngIf="activeTab === 'session'">
                    <!-- Session Analysis Content -->
                    <div class="col-12">
                      <div class="card card-bordered h-100">
                        <div class="card-header">
                          <h5 class="card-title">Session Analysis</h5>
                        </div>
                        <div class="card-body">
                          <div class="d-flex flex-column gap-4">
                            <div>
                              <div class="d-flex justify-content-between mb-1">
                                <span class="text-muted">Session Count</span>
                                <span class="fw-bold">{{ user.metrics.sessionCount | number }}</span>
                              </div>
                              <div class="d-flex justify-content-between mb-1">
                                <span class="text-muted">Avg. Session Length</span>
                                <span class="fw-bold">{{ user.metrics.avgSessionLength | number:'1.1-1' }} messages</span>
                              </div>
                              <div class="d-flex justify-content-between mb-1">
                                <span class="text-muted">Total Messages</span>
                                <span class="fw-bold">{{ user.metrics.totalMessages | number }}</span>
                              </div>
                              <div class="text-muted fs-8 mt-3">
                                Minimum expected: {{ user.metrics.minSessionLength }} messages/session
                              </div>
                            </div>
                            
                            <div>
                              <div class="d-flex justify-content-between mb-1">
                                <span class="text-muted">Fallback Rate</span>
                                <span class="fw-bold">{{ user.metrics.fallbackRate * 100 | number:'1.0-0' }}%</span>
                              </div>
                              <div class="progress h-10px">
                                <div class="progress-bar" [ngClass]="{
                                  'bg-danger': user.metrics.fallbackRate >= user.metrics.fallbackThreshold,
                                  'bg-warning': user.metrics.fallbackRate >= user.metrics.fallbackThreshold * 0.7,
                                  'bg-success': true
                                }" [style.width.%]="user.metrics.fallbackRate * 100"></div>
                              </div>
                              <div class="text-muted fs-8 mt-1">
                                Threshold: {{ user.metrics.fallbackThreshold * 100 | number:'1.0-0' }}%
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="row g-5" *ngIf="activeTab === 'message'">
                    <!-- Message Metrics Content -->
                    <div class="col-12">
                      <div class="card card-bordered h-100">
                        <div class="card-header">
                          <h5 class="card-title">Message Metrics</h5>
                        </div>
                        <div class="card-body">
                          <div class="d-flex flex-column gap-4">
                            <div>
                              <div class="d-flex justify-content-between mb-1">
                                <span class="text-muted">Messages per Minute</span>
                                <span class="fw-bold">{{ user.metrics.messagesPerMinute | number:'1.1-1' }}</span>
                              </div>
                              <div class="progress h-10px">
                                <div class="progress-bar" [ngClass]="{
                                  'bg-danger': user.metrics.messagesPerMinute >= user.metrics.highFrequencyThreshold,
                                  'bg-warning': user.metrics.messagesPerMinute >= user.metrics.highFrequencyThreshold * 0.5,
                                  'bg-success': true
                                }" [style.width.%]="Math.min(user.metrics.messagesPerMinute, 30) * 3.33"></div>
                              </div>
                              <div class="text-muted fs-8 mt-1">
                                Threshold: {{ user.metrics.highFrequencyThreshold }}/min
                              </div>
                            </div>
                            
                            <div>
                              <div class="d-flex justify-content-between mb-1">
                                <span class="text-muted">Duplicate Messages</span>
                                <span class="fw-bold">{{ user.metrics.duplicateMessages }} ({{ user.metrics.duplicateMessageRatio * 100 | number:'1.0-0' }}%)</span>
                              </div>
                              <div class="progress h-10px">
                                <div class="progress-bar" [ngClass]="{
                                  'bg-danger': user.metrics.duplicateMessageRatio >= user.metrics.duplicateThreshold,
                                  'bg-warning': user.metrics.duplicateMessageRatio >= user.metrics.duplicateThreshold * 0.7,
                                  'bg-success': true
                                }" [style.width.%]="user.metrics.duplicateMessageRatio * 100"></div>
                              </div>
                              <div class="text-muted fs-8 mt-1">
                                Threshold: {{ user.metrics.duplicateThreshold * 100 | number:'1.0-0' }}%
                              </div>
                            </div>
                            
                            <div>
                              <div class="d-flex justify-content-between mb-1">
                                <span class="text-muted">Average Confidence</span>
                                <span class="fw-bold">{{ (user.metrics.avgConfidence * 100) | number:'1.0-0' }}%</span>
                              </div>
                              <div class="progress h-10px">
                                <div class="progress-bar" [ngClass]="{
                                  'bg-danger': user.metrics.avgConfidence < 0.5,
                                  'bg-warning': user.metrics.avgConfidence >= 0.5 && user.metrics.avgConfidence < 0.7,
                                  'bg-success': user.metrics.avgConfidence >= 0.7
                                }" [style.width.%]="user.metrics.avgConfidence * 100"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!--end::Metrics-->
                </div>
              </td>
            </tr>
            <!--end::Details row-->
          </ng-container>
        </tbody>
        <!--end::Table body-->
      </table>
    </div>
    <!--end::User list-->
    
    <!--begin::Pagination-->
    <div *ngIf="!loading && metrics.length > 0" class="d-flex justify-content-between align-items-center flex-wrap pt-10">
      <div class="fs-6 fw-semibold text-gray-700">
        Showing <span class="text-dark fw-bold">{{ getStartIndex() }}</span> to 
        <span class="text-dark fw-bold">{{ getEndIndex() }}</span> of 
        <span class="text-dark fw-bold">{{ totalItems }}</span> entries
      </div>
      <ul class="pagination">
        <li class="page-item previous" [class.disabled]="currentPage === 1">
          <a href="javascript:;" class="page-link" (click)="onPageChange(currentPage - 1)">
            <i class="previous"></i>
          </a>
        </li>
        
        <li *ngFor="let page of getPageNumbers()" class="page-item" [class.active]="page === currentPage">
          <a *ngIf="page !== '...'" href="javascript:;" class="page-link" (click)="onPageChange(page)">{{ page }}</a>
          <span *ngIf="page === '...'" class="page-link">...</span>
        </li>
        
        <li class="page-item next" [class.disabled]="currentPage * itemsPerPage >= totalItems">
          <a href="javascript:;" class="page-link" (click)="onPageChange(currentPage + 1)">
            <i class="next"></i>
          </a>
        </li>
      </ul>
    </div>
    <!--end::Pagination-->
  </div>
  <!--end::Card body-->
</div>
<!--end::Card-->
