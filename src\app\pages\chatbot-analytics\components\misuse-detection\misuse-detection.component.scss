// Add any custom styles that can't be achieved with Metronic classes
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

// Style for the progress bar in the table
.progress {
  height: 6px;
  border-radius: 3px;
  background-color: #f1f1f2;
  overflow: visible;
  
  .progress-bar {
    border-radius: 3px;
  }
}

// Hover effect for table rows
tr[style*="cursor: pointer"] {
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f8f9fa !important;
  }
}

// Card styles for metrics
.card-bordered {
  border: 1px solid #e4e6ef;
  border-radius: 0.475rem;
  
  .card-header {
    border-bottom: 1px solid #e4e6ef;
    padding: 1rem 1.5rem;
    background-color: #f9f9f9;
    
    .card-title {
      margin-bottom: 0;
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .card-body {
    padding: 1.5rem;
  }
}

// Badge styles for risk levels
.badge-light-danger {
  color: #f1416c;
  background-color: #fff5f8;
  border: 1px solid #f1416c;
}

.badge-light-warning {
  color: #ffc700;
  background-color: #fff8dd;
  border: 1px solid #ffc700;
}

.badge-light-success {
  color: #50cd89;
  background-color: #e8fff3;
  border: 1px solid #50cd89;
}

// Responsive adjustments
@media (max-width: 991.98px) {
  .card-bordered {
    margin-bottom: 1.5rem;
  }
}

// Animation for expanding/collapsing details
tr[role="row"] {
  transition: all 0.3s ease;
}

// Tooltip styles
.tooltip-inner {
  max-width: 300px;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}
