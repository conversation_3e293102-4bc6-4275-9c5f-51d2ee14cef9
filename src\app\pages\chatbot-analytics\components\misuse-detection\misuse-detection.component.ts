import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MisuseDetectionService, MisuseMetrics } from '../../services/misuse-detection.service';
import { Conversation } from '../../models/chat-analytics.model';

@Component({
  selector: 'app-misuse-detection',
  templateUrl: './misuse-detection.component.html',
  styleUrls: ['./misuse-detection.component.scss'],
  host: {
    class: 'd-flex flex-column flex-grow-1'
  }
})
export class MisuseDetectComponent implements OnChanges {
  @Input() conversations: Conversation[] = [];
  @Output() flaggedUsersChange = new EventEmitter<string[]>();
  
  metrics: MisuseMetrics[] = [];
  paginatedMetrics: MisuseMetrics[] = [];
  loading = false;
  selectedUserId: string | null = null;
  activeTab: 'engagement' | 'session' | 'message' = 'engagement';
  
  // Pagination properties
  currentPage = 1;
  itemsPerPage = 5;
  totalItems = 0;
  
  constructor(private readonly misuseDetectionService: MisuseDetectionService) {}
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['conversations']) {
      if (this.conversations?.length > 0) {
        this.analyzeConversations();
      } else {
        this.metrics = [];
      }
    }
  }
  
  private analyzeConversations(): void {
    this.loading = true;
    this.metrics = this.misuseDetectionService.analyzeConversations(this.conversations);
    // Emit flagged users (riskScore >= 70)
    const flagged = this.metrics.filter(m => m.metrics.riskScore >= 70).map(m => m.userId);
    this.flaggedUsersChange.emit(flagged);
    this.updatePagination();
    this.loading = false;
  }
  
  selectUser(userId: string): void {
    const wasSelected = this.selectedUserId === userId;
    this.selectedUserId = wasSelected ? null : userId;
    if (!wasSelected) {
      this.activeTab = 'engagement';
    }
  }
  
  getRiskBadgeClass(level: 'low' | 'medium' | 'high'): string {
    switch (level) {
      case 'high': return 'badge-light-danger';
      case 'medium': return 'badge-light-warning';
      case 'low':
      default: return 'badge-light-success';
    }
  }

  // Pagination methods
  updatePagination(): void {
    this.totalItems = this.metrics.length;
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedMetrics = this.metrics.slice(startIndex, endIndex);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.updatePagination();
  }

  getStartIndex(): number {
    return (this.currentPage - 1) * this.itemsPerPage + 1;
  }

  getEndIndex(): number {
    return Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
  }

  getPageNumbers(): (number | string)[] {
    const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    const range: (number | string)[] = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
      range.push(1);
      if (startPage > 2) {
        range.push('...');
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        range.push('...');
      }
      range.push(totalPages);
    }

    return range;
  }
  
  getRiskText(level: 'low' | 'medium' | 'high'): string {
    return level.charAt(0).toUpperCase() + level.slice(1);
  }

  setActiveTab(tab: 'engagement' | 'session' | 'message'): void {
    this.activeTab = tab;
  }
}
