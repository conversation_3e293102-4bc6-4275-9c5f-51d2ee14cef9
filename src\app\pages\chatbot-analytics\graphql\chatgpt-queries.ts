import { gql } from 'apollo-angular';

export const GET_CHATGPT_BY_DATE = gql`
  query ChatGPTByDate(
    $isDeleted: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelChatGPTFilterInput
    $limit: Int
    $nextToken: String
  ) {
    chatGPTByDate(
      isDeleted: $isDeleted
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        userData {
          familyName
          givenName
        }
        chatType
        message
        role
        messageType
        isDeleted
        createdAt
      }
      nextToken
    }
  }
`;
