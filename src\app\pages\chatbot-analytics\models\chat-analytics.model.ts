/**
 * Chat type enum
 */
export enum ChatType {
  UNIFY = 'Unify',
  COMMUNITY = 'Community',
  FAMILY = 'Family',
  KNOWLEDGE = 'Knowledge',
  DREAMS = 'Dreams',
  SPIRITUALITY = 'Spirituality',
  GENERAL = 'General'
}

/**
 * Chat message interface
 */
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'Asistant' | 'system';
  content: string;
  message?: string; // Alias for content for backward compatibility
  timestamp?: string; // Keeping for backward compatibility
  createdAt: string; // Primary timestamp field
  chatType?: ChatType;
  userId: string;
  userData?: {
    givenName?: string;
    familyName?: string;
    email?: string;
  };
  intent?: string;
  confidence?: number;
  sources?: any[];
  isDeleted?: boolean;
}

/**
 * Conversation interface representing a chat session
 */
export interface Conversation {
  id: string;
  userId: string;
  userData: {
    givenName?: string;
    familyName?: string;
    email?: string;
  };
  messages: ChatMessage[];
  timestamp: string;
  chatType: ChatType;
  createdAt?: string;
}

/**
 * Key Performance Indicators for chat analytics
 */
export interface ChatAnalyticsKPIs {
  totalConversations: number;
  uniqueUsers: number;
  flaggedUsers: number;
  totalMessages?: number;
  avgResponseTime?: number;
  satisfactionRate?: number;
  successRate?: number;
}

/**
 * Date range interface for filtering
 */
export interface DateRange {
  start: string | Date | null;
  end: string | Date | null;
}

/**
 * Table column configuration
 */
export interface TableColumn {
  key: string;
  label: string;
  sortable: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (item: any) => string;
}

/**
 * Chat analytics parameters for API requests
 */
export interface ChatAnalyticsParams {
  startDate?: string;
  endDate?: string;
  chatType?: string;
  searchQuery?: string;
  page?: number;
  pageSize?: number;
  userId?: string;
}

/**
 * Chat analytics API response
 */
export interface ChatAnalyticsResponse {
  conversations: Conversation[];
  kpis: ChatAnalyticsKPIs;
  totalCount: number;
  hasNextPage: boolean;
  nextToken?: string;
}

/**
 * Error details interface
 */
export interface ErrorDetails {
  message: string;
  details?: string;
  status?: number;
}

/**
 * Formatted conversation for display
 */
export interface FormattedConversation {
  id: string;
  conversationId: string;
  userId: string;
  userName: string;
  message: string;
  response: string;
  chatType: ChatType;
  timestamp: string;
  createdAt: string;
  messageTimestamp: string;
  responseTimestamp: string | null;
  messageId: string;
  responseId?: string;
  hasResponse: boolean;
  messages: ChatMessage[];
}

/**
 * Chat type option for filter dropdown
 */
export interface ChatTypeOption {
  value: ChatType | '';
  label: string;
  badgeClass: string;
}
