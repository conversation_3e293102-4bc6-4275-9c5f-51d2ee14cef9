import { Pipe, PipeTransform } from '@angular/core';

type ChatTypeStyle = {
  class: string;
  display: string;
};

const CHAT_TYPES: { [key: string]: ChatTypeStyle } = {
  'GENERAL': { class: 'badge-light-primary', display: 'General' },
  'SUPPORT': { class: 'badge-light-success', display: 'Support' },
  'TRANSACTION': { class: 'badge-light-info', display: 'Transaction' },
  'UNIFY': { class: 'badge-light-primary', display: 'Unify' },
  'DEFAULT': { class: 'badge-light-secondary', display: 'N/A' }
};

@Pipe({
  name: 'chatType'
})
export class ChatTypePipe implements PipeTransform {
  transform(value: string | undefined, format: 'class' | 'display' = 'class'): string {
    if (!value) {
      return format === 'class' ? CHAT_TYPES['DEFAULT'].class : CHAT_TYPES['DEFAULT'].display;
    }
    
    const type = value.toUpperCase();
    const matchedType = Object.entries(CHAT_TYPES).find(([key]) => 
      type.includes(key) || key.includes(type)
    );
    
    const result = matchedType ? CHAT_TYPES[matchedType[0]] : CHAT_TYPES['DEFAULT'];
    return format === 'class' ? result.class : result.display;
  }
}
