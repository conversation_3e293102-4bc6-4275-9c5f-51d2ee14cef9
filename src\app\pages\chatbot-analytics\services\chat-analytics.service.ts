import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, take } from 'rxjs/operators';
import {
  ChatMessage,
  Conversation,
  ChatAnalyticsKPIs,
  ChatType,
  ChatAnalyticsParams,
  ChatAnalyticsResponse
} from '../models/chat-analytics.model';
import { GET_CHATGPT_BY_DATE } from '../graphql/chatgpt-queries';
import { getMessageTime } from '../utils/date.utils';
import {
  processChatTypeData,
  processConversationVolume,
  processTrendData
} from '../utils/data-processor.utils';

const DEFAULT_PAGE_SIZE = 10;

// Define the expected response type for the GraphQL query
interface ChatGptByDateResponse {
  chatGPTByDate: {
    items: ChatMessage[];
    nextToken: string | null;
  };
}

const TIME_WINDOW_MS = 30 * 60 * 1000; // 30 minutes

@Injectable({
  providedIn: 'root'
})
export class ChatAnalyticsService {
  constructor(private apollo: Apollo) { }

  /**
   * Get analytics data with the specified parameters
   * @param params Query parameters for filtering analytics data
   * @returns Observable with analytics data
   */
  /**
   * Get analytics data with the specified parameters
   */
  getAnalyticsData(params: {
    startDate: string;
    endDate: string;
    types: string;
    sort: string;
    search: string;
    userId?: string;
    range?: '24h' | '7d' | '30d' | '90d' | 'custom';
  }): Observable<any> {
    // First, fetch the raw chat data
    return this.getChatData(params).pipe(
      map(response => {
        const messages = response?.chatGPTByDate?.items || [];
        const conversations = this.groupMessagesIntoConversations(messages);

        // Process data for different visualizations
        const chatTypeData = processChatTypeData(messages);
        const volumeData = processConversationVolume(conversations, params.range || '7d',
          params.startDate ? new Date(params.startDate) : undefined,
          params.endDate ? new Date(params.endDate) : undefined
        );
        const trendData = processTrendData(messages, params.range || '7d',
          params.startDate ? new Date(params.startDate) : undefined,
          params.endDate ? new Date(params.endDate) : undefined
        );

        // Calculate KPIs using only conversations since all needed data is in the conversation objects
        const kpis = this.calculateKPIs(conversations);

        return {
          volumeData: volumeData.values,
          chatTypeData: {
            series: chatTypeData.series,
            labels: chatTypeData.labels
          },
          trendData: {
            values: trendData.values,
            categories: trendData.categories
          },
          kpis
        };
      }),
      catchError(error => {
        console.error('Error processing analytics data:', error);
        return throwError(() => new Error('Failed to process analytics data'));
      })
    );
  }

  /**
   * Fetch chat data from the API
   */
  private getChatData(params: {
    startDate: string;
    endDate: string;
    types: string;
    sort: string;
    search: string;
    userId?: string;
  }): Observable<{ chatGPTByDate: { items: ChatMessage[]; nextToken: string | null } }> {
    return this.apollo
      .query<{ chatGPTByDate: { items: ChatMessage[]; nextToken: string | null } }>({
        query: GET_CHATGPT_BY_DATE,
        variables: {
          isDeleted: 'false',
          createdAt: {
            between: [params.startDate, params.endDate]
          },
          filter: {
            ...(params.types && params.types !== 'all' ? { chatType: { eq: params.types } } : {}),
            ...(params.userId ? { userId: { eq: params.userId } } : {}),
            ...(params.search ? { message: { contains: params.search } } : {})
          },
          sortDirection: params.sort === 'asc' ? 'ASC' : 'DESC',
          limit: 1000 // Adjust based on your needs
        },
        fetchPolicy: 'network-only' // Always fetch fresh data
      })
      .pipe(
        map(result => result.data),
        catchError(error => {
          console.error('Error fetching chat data:', error);
          return throwError(() => new Error('Failed to fetch chat data'));
        })
      );
  }

  /**
   * Groups individual chat messages into conversation threads
   * @param messages Array of chat messages
   * @returns Array of conversation objects
   */
  private groupMessagesIntoConversations(messages: ChatMessage[]): Conversation[] {
    if (!messages || messages.length === 0) return [];

    // Sort messages by timestamp (oldest first for easier grouping)
    const sortedMessages = [...messages].sort((a, b) =>
      new Date(getMessageTime(a)).getTime() - new Date(getMessageTime(b)).getTime()
    );

    const conversations: Conversation[] = [];
    const timeWindow = TIME_WINDOW_MS;
    let currentConversation: Conversation | null = null;

    // Group messages into conversations
    for (const message of sortedMessages) {
      const messageTime = new Date(getMessageTime(message)).getTime();

      // Check if we should start a new conversation
      if (!currentConversation ||
        currentConversation.userId !== message.userId ||
        (currentConversation.chatType !== message.chatType && message.chatType) ||
        (currentConversation.messages.length > 0 &&
          messageTime - new Date(getMessageTime(currentConversation.messages[currentConversation.messages.length - 1])).getTime() > timeWindow)) {

        // Save previous conversation if exists
        if (currentConversation) {
          conversations.push(currentConversation);
        }

        // Start new conversation
        currentConversation = {
          id: `conv_${message.id}`,
          userId: message.userId,
          userData: message.userData || {
            givenName: message.userId,
            familyName: '',
            email: ''
          },
          messages: [],
          timestamp: message.createdAt || new Date().toISOString(),
          chatType: (message.chatType as ChatType) || ChatType.GENERAL,
          createdAt: message.createdAt
        };
      }

      // Add message to current conversation
      if (currentConversation) {
        currentConversation.messages.push(message);
        // Update conversation timestamp to the latest message
        currentConversation.timestamp = message.createdAt || currentConversation.timestamp;
      }
    }

    // Add the last conversation if it exists
    if (currentConversation) {
      conversations.push(currentConversation);
    }

    return conversations;
  }

  /**
   * Calculates key performance indicators from conversations
   * @param conversations Array of conversation objects
   * @param messages Array of chat messages
   * @returns Object containing KPI metrics
   */
  private calculateKPIs(conversations: Conversation[]): ChatAnalyticsKPIs {
    if (!conversations || conversations.length === 0) {
      return {
        totalConversations: 0,
        uniqueUsers: 0,
        flaggedUsers: 0,
        totalMessages: 0,
        avgResponseTime: 0,
        satisfactionRate: 0,
        successRate: 0
      };
    }

    const uniqueUsers = new Set<string>();
    let flaggedUsers = 0;
    let totalMessages = 0;
    let totalResponseTime = 0;
    let responseCount = 0;
    let satisfiedResponses = 0;
    let successfulConversations = 0;

    conversations.forEach(conv => {
      // Track unique users
      uniqueUsers.add(conv.userId);

      // Count total messages in this conversation
      totalMessages += conv.messages.length;

      // Track response times and satisfaction
      let lastUserMessageTime: number | null = null;
      let hasBotResponse = false;

      // Sort messages by timestamp to ensure correct order
      const sortedMessages = [...conv.messages].sort((a, b) =>
        new Date(getMessageTime(a)).getTime() - new Date(getMessageTime(b)).getTime()
      );

      for (const msg of sortedMessages) {
        const msgTime = new Date(getMessageTime(msg)).getTime();

        if (msg.role === 'user') {
          lastUserMessageTime = msgTime;
        }
        // Check for bot responses
        else if ((msg.role === 'assistant' || msg.role === 'Asistant') && lastUserMessageTime) {
          hasBotResponse = true;
          const responseTime = msgTime - lastUserMessageTime;
          totalResponseTime += responseTime;
          responseCount++;

          // Simple satisfaction detection
          if (msg.content &&
            (msg.content.toLowerCase().includes('thank') ||
              msg.content.toLowerCase().includes('helpful'))) {
            satisfiedResponses++;
          }
        }
      }

      // Consider a conversation successful if it had at least one bot response
      if (hasBotResponse) {
        successfulConversations++;
      }

      // Check for flagged conversations (e.g., deleted messages)
      if (conv.messages.some(m => m.isDeleted)) {
        flaggedUsers++;
      }
    });

    // Calculate averages and rates
    const avgResponseTime = responseCount > 0 ? Math.round(totalResponseTime / responseCount) : 0;
    const satisfactionRate = responseCount > 0 ? Math.round((satisfiedResponses / responseCount) * 100) : 0;
    const successRate = conversations.length > 0 ? Math.round((successfulConversations / conversations.length) * 100) : 0;

    return {
      totalConversations: conversations.length,
      uniqueUsers: uniqueUsers.size,
      flaggedUsers,
      totalMessages,
      avgResponseTime,
      satisfactionRate,
      successRate
    };
  }

  async fetchConversations(params: ChatAnalyticsParams): Promise<ChatAnalyticsResponse> {
    try {
      const {
        startDate,
        endDate,
        chatType,
        searchQuery,
        page = 1,
        pageSize = DEFAULT_PAGE_SIZE
      } = params;

      // Format dates for API
      const formatDate = (date: string | Date | null | undefined): string | undefined => {
        if (!date) return undefined;
        try {
          const d = new Date(date);
          return isNaN(d.getTime()) ? undefined : d.toISOString();
        } catch (e) {
          console.warn('Invalid date format:', date, e);
          return undefined;
        }
      };

      // Build filter - only include fields that exist in ModelChatGPTFilterInput
      const filter: any = {};

      // Add date range to the key condition
      const createdAt: any = {};
      const start = formatDate(startDate);
      const end = formatDate(endDate);

      // For DynamoDB, we need to use between for date ranges
      if (start && end) {
        createdAt.between = [start, end];
      } else if (start) {
        createdAt.ge = start;
      } else if (end) {
        createdAt.le = end;
      }

      // Add chat type filter if provided
      if (chatType) {
        filter.chatType = { eq: chatType };
      }

      // Add search query filter if provided
      // We'll handle the search filtering manually after fetching to include related Q&A pairs
      if (searchQuery) {
        // We'll still filter on the server side for performance
        filter.message = { contains: searchQuery };
      }

      // Calculate pagination
      const limit = Math.min(pageSize, 500); // Cap at 100 items per page

      // Prepare query variables for DynamoDB
      const variables: any = {
        isDeleted: 'false', // Required partition key
        sortDirection: 'DESC',
        limit: limit || 1000,
        nextToken: null
      };

      // Only add createdAt if we have date range conditions
      if (Object.keys(createdAt).length > 0) {
        variables.createdAt = createdAt;
      }

      // Only add filter if we have filter conditions
      if (Object.keys(filter).length > 0) {
        variables.filter = filter;
      }

      // Execute GraphQL query
      const result = await this.apollo.query<ChatGptByDateResponse>({
        query: GET_CHATGPT_BY_DATE,
        variables,
        fetchPolicy: 'network-only',
        errorPolicy: 'all'
      }).pipe(
        take(1),
        catchError(error => {
          console.error('GraphQL query error:', error);
          return throwError(() => new Error('Failed to fetch conversations. Please try again later.'));
        })
      ).toPromise();

      if (!result?.data?.chatGPTByDate?.items) {
        console.error('Invalid response format from server:', result);
        throw new Error('Received invalid data from the server');
      }

      const items = result.data.chatGPTByDate.items;
      const nextToken = result.data.chatGPTByDate.nextToken;

      // Group messages into conversations
      let conversations = this.groupMessagesIntoConversations(items);

      // Store the original search results to ensure we don't lose them
      const originalSearchResults = [...conversations];

      // If there's a search query, we need to include related Q&A pairs
      if (searchQuery) {
        // Get all conversation IDs that matched the search
        const matchedConversationIds = new Set(conversations.map(c => c.id));

        // If we found matches, fetch the full conversations to include all related messages
        if (matchedConversationIds.size > 0) {
          try {
            // Execute GraphQL query to get all messages from matched conversations
            const fullResult = await this.apollo.query<ChatGptByDateResponse>({
              query: GET_CHATGPT_BY_DATE,
              variables: {
                isDeleted: 'false',
                sortDirection: 'DESC',
                filter: {
                  or: Array.from(matchedConversationIds).map(convId => ({
                    id: { beginsWith: convId }
                  }))
                },
                limit: 1000
              },
              fetchPolicy: 'network-only',
              errorPolicy: 'all'
            }).pipe(
              take(1),
              catchError(error => {
                console.error('GraphQL query error when fetching full conversations:', error);
                // Return the original search results if there's an error
                return of({ data: { chatGPTByDate: { items, nextToken: null } } });
              })
            ).toPromise();

            if (fullResult?.data?.chatGPTByDate?.items?.length) {
              // Group all messages including the ones from the same conversations
              const allConversations = this.groupMessagesIntoConversations(fullResult.data.chatGPTByDate.items);

              // Only include conversations that matched the original search
              conversations = allConversations.filter(conv =>
                matchedConversationIds.has(conv.id)
              );

              // Ensure we maintain the original order and include all messages from the conversation
              conversations = conversations.map(conv => {
                const fullConv = allConversations.find(c => c.id === conv.id);
                return fullConv || conv;
              });
            }
          } catch (error) {
            console.error('Error fetching full conversations:', error);
            // Fall back to original search results if there's an error
            conversations = originalSearchResults;
          }
        }
      }

      // Calculate KPIs
      const kpis = this.calculateKPIs(conversations);

      return {
        conversations,
        kpis,
        totalCount: conversations.length,
        hasNextPage: !!nextToken,
        nextToken
      };
    } catch (error) {
      console.error('Error in fetchConversations:', error);
      throw error instanceof Error
        ? error
        : new Error('An unexpected error occurred while fetching conversations');
    }
  }
}
