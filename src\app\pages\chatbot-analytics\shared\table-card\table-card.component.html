<div class="table-responsive position-relative">
  <!-- Loading overlay -->
  <div *ngIf="loading" class="loading-overlay">
    <div class="d-flex flex-column align-items-center">
      <div class="spinner-border text-primary mb-3" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <span class="text-muted">{{ spinnerMessage }}</span>
    </div>
  </div>
  
  <table class="table align-middle table-row-bordered table-row-solid gy-4 gs-9">
    <!-- Table header -->
    <thead class="border-gray-200 fs-5 fw-semibold bg-lighten">
      <tr>
        <ng-container *ngFor="let col of processedColumns; trackBy: trackByIndex">
          <th 
            [class]="'min-w-150px ' + (col.width ? 'w-' + col.width : '')"
            [ngClass]="getCellClasses(col)"
            [style.width]="col.width ? col.width + 'px' : 'auto'"
            [attr.data-col]="col.key">
            <div class="d-flex align-items-center">
              <span>{{ col.label }}</span>
              <!-- Add sorting indicators here if needed -->
              <ng-container *ngIf="col.sortable">
                <i class="bi bi-arrow-down-up ms-2 text-muted" style="opacity: 0.5;"></i>
              </ng-container>
            </div>
          </th>
        </ng-container>
      </tr>
    </thead>
    
    <!-- Error state -->
    <tbody *ngIf="!loading && error" class="error-state">
      <tr>
        <td [attr.colspan]="processedColumns.length" class="text-center py-10">
          <div class="d-flex flex-column align-items-center">
            <i class="bi bi-exclamation-triangle-fill text-danger fs-1 mb-3"></i>
            <h5 class="text-danger mb-3">Error loading data</h5>
            <p class="text-muted mb-3">We couldn't load the data. {{ error.message || 'Please try again.' }}</p>
            <button class="btn btn-sm btn-primary" (click)="onRetry()" [disabled]="loading">
              <i class="bi bi-arrow-clockwise me-1" [class.spin]="loading"></i>
              {{ loading ? 'Retrying...' : 'Retry' }}
            </button>
          </div>
        </td>
      </tr>
    </tbody>
    
    <!-- Empty state -->
    <tbody *ngIf="!loading && !error && (!data || data.length === 0)" class="empty-state">
      <tr>
        <td [attr.colspan]="processedColumns.length" class="text-center py-10">
          <div class="d-flex flex-column align-items-center">
            <i class="bi bi-inbox fs-1 text-muted mb-3"></i>
            <h5 class="text-muted mb-2">{{ emptyMessage }}</h5>
            <p class="text-muted mb-0">{{ emptyDescription }}</p>
          </div>
        </td>
      </tr>
    </tbody>
    
    <!-- Data rows -->
    <ng-content *ngIf="!loading && !error && data && data.length > 0"></ng-content>
  </table>
</div>

<!-- Pagination -->
<div *ngIf="showPagination && !loading && !error && data.length > 0" class="d-flex justify-content-between align-items-center mt-4">
  <div class="text-muted">
    Showing <span class="fw-bold">{{(currentPage - 1) * itemsPerPage + 1}}</span> to 
    <span class="fw-bold">{{Math.min(currentPage * itemsPerPage, totalItems)}}</span> of 
    <span class="fw-bold">{{totalItems | number}}</span> entries
  </div>
  
  <nav aria-label="Page navigation">
    <ul class="pagination mb-0">
      <!-- First Page -->
      <li class="page-item" [class.disabled]="currentPage === 1">
        <a class="page-link" href="javascript:;" (click)="$event.preventDefault(); onFirstPage()" aria-label="First" [class.text-muted]="currentPage === 1">
          <i class="bi bi-chevron-double-left"></i>
        </a>
      </li>
      
      <!-- Previous Page -->
      <li class="page-item" [class.disabled]="currentPage === 1">
        <a class="page-link" href="javascript:;" (click)="$event.preventDefault(); onPreviousPage()" aria-label="Previous" [class.text-muted]="currentPage === 1">
          <i class="bi bi-chevron-left"></i>
        </a>
      </li>
      
      <!-- Page Numbers -->
      <ng-container *ngFor="let page of getPageNumbers()">
        <li class="page-item" [class.active]="page === currentPage" [class.disabled]="page === -1">
          <ng-container *ngIf="page === -1; else pageNumber">
            <span class="page-link">...</span>
          </ng-container>
          <ng-template #pageNumber>
            <a class="page-link" href="javascript:;" (click)="$event.preventDefault(); onPageChange(page)" [class.text-primary]="page !== currentPage">
              {{page}}
            </a>
          </ng-template>
        </li>
      </ng-container>
      
      <!-- Next Page -->
      <li class="page-item" [class.disabled]="currentPage === getTotalPages()">
        <a class="page-link" href="javascript:;" (click)="$event.preventDefault(); onNextPage()" aria-label="Next" [class.text-muted]="currentPage === getTotalPages()">
          <i class="bi bi-chevron-right"></i>
        </a>
      </li>
      
      <!-- Last Page -->
      <li class="page-item" [class.disabled]="currentPage === getTotalPages()">
        <a class="page-link" href="javascript:;" (click)="$event.preventDefault(); onLastPage()" aria-label="Last" [class.text-muted]="currentPage === getTotalPages()">
          <i class="bi bi-chevron-double-right"></i>
        </a>
      </li>
    </ul>
  </nav>
</div>
