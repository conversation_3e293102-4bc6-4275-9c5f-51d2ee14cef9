// Table Card Styles
:host {
  display: block;
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 0.5rem;
  backdrop-filter: blur(2px);
  
  .spinner-border {
    width: 2.5rem;
    height: 2.5rem;
    border-width: 0.2em;
  }
  
  .text-muted {
    color: #6c757d !important;
    font-size: 0.9rem;
    margin-top: 0.75rem;
  }
}

.table-card {
  .table {
    thead {
      th {
        font-weight: 600;
        color: #3f4254;
        background-color: #f9f9f9;
        border-bottom: 2px solid #eff2f5;
        white-space: nowrap;
        position: relative;
        padding: 1rem 1.25rem;
        
        &:first-child {
          border-top-left-radius: 0.5rem;
        }
        
        &:last-child {
          border-top-right-radius: 0.5rem;
        }
      }
    }

    tbody {
      tr {
        transition: all 0.2s ease;
        
        &:hover {
          background-color: #f8f9fa;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        &:last-child {
          td:first-child {
            border-bottom-left-radius: 0.5rem;
          }
          td:last-child {
            border-bottom-right-radius: 0.5rem;
          }
        }
      }

      td {
        vertical-align: middle;
        padding: 1.25rem 1.25rem;
        border-bottom: 1px solid #eff2f5;
        transition: all 0.2s ease;
        
        &:first-child {
          padding-left: 1.5rem;
        }
        
        &:last-child {
          padding-right: 1.5rem;
        }
      }
      
      // Error state
      .error-state {
        td {
          padding: 3rem 1.5rem;
          text-align: center;
          
          .bi {
            font-size: 2.5rem;
            margin-bottom: 1rem;
          }
          
          h5 {
            font-weight: 600;
            margin-bottom: 0.5rem;
          }
          
          p {
            color: #6c757d;
            margin-bottom: 1.5rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
          }
          
          .btn {
            min-width: 120px;
            
            .bi {
              font-size: 1rem;
              margin-bottom: 0;
              margin-right: 0.5rem;
              vertical-align: -0.125em;
            }
          }
        }
      }
      
      // Empty state
      .empty-state {
        td {
          padding: 4rem 1.5rem;
          text-align: center;
          
          .bi {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            opacity: 0.7;
          }
          
          h5 {
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
          }
          
          p {
            color: #adb5bd;
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .pagination {
    margin: 1.5rem 0 0;
    
    .page-item {
      margin: 0 0.25rem;
      
      &.disabled {
        .page-link {
          color: #b5b5c3;
          background-color: #f5f5f5;
          border-color: #f5f5f5;
          cursor: not-allowed;
        }
      }
      
      &.active {
        .page-link {
          background-color: #009ef7;
          border-color: #009ef7;
          color: #ffffff;
          font-weight: 600;
          box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
        }
      }
      
      &:first-child,
      &:last-child {
        .page-link {
          border-radius: 0.475rem;
        }
      }
    }
    
    .page-link {
      color: #7e8299;
      border: 1px solid #e4e6ef;
      padding: 0.5rem 0.75rem;
      min-width: 38px;
      text-align: center;
      border-radius: 0.475rem;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: #f5f8fa;
        border-color: #f5f8fa;
        color: #009ef7;
      }
      
      i {
        font-size: 0.8rem;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 991.98px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

// Dark mode support
[data-kt-app-layout="dark-sidebar"] {
  .table-card {
    .table {
      thead {
        th {
          color: #cdcdde;
          background-color: #1e1e2d;
          border-bottom-color: #2b2b40;
        }
      }
      
      tbody {
        tr {
          border-color: #2b2b40;
          
          &:hover {
            background-color: #2b2b40;
          }
        }
        
        td {
          color: #cdcdde;
          border-color: #2b2b40;
        }
      }
    }
    
    .pagination {
      .page-link {
        background-color: #1e1e2d;
        border-color: #2b2b40;
        color: #9899ac;
        
        &:hover {
          background-color: #2b2b40;
          border-color: #2b2b40;
        }
      }
      
      .page-item.active .page-link {
        background-color: #009ef7;
        border-color: #009ef7;
      }
      
      .page-item.disabled .page-link {
        background-color: #2b2b40;
        border-color: #2b2b40;
        color: #565674;
      }
    }
  }
}
