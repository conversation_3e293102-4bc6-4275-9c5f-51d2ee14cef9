import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

@Component({
  selector: 'app-table-card',
  templateUrl: './table-card.component.html',
  styleUrls: ['./table-card.component.scss']
})
export class TableCardComponent<T = any> implements OnChanges {
  @Input() columns: (string | TableColumn)[] = [];
  @Input() data: T[] = [];
  @Input() loading = false;
  @Input() error: any = null;
  @Input() currentPage = 1;
  @Input() itemsPerPage = 10;
  @Input() totalItems = 0;
  @Input() showPagination = true;
  @Input() spinnerMessage = 'Loading data...';
  @Input() emptyMessage = 'No data available';
  @Input() emptyDescription = 'There are no records to display.';
  
  @Output() pageChange = new EventEmitter<number>();
  @Output() retry = new EventEmitter<void>();
  
  pagedData: T[] = [];
  totalPages = 0;
  processedColumns: TableColumn[] = [];
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['columns']) {
      this.processColumns();
    }
    
    if (changes['data'] || changes['currentPage'] || changes['itemsPerPage'] || changes['totalItems']) {
      this.updatePagination();
    }
  }
  
  private processColumns(): void {
    this.processedColumns = this.columns.map(col => {
      if (typeof col === 'string') {
        return {
          key: col.toLowerCase().replace(/\s+/g, '-'),
          label: col,
          sortable: false,
          align: 'left'
        };
      }
      return {
        key: col.key,
        label: col.label,
        sortable: col.sortable || false,
        width: col.width,
        align: col.align || 'left'
      };
    });
  }
  
  updatePagination(): void {
    if (!this.data) {
      this.pagedData = [];
      this.totalPages = 0;
      return;
    }
    
    this.totalPages = Math.max(1, Math.ceil(this.totalItems / this.itemsPerPage));
    
    // If current page is out of bounds, reset to last page
    if (this.currentPage > this.totalPages && this.totalPages > 0) {
      this.currentPage = this.totalPages;
      this.pageChange.emit(this.currentPage);
      return;
    }
    
    // For client-side pagination
    if (this.totalItems === this.data.length) {
      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;
      this.pagedData = this.data.slice(startIndex, endIndex);
    } else {
      // For server-side pagination, use the data as is
      this.pagedData = [...this.data];
    }
  }
  
  onRetry(): void {
    if (!this.loading) {
      this.retry.emit();
    }
  }
  
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.pageChange.emit(page);
    }
  }
  
  onFirstPage(): void {
    this.onPageChange(1);
  }
  
  onPreviousPage(): void {
    this.onPageChange(this.currentPage - 1);
  }
  
  onNextPage(): void {
    this.onPageChange(this.currentPage + 1);
  }
  
  onLastPage(): void {
    this.onPageChange(this.totalPages);
  }
  
  trackByIndex(index: number): number {
    return index;
  }
  
  getCellClasses(column: TableColumn): { [key: string]: boolean } {
    return {
      'text-start': column.align === 'left',
      'text-center': column.align === 'center',
      'text-end': column.align === 'right',
      'sortable': column.sortable
    };
  }
  
  getTotalPages(): number {
    return this.totalPages;
  }
}
