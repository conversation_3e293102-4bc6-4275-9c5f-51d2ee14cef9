import { ChatMessage, ChatType, Conversation } from '../models/chat-analytics.model';
import { format, subDays, parseISO, isWithinInterval } from 'date-fns';

type TimeRange = '24h' | '7d' | '30d' | '90d' | 'custom';

/**
 * Processes chat messages to calculate type distribution
 */
export function calculateChatTypeDistribution(messages: ChatMessage[]): { type: string; count: number; percentage: number }[] {
  // Initialize with all possible chat types from the enum
  const typeCounts: Record<string, number> = {
    [ChatType.UNIFY]: 0,
    [ChatType.COMMUNITY]: 0,
    [ChatType.FAMILY]: 0,
    [ChatType.KNOWLEDGE]: 0,
    [ChatType.DREAMS]: 0,
    [ChatType.SPIRITUALITY]: 0,
    [ChatType.GENERAL]: 0
  };

  // Count occurrences of each chat type
  messages.forEach(msg => {
    // Use the chatType from the message or default to 'General'
    const type = msg.chatType || ChatType.GENERAL;
    // Ensure the type exists in our counts (case-insensitive match)
    const normalizedType = Object.values(ChatType).find(
      t => t.toLowerCase() === type.toString().toLowerCase()
    ) || ChatType.GENERAL;
    
    typeCounts[normalizedType] = (typeCounts[normalizedType] || 0) + 1;
  });

  const total = messages.length || 1; // Avoid division by zero
  
  // Convert to array and calculate percentages
  return Object.entries(typeCounts)
    .map(([type, count]) => ({
      type,
      count,
      percentage: Math.round((count / total) * 1000) / 10 // Round to 1 decimal place
    }))
    .filter(item => item.count > 0) // Only include types with counts > 0
    .sort((a, b) => b.count - a.count); // Sort by count descending
}

/**
 * Groups messages by time intervals for trend analysis
 */
export function groupMessagesByTimeInterval(
  messages: ChatMessage[],
  range: TimeRange = '7d',
  customStart?: Date,
  customEnd?: Date
): { date: string; count: number; messages: ChatMessage[] }[] {
  if (messages.length === 0) return [];

  // Determine date range
  let startDate: Date;
  let endDate = new Date();
  
  switch (range) {
    case '24h':
      startDate = subDays(endDate, 1);
      break;
    case '7d':
      startDate = subDays(endDate, 7);
      break;
    case '30d':
      startDate = subDays(endDate, 30);
      break;
    case '90d':
      startDate = subDays(endDate, 90);
      break;
    case 'custom':
      startDate = customStart || subDays(endDate, 7);
      endDate = customEnd || new Date();
      break;
    default:
      startDate = subDays(endDate, 7);
  }

  // Create time buckets based on range
  const timeBuckets: { [key: string]: { count: number; messages: ChatMessage[] } } = {};
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Initialize time buckets
  const formatString = daysDiff <= 1 ? 'HH:00' : 'MMM dd';
  
  // Group messages into time buckets
  messages.forEach(message => {
    const messageTime = new Date(message.createdAt || message.timestamp || new Date());
    if (!isWithinInterval(messageTime, { start: startDate, end: endDate })) {
      return; // Skip messages outside our date range
    }
    
    const bucketKey = format(messageTime, formatString);
    
    if (!timeBuckets[bucketKey]) {
      timeBuckets[bucketKey] = { count: 0, messages: [] };
    }
    
    timeBuckets[bucketKey].count++;
    timeBuckets[bucketKey].messages.push(message);
  });

  // Convert to array and sort by date
  return Object.entries(timeBuckets)
    .map(([date, data]) => ({
      date,
      count: data.count,
      messages: data.messages
    }))
    .sort((a, b) => {
      // Sort by date
      const dateA = parseISO(a.messages[0]?.createdAt || a.messages[0]?.timestamp || new Date().toISOString());
      const dateB = parseISO(b.messages[0]?.createdAt || b.messages[0]?.timestamp || new Date().toISOString());
      return dateA.getTime() - dateB.getTime();
    });
}

/**
 * Processes conversation volume data
 */
export function processConversationVolume(
  conversations: Conversation[],
  range: TimeRange = '7d',
  customStart?: Date,
  customEnd?: Date
): { values: number[]; categories: string[] } {
  if (!conversations.length) {
    return { values: [], categories: [] };
  }

  const timeGroups = groupMessagesByTimeInterval(
    conversations.flatMap(conv => conv.messages),
    range,
    customStart,
    customEnd
  );

  return {
    values: timeGroups.map(group => group.count),
    categories: timeGroups.map(group => group.date)
  };
}

/**
 * Processes chat type data for chart display
 */
/**
 * Processes chat type data for chart display with consistent ordering and formatting
 */
export function processChatTypeData(
  messages: ChatMessage[]
): { series: number[]; labels: string[] } {
  // Get the distribution with counts and percentages
  const typeDistribution = calculateChatTypeDistribution(messages);
  
  // If no messages, return empty data with a single "No Data" entry
  if (messages.length === 0) {
    return {
      series: [1],
      labels: ['No Data']
    };
  }
  
  // Ensure we have consistent ordering of chat types
  const orderedTypes = [
    ChatType.UNIFY,
    ChatType.COMMUNITY,
    ChatType.FAMILY,
    ChatType.KNOWLEDGE,
    ChatType.DREAMS,
    ChatType.SPIRITUALITY,
    ChatType.GENERAL
  ];
  
  // Create a map for quick lookup
  const typeMap = new Map(typeDistribution.map(item => [item.type, item]));
  
  // Generate ordered series and labels
  const series: number[] = [];
  const labels: string[] = [];
  
  orderedTypes.forEach(type => {
    const item = typeMap.get(type);
    if (item && item.count > 0) {
      series.push(item.count);
      labels.push(type);
    }
  });
  
  // If no data (shouldn't happen due to earlier check, but just in case)
  if (series.length === 0) {
    return {
      series: [1],
      labels: ['No Data']
    };
  }
  
  return { series, labels };
}

/**
 * Processes trend data for chart display
 */
export function processTrendData(
  messages: ChatMessage[],
  range: TimeRange = '7d',
  customStart?: Date,
  customEnd?: Date
): { values: number[]; categories: string[] } {
  const timeGroups = groupMessagesByTimeInterval(messages, range, customStart, customEnd);
  
  return {
    values: timeGroups.map(group => group.count),
    categories: timeGroups.map(group => group.date)
  };
}
