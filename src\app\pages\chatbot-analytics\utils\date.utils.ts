/**
 * Utility functions for date operations
 */

/**
 * Format a date for display in the UI
 * @param date Date string, timestamp, or Date object
 * @returns Formatted date string (e.g., 'Jun 25, 2025, 12:30 PM') or '-' if invalid
 */
export function formatDateForDisplay(date: string | Date | number | null | undefined): string {
  try {
    // Handle null/undefined/empty
    if (!date) return '-';
    
    // Convert to Date object if it's a string or number
    const dateObj = typeof date === 'string' || typeof date === 'number' 
      ? new Date(date) 
      : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      console.warn('Invalid date value:', date);
      return '-';
    }
    
    // Format as: Jun 25, 2025, 12:30 PM
    return dateObj.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZone: 'UTC' // Use UTC to avoid timezone issues
    });
  } catch (e) {
    console.warn('Error formatting date:', date, e);
    return '-';
  }
}

/**
 * Format date for date input (yyyy-MM-dd)
 * @param date Date object or string
 * @returns Formatted date string (yyyy-MM-dd) or empty string if invalid
 */
export function formatDateForInput(date: Date | string | null): string {
  if (!date) return '';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * Format date for API (ISO string)
 * @param date Date object or string
 * @returns ISO date string or empty string if invalid
 */
export function formatDateForApi(date: Date | string): string {
  try {
    if (!date) return '';
    const d = typeof date === 'string' ? new Date(date) : date;
    return isNaN(d.getTime()) ? '' : d.toISOString();
  } catch (e) {
    console.warn('Error formatting date for API:', date, e);
    return '';
  }
}

/**
 * Get the correct timestamp from a message (prefers createdAt, falls back to timestamp)
 * @param msg Message object
 * @returns Timestamp string or current time if none found
 */
export function getMessageTime(msg: any): string {
  return msg?.createdAt || msg?.timestamp || new Date().toISOString();
}

/**
 * Get current timestamp in ISO format
 * @returns Current timestamp as ISO string
 */
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Get start of day in ISO string format
 * @param date Date object or string
 * @returns ISO string of the start of the day
 */
export function getStartOfDay(date: Date | string = new Date()): string {
  const d = typeof date === 'string' ? new Date(date) : new Date(date);
  d.setHours(0, 0, 0, 0);
  return d.toISOString();
}

/**
 * Get end of day in ISO string format
 * @param date Date object or string
 * @returns ISO string of the end of the day
 */
export function getEndOfDay(date: Date | string = new Date()): string {
  const d = typeof date === 'string' ? new Date(date) : new Date(date);
  d.setHours(23, 59, 59, 999);
  return d.toISOString();
}

/**
 * Format date range for API request
 * @param start Start date
 * @param end End date
 * @returns Formatted date range object
 */
export function formatDateRangeForApi(start: Date | string, end: Date | string): { start: string; end: string } {
  return {
    start: start ? formatDateForApi(start) : '',
    end: end ? formatDateForApi(end) : ''
  };
}

/**
 * Calculate date range based on a predefined period
 * @param period The period identifier ('24h', '7d', '30d', '90d')
 * @returns Object with start and end dates
 */
export function calculateDateRange(period: '24h' | '7d' | '30d' | '90d' | 'custom' = '7d'): { start: Date; end: Date } {
  const end = new Date();
  const start = new Date(end);

  switch (period) {
    case '24h':
      start.setDate(end.getDate() - 1);
      break;
    case '7d':
      start.setDate(end.getDate() - 7);
      break;
    case '30d':
      start.setDate(end.getDate() - 30);
      break;
    case '90d':
      start.setDate(end.getDate() - 90);
      break;
    case 'custom':
    default:
      // For custom, we'll just return the current day
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      break;
  }

  return { start, end };
}
