<app-toolbar class="toolbar" [showBack]="false">
  <div class="d-flex align-items-center">
    <h2 class="text-gray-800 fw-bold m-0">Chatbot Prompts</h2>
  </div>
  <div class="card-toolbar">
    <button *ngIf="currentTab === 'userPromptData'" class="btn btn-icon btn-light-primary btn-sm" (click)="fetchData()"
      title="Refresh">
      <app-icon icon="arrows-rotate" size="24"></app-icon>
    </button>
  </div>
</app-toolbar>

<div class="card card-xxl-stretch">
  <!--end::Card header-->

  <!--begin::Tabs-->
  <div class="card-px-0">
    <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fw-bold fs-5">
      <!--begin::Tab item-->
      <li class="nav-item">
        <a class="nav-link text-active-primary pb-5 px-5 cursor-pointer"
          [class.active]="currentTab === 'userPromptData'" (click)="onTabChange('userPromptData')"
          (keydown.enter)="onTabChange('userPromptData')" tabindex="0">
          <app-icon icon="message-text-2" size="24" additionalClasses="me-2"></app-icon>
          User Prompt Data
        </a>
      </li>
      <!--end::Tab item-->

      <!--begin::Tab item-->
      <li class="nav-item">
        <a class="nav-link text-active-primary pb-5 px-5 cursor-pointer" [class.active]="currentTab === 'managePrompt'"
          (click)="onTabChange('managePrompt')" (keydown.enter)="onTabChange('managePrompt')" tabindex="0">
          <app-icon icon="setting-3" size="24" additionalClasses="me-2"></app-icon>
          Manage Prompt
        </a>
      </li>
      <!--end::Tab item-->

      <!--begin::Tab item-->
      <li class="nav-item">
        <a class="nav-link text-active-primary pb-5 px-5 cursor-pointer" [class.active]="currentTab === 'userChatbot'"
          (click)="onTabChange('userChatbot')" (keydown.enter)="onTabChange('userChatbot')" tabindex="0">
          <app-icon icon="messages" size="24" additionalClasses="me-2"></app-icon>
          User Chatbot
        </a>
      </li>
      <!--end::Tab item-->
    </ul>
  </div>
  <!--end::Tabs-->

  <!--begin::Tab content-->
  <div class="card-body pt-0">
    <!--begin::User Prompt Data Tab-->
    <div *ngIf="currentTab === 'userPromptData'" class="tab-pane fade show active">
      <app-user-prompt-table [dataSource]="dataSource" [loading]="loading"
        (promptDataView)="showPromptData($event)"></app-user-prompt-table>
    </div>
    <!--end::User Prompt Data Tab-->

    <!--begin::Manage Prompt Tab-->
    <div *ngIf="currentTab === 'managePrompt'" class="tab-pane fade show">
      <app-manage-prompt></app-manage-prompt>
    </div>
    <!--end::Manage Prompt Tab-->

    <!--begin::User Chatbot Tab-->
    <div *ngIf="currentTab === 'userChatbot'" class="tab-pane fade show">
      <app-user-chatbot></app-user-chatbot>
    </div>
    <!--end::User Chatbot Tab-->
  </div>
  <!--end::Tab content-->
</div>