import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subject, from } from 'rxjs';
import { takeUntil, finalize } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { API, graphqlOperation } from 'aws-amplify';

import { listChatbotPromptsData } from './graphql/chatbot-prompts.queries';
import { PromptDataDialogComponent } from './shared/prompt-data-dialog/prompt-data-dialog.component';

@Component({
  selector: 'app-chatbot-prompts-list',
  templateUrl: './chatbot-prompts-list.component.html',
  styleUrls: ['./chatbot-prompts-list.component.scss']
})
export class ChatbotPromptsListComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  displayedColumns: string[] = ['givenname', 'familyname', 'chatType', 'promptData', 'actions'];
  dataSource = new MatTableDataSource<any>([]);
  loading = false;
  currentTab: 'userPromptData' | 'managePrompt' | 'userChatbot' = 'userPromptData';

  constructor(private readonly dialog: MatDialog) {}

  ngOnInit() {
    this.fetchData();
  }

  fetchData(): void {
    this.loading = true;

    from(API.graphql(graphqlOperation(listChatbotPromptsData)))
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: (result: any) => {
          // Filter out records where userData is null
          const items = result?.data?.listChatbotPromptsData?.items || [];
          this.dataSource.data = items.filter((item: any) => item.userData !== null);
        },
        error: (error) => {
          console.error('Error fetching data', error);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  showPromptData(promptData: string) {
    this.dialog.open(PromptDataDialogComponent, {
      data: { promptData }
    });
  }

  /**
   * Handles tab change events
   * @param tab The tab to switch to
   */
  onTabChange(tab: 'userPromptData' | 'managePrompt' | 'userChatbot'): void {
    this.currentTab = tab;
  }

}
