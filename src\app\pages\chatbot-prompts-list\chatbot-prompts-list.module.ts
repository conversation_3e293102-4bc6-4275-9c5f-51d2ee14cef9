import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// Metronic Modules
import { LayoutModule } from '../../_metronic/layout/layout.module';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrModule } from 'ngx-toastr';

// Components
import { ChatbotPromptsListComponent } from './chatbot-prompts-list.component';
import { ChatbotPromptsListRoutingModule } from './chatbot-prompts-list-routing.module';
import { UserChatbotComponent } from './shared/user-chatbot/user-chatbot.component';
import { UserChatbotWidgetComponent } from './shared/user-chatbot-widget/user-chatbot-widget.component';
import { UserPromptTableComponent } from './shared/user-prompt-table/user-prompt-table.component';
import { ManagePromptComponent } from './shared/manage-prompt/manage-prompt.component';
import { PromptDataDialogComponent } from './shared/prompt-data-dialog/prompt-data-dialog.component';
import { JsonViewerComponent } from './shared/json-viewer/json-viewer.component';
import { UserSearchDropdownComponent } from 'src/app/shared/formcontrol/user-search-dropdown/user-search-dropdown.component';
import { AppIconComponent } from '../../shared/formcontrol/app-icon/app-icon.component';

@NgModule({
  declarations: [
    ChatbotPromptsListComponent
  ],
  imports: [
    CommonModule,
    ChatbotPromptsListRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    LayoutModule,
    UserSearchDropdownComponent,
    InlineSVGModule,
    NgbTooltipModule,
    ToastrModule.forRoot({
      timeOut: 3000,
      positionClass: 'toast-top-right',
      preventDuplicates: true,
    }),
    // Standalone components
    UserChatbotComponent,
    UserChatbotWidgetComponent,
    UserPromptTableComponent,
    ManagePromptComponent,
    PromptDataDialogComponent,
    JsonViewerComponent,
    AppIconComponent // This is a standalone component
  ]
})
export class ChatbotPromptsListModule { }
