export const listChatbotPromptsData = `query ListChatbotPromptsData {
  listChatbotPromptsData(filter: {isDeleted: {ne: "true"}}) {
    items {
      id
      userId
      userData {
        givenName
        familyName
      }
      promptData
      chatType
    }
  }
}`;

export const listChatbotPrompts = /* GraphQL */ `
query ListChatbotPrompts($filter: ModelChatbotPromptFilterInput, $limit: Int, $nextToken: String) {
  listChatbotPrompts(filter: $filter, limit: $limit, nextToken: $nextToken) {
    items {
      id
      chatType
      title
      description
      promptText
      lastUpdated
      _version
      _deleted
      _lastChangedAt
    }
    nextToken
    startedAt
  }
}
`;

export const createChatbotPrompt = /* GraphQL */ `
mutation CreateChatbotPrompt($input: CreateChatbotPromptInput!, $condition: ModelChatbotPromptConditionInput) {
  createChatbotPrompt(input: $input, condition: $condition) {
    id
    chatType
    title
    description
    promptText
    lastUpdated
    _version
    _deleted
    _lastChangedAt
  }
}
`;

export const updateChatbotPrompt = /* GraphQL */ `
mutation UpdateChatbotPrompt($input: UpdateChatbotPromptInput!, $condition: ModelChatbotPromptConditionInput) {
  updateChatbotPrompt(input: $input, condition: $condition) {
    id
    chatType
    title
    description
    promptText
    lastUpdated
    _version
    _deleted
    _lastChangedAt
  }
}
`;