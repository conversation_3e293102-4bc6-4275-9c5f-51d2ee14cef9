<ng-container *ngIf="isObject(data)">
  <ul class="json-viewer-list">
    <li *ngFor="let key of objectKeys(data)">
      <span class="json-key">{{ key | titlecase }}:</span>
      <ng-container *ngIf="isObject(data[key]) || isArray(data[key]); else primitive">
        <button class="btn btn-xs btn-link p-0 ms-1" (click)="toggle(key)">
          <span [ngClass]="{ 'bi-caret-down-fill': isExpanded(key), 'bi-caret-right-fill': !isExpanded(key) }" class="bi"></span>
        </button>
        <div *ngIf="isExpanded(key)" class="json-nested">
          <app-json-viewer [data]="data[key]"></app-json-viewer>
        </div>
      </ng-container>
      <ng-template #primitive>
        <span class="json-value">{{ data[key] }}</span>
      </ng-template>
    </li>
  </ul>
</ng-container>
<ng-container *ngIf="isArray(data)">
  <ul class="json-viewer-list">
    <li *ngFor="let item of data; let i = index">
      <span class="json-key">[{{ i }}]:</span>
      <app-json-viewer [data]="item"></app-json-viewer>
    </li>
  </ul>
</ng-container>
<ng-container *ngIf="!isObject(data) && !isArray(data)">
  <span class="json-value">{{ data }}</span>
</ng-container>
