import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-json-viewer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './json-viewer.component.html',
  styleUrls: ['./json-viewer.component.scss']
})
export class JsonViewerComponent {
  @Input() data: any;
  expanded: { [key: string]: boolean } = {};
  objectKeys(obj: any): string[] {
    return obj ? Object.keys(obj) : [];
  }
  isArray(val: any): boolean {
    return Array.isArray(val);
  }
  isObject(val: any): boolean {
    return val && typeof val === 'object' && !Array.isArray(val);
  }
  toggle(key: string) {
    this.expanded[key] = !this.expanded[key];
  }
  isExpanded(key: string): boolean {
    return !!this.expanded[key];
  }
}
