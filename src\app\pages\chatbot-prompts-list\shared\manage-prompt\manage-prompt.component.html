<div class="card">
  <!--begin::Card header-->
  <div class="card-header border-0 pt-6">
    <!--begin::Card title-->
    <div class="card-title">
      <h3 class="card-label fw-bolder text-gray-800">Manage Chatbot Prompts</h3>
    </div>
    <!--end::Card title-->
  </div>
  <!--end::Card header-->
  
  <!--begin::Card body-->
  <div class="card-body pt-0">
    <form [formGroup]="form" class="form">
      <!--begin::Chat Type Selection - Moved outside prompts array-->
      <div class="mb-5">
        <ul ngbNav #nav="ngbNav" [activeId]="form.get('selectedChatType')?.value" (activeIdChange)="onChatTypeChange($event)" class="nav nav-tabs nav-line-tabs mb-5 fs-6">
            <li [ngbNavItem]="chatType" *ngFor="let chatType of chatTypes">
                <a ngbNavLink>{{ chatType }}</a>
            </li>
        </ul>
      </div>
      <!--end::Chat Type Selection-->

      <!--begin::Prompts List-->
      <div formArrayName="prompts" class="prompts-list">
        <div *ngFor="let promptGroup of promptsFormArray.controls; let i = index" [formGroup]="promptGroup"
          class="card mb-5 mb-xl-8">
          <!--begin::Card body-->
          <div class="card-body pt-0">
            <div class="card-body">
              <!-- Hidden fields that are still part of the form -->
              <div style="display: none;">
                <input type="text" formControlName="id">
                <input type="text" formControlName="title">
                <input type="text" formControlName="chatType">
                <textarea formControlName="description"></textarea>
                <input type="text" formControlName="lastUpdated">
              </div>

              <!-- Only show the prompt text field -->
              <div class="mb-10">
                <div class="row g-5">
                  <div class="col-12">
                    <label class="form-label fw-semibold">Prompt Template <span class="text-danger">*</span></label>
                    <textarea class="form-control form-control-solid" formControlName="promptText" 
                      rows="15" placeholder="Enter your prompt template here..." required></textarea>
                    <div class="form-text">
                      Use variables like {{ '${question}' }}, {{ '${userId}' }} etc. in your prompt template.
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--begin::Card footer-->
            <div class="card-footer d-flex justify-content-end py-4 px-6">
              <button type="button" class="btn btn-light btn-active-light-primary me-2" (click)="promptGroup.reset()">
                <i class="ki-duotone ki-arrow-rotate-left fs-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                Reset
              </button>
              <button type="button" class="btn btn-primary" (click)="savePrompt(i)" [disabled]="!promptGroup.valid">
                <i class="ki-duotone ki-save-2 fs-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                Save Prompt
              </button>
            </div>
            <!--end::Card footer-->
          </div>
        </div>
      </div>
      <!--end::Prompts List-->
    </form>
  </div>
  <!--end::Card body-->
</div>