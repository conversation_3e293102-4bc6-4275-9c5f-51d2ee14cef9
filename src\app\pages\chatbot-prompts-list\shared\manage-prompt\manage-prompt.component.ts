import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormArray, FormBuilder, FormControl, FormGroup, FormsModule, Validators, AbstractControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { NgbTooltipModule, NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { API } from 'aws-amplify';

import { createChatbotPrompt, updateChatbotPrompt, listChatbotPrompts } from '../../graphql/chatbot-prompts.queries';
import { ChatType } from 'src/app/pages/organizations/components/organization-chatbot-widget/models/chat.model';

interface ChatbotPrompt {
    id: string;
    chatType: ChatType;
    title: string;
    description: string;
    promptText: string;
    lastUpdated: string;
    _version?: number;
    _deleted?: boolean | null;
    _lastChangedAt?: number;
}

interface ChatbotPromptInput {
    id?: string;
    chatType?: ChatType;
    title?: string;
    description?: string;
    promptText?: string;
    lastUpdated?: string;
    _version?: number;
}

interface PromptFormGroup extends FormGroup {
    value: {
        id: string;
        chatType: ChatType;
        title: string;
        description: string;
        promptText: string;
        lastUpdated: string;
    };
    controls: {
        id: FormControl<string>;
        chatType: FormControl<ChatType>;
        title: FormControl<string>;
        description: FormControl<string>;
        promptText: FormControl<string>;
        lastUpdated: FormControl<string>;
    };
}

@Component({
    selector: 'app-manage-prompt',
    templateUrl: './manage-prompt.component.html',
    styleUrls: ['./manage-prompt.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        InlineSVGModule,
        NgbTooltipModule,
        NgbNavModule
    ]
})
export class ManagePromptComponent implements OnInit, OnDestroy {
    chatTypes = [ChatType.UNIFY, ChatType.KNOWLEDGE, ChatType.COMMUNITY, ChatType.FAMILY];
    selectedChatType: ChatType = ChatType.KNOWLEDGE;
    form: FormGroup<{
        selectedChatType: FormControl<ChatType | null>;
        prompts: FormArray<PromptFormGroup>;
    }>;
    showFormControls = true;
    private readonly destroy$ = new Subject<void>();

    constructor(
        private readonly fb: FormBuilder,
        private readonly toastr: ToastrService
    ) {
        this.initializeForm();
    }

    ngOnInit(): void {
        // Set the first chat type as default and immediately load data for it.
        if (this.chatTypes.length > 0) {
            const initialChatType = this.chatTypes[0];
            this.form.get('selectedChatType')?.setValue(initialChatType);
            this.selectedChatType = initialChatType; // Keep component property in sync
            this.loadPromptData(initialChatType);
        }

        // Subscribe to valueChanges of the form control to load data when chat type changes
        this.form.get('selectedChatType')?.valueChanges.pipe(
            takeUntil(this.destroy$)
        ).subscribe((chatType: ChatType) => {
            this.onChatTypeChange(chatType);
        });
    }

    onChatTypeChange(chatType: ChatType): void {
        this.selectedChatType = chatType;
        this.loadPromptData(chatType);
    }

    private initializeForm(): void {
        this.form = this.fb.group({
            selectedChatType: [this.selectedChatType, Validators.required],
            prompts: this.fb.array<PromptFormGroup>([]) // Will be populated in loadPromptData
        }) as FormGroup<{
            selectedChatType: FormControl<ChatType | null>;
            prompts: FormArray<PromptFormGroup>;
        }>;
    }

    get promptsFormArray(): FormArray<PromptFormGroup> {
        return this.form.get('prompts') as FormArray<PromptFormGroup>;
    }

    private createPromptFormGroup(prompt?: Partial<ChatbotPrompt>): PromptFormGroup {
        return this.fb.group({
            id: [prompt?.id || ''],
            chatType: [prompt?.chatType || this.selectedChatType, Validators.required],
            title: [prompt?.title || '', Validators.required],
            description: [prompt?.description || ''],
            promptText: [prompt?.promptText || '', Validators.required],
            lastUpdated: [prompt?.lastUpdated || new Date().toISOString()],
            ...(prompt?._version && { _version: [prompt._version] }),
        }) as unknown as PromptFormGroup;
    }

    private async loadPromptData(chatType: ChatType): Promise<void> {
        try {
            const result: any = await API.graphql({
                query: listChatbotPrompts,
                variables: { filter: { chatType: { eq: chatType } } },
                authMode: 'AMAZON_COGNITO_USER_POOLS'
            });

            const prompts: ChatbotPrompt[] = (result.data?.listChatbotPrompts?.items ?? []).map(prompt => ({
                ...prompt,
                // Ensure chatType is properly formatted to match enum values
                chatType: Object.values(ChatType).find(
                    type => type.toLowerCase() === prompt.chatType.toLowerCase()
                ) || prompt.chatType
            }));

            this.promptsFormArray.clear();

            if (prompts.length > 0) {
                // Use the first prompt found for this chat type
                this.promptsFormArray.push(this.createPromptFormGroup(prompts[0]));
            } else {
                // Add a new empty prompt if none exists for this chat type
                const chatTypeName = chatType.charAt(0).toUpperCase() + chatType.slice(1).toLowerCase();
                this.promptsFormArray.push(this.createPromptFormGroup({
                    chatType,
                    title: `New ${chatTypeName} Prompt`,
                    description: `Prompt for ${chatTypeName} chat`,
                    promptText: `You are a helpful ${chatTypeName.toLowerCase()} assistant.\n\nUser: {{ '${'${'}question}' }}\nAssistant: `,
                    lastUpdated: new Date().toISOString()
                }));
            }
        } catch (error) {
            console.error('Error loading prompts:', error);
            this.showError('Failed to load prompts');
        }
    }

    async savePrompt(index: number): Promise<void> {
        try {
            const promptGroup = this.promptsFormArray.at(index) as FormGroup;
            if (!promptGroup.valid) {
                this.markFormGroupTouched(promptGroup);
                this.toastr.error('Please fill in all required fields');
                return;
            }

            const formValue = promptGroup.value as ChatbotPrompt;

            // Ensure chatType is properly formatted to match enum values
            const formattedChatType = Object.values(ChatType).find(
                type => type.toLowerCase() === formValue.chatType.toLowerCase()
            ) || formValue.chatType;

            // Prepare input object without id for new prompts
            const input: ChatbotPromptInput = {
                ...formValue,
                chatType: formattedChatType,
                lastUpdated: new Date().toISOString()
            };

            // Remove id if it's empty to prevent DynamoDB validation error
            if (!input.id) {
                delete input.id;
            }

            // If this is an update, include the _version
            if (formValue._version) {
                input._version = formValue._version;
            }

            try {
                if (formValue.id) {
                    // Try to update first
                    try {
                        const result: any = await API.graphql({
                            query: updateChatbotPrompt,
                            variables: { input },
                            authMode: 'AMAZON_COGNITO_USER_POOLS'
                        });
                        const savedPrompt = result.data.updateChatbotPrompt as ChatbotPrompt;
                        // Update all fields from the server response to ensure consistency
                        promptGroup.patchValue({
                            ...savedPrompt
                        }, { emitEvent: false });
                        this.showSuccess('Prompt updated successfully');
                    } catch (updateError) {
                        // If update fails with ConditionalCheckFailedException, try to create instead
                        if (updateError.errors?.[0]?.errorType === 'ConditionalCheckFailedException') {
                            const { id, ...createInput } = input; // Remove id for create operation
                            const result: any = await API.graphql({
                                query: createChatbotPrompt,
                                variables: { input: createInput },
                                authMode: 'AMAZON_COGNITO_USER_POOLS'
                            });
                            const savedPrompt = result.data.createChatbotPrompt as ChatbotPrompt;
                            // Update all fields from the server response to ensure consistency
                            promptGroup.patchValue({
                                ...savedPrompt
                            }, { emitEvent: false });
                            this.showSuccess('New prompt created successfully');
                        } else {
                            throw updateError;
                        }
                    }
                } else {
                    // Create new prompt
                    const result: any = await API.graphql({
                        query: createChatbotPrompt,
                        variables: { input },
                        authMode: 'AMAZON_COGNITO_USER_POOLS'
                    });
                    const savedPrompt = result.data.createChatbotPrompt as ChatbotPrompt;
                    // Update all fields from the server response to ensure consistency
                    promptGroup.patchValue({
                        ...savedPrompt
                    }, { emitEvent: false });
                    this.showSuccess('Prompt created successfully');
                }
                // Refresh the data to ensure consistency
                await this.loadPromptData(this.selectedChatType);
            } catch (error: any) {
                console.error('Error saving prompt:', error);
                this.showError('Failed to save prompt: ' + (error.message ?? 'Unknown error'));
            }
        } catch (error: any) {
            console.error('Error saving prompt:', error);
            this.showError('Failed to save prompt: ' + (error.message ?? 'Unknown error'));
        }
    }

    private markFormGroupTouched(formGroup: FormGroup): void {
        Object.values(formGroup.controls).forEach((control: AbstractControl) => {
            control.markAsTouched();
            if (control instanceof FormGroup) {
                this.markFormGroupTouched(control);
            }
        });
    }

    private showSuccess(message: string): void {
        this.toastr.success(message, 'Success', {
            timeOut: 3000,
            progressBar: true,
            positionClass: 'toast-top-right'
        });
    }

    private showError(message: string): void {
        this.toastr.error(message, 'Error', {
            timeOut: 5000,
            progressBar: true,
            positionClass: 'toast-top-right'
        });
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }
}
