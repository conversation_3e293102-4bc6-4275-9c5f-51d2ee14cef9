<h2 mat-dialog-title class="fw-bold fs-2 mb-4 text-primary">Prompt Data</h2>
<mat-dialog-content>
  <ng-container *ngIf="parsedData; else invalidJson">
    <div class="card card-flush bg-light p-4 mb-3 position-relative" style="border-radius: 12px; max-height: 400px; overflow:auto;">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <span class="fw-bold fs-5">Prompt Details</span>
        <button mat-icon-button color="primary" (click)="copyJson()" title="Copy JSON">
          <i class="bi bi-clipboard"></i>
        </button>
      </div>
      <app-json-viewer [data]="parsedData"></app-json-viewer>
    </div>
  </ng-container>
  <ng-template #invalidJson>
    <span class="text-danger">Invalid prompt data</span>
  </ng-template>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close color="primary" class="fw-bold">Close</button>
</mat-dialog-actions>
