import { Component, Inject } from '@angular/core';
import { MatDialogModule, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { JsonViewerComponent } from '../json-viewer/json-viewer.component';

@Component({
  selector: 'app-prompt-data-dialog',
  standalone: true,
  imports: [MatDialogModule, MatButtonModule, CommonModule, JsonViewerComponent],
  templateUrl: './prompt-data-dialog.component.html',
  styleUrls: ['./prompt-data-dialog.component.scss']
})
export class PromptDataDialogComponent {
  parsedData: any = null;
  constructor(@Inject(MAT_DIALOG_DATA) public data: { promptData: string }) {
    try {
      this.parsedData = JSON.parse(data.promptData);
    } catch {
      this.parsedData = null;
    }
  }

  copyJson() {
    if (this.parsedData) {
      const jsonStr = JSON.stringify(this.parsedData, null, 2);
      navigator.clipboard.writeText(jsonStr).then(() => {
        // You can replace this with a toast/snackbar
        alert('JSON copied to clipboard!');
      });
    }
  }
}
