<div [ngClass]="cssClass" class="card">
  <div class="card-header border-0 pt-5">
    <div class="d-flex justify-content-between align-items-center flex-nowrap w-100" style="gap: 24px;">
      <div class="flex-grow-1"></div>
      <div class="d-flex align-items-center flex-nowrap justify-content-end" style="gap: 24px; min-width: 500px;">
        <div class="d-flex align-items-center">
          <label class="form-label fw-semibold me-3 mb-0" for="selectedChatType">Chat Type:</label>
          <select class="form-select form-select-sm form-select-solid w-150px" 
                  [(ngModel)]="selectedChatType" 
                  (ngModelChange)="onChatTypeChange($event)">
            <option *ngFor="let type of chatTypes" [ngValue]="type.value">
              {{ type.label }}
            </option>
          </select>
        </div>
        <app-user-search-dropdown 
          [users]="cityUsers"
          [selectedUserId]="selectedUser?.id"
          (userChange)="onUserSelected($event)">
        </app-user-search-dropdown>
      </div>
    </div>
  </div>
  <div class="card-body d-flex flex-column" [ngStyle]="{'height': widgetHeight}">
    <div *ngIf="!selectedUser" class="d-flex align-items-center justify-content-center h-100 chatbot-card-empty">
      <div class="text-center">
        <i class="bi bi-robot display-3 text-primary mb-2"></i>
        <h3 class="text-primary">Chatbot</h3>
        <p class="lead">Please select a user for chatbot to continue.</p>
      </div>
    </div>
    <div #messagesContainer class="messages-container flex-grow-1 overflow-auto mb-5" *ngIf="selectedUser" style="background: #f5f6fa; border-radius: 8px; padding: 16px;">
      <div *ngIf="isLoading" class="d-flex align-items-center justify-content-center h-100">
        <span>Loading chat...</span>
      </div>
      <div *ngIf="!isLoading">
        <ng-container *ngFor="let message of messages; let i = index">
          <ng-container *ngIf="i === 0 || (message.timestamp | date:'yyyy-MM-dd') !== (messages[i-1]?.timestamp | date:'yyyy-MM-dd')">
            <div class="chat-date-separator text-center my-2">
              <span class="badge bg-light text-dark px-3 py-1">{{ message.timestamp | date:'fullDate' }}</span>
            </div>
          </ng-container>
          <div class="d-flex mb-4"
            [ngClass]="{'justify-content-end': message.sender === 'user'}">
            <div *ngIf="message.sender === 'bot'" class="d-flex align-items-start">
              <div class="symbol symbol-35px symbol-circle me-3" style="background-color: #1e1e2d;">
                <img [alt]="'Bot Logo'" [src]="defaultLogo" />
              </div>
              <div class="d-flex flex-column">
                <div class="bg-light-primary rounded p-3">
                  <span class="text-dark">{{message.content}}</span>
                </div>
                <span class="text-muted fs-7 mt-1">{{message.timestamp | date:'shortTime'}} </span>
              </div>
            </div>
            <div *ngIf="message.sender === 'user'" class="d-flex align-items-start">
              <div class="d-flex flex-column align-items-end">
                <div class="bg-primary rounded p-3">
                  <span class="text-white">{{message.content}}</span>
                </div>
                <span class="text-muted fs-7 mt-1">{{message.timestamp | date:'shortTime'}}</span>
              </div>
              <div class="symbol symbol-35px symbol-circle ms-3">
                <img [alt]="'User Profile'" [src]="getUserAvatar()" />
              </div>
            </div>
          </div>
        </ng-container>
        <div *ngIf="isTyping" class="d-flex align-items-center mb-4">
          <div class="symbol symbol-35px symbol-circle me-3" style="background-color: #1e1e2d;">
            <img [alt]="'Bot Logo'" [src]="defaultLogo" />
          </div>
          <div class="d-flex flex-column">
            <div class="bg-light-primary rounded p-3">
              <span class="text-dark">Typing...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="chat-input-container">
      <form class="position-relative" (ngSubmit)="sendMessage()">
        <input type="text" class="form-control form-control-solid pe-10" name="search" [(ngModel)]="newMessage"
          placeholder="Type your message" />
        <button type="submit" class="btn btn-primary position-absolute top-50 end-0 translate-middle-y me-2">
          <i class="bi bi-send-fill"></i>
        </button>
      </form>
    </div>
  </div>
</div>