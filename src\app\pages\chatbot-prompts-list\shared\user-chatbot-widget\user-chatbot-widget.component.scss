// Chat widget container
.chat-widget {
  display: flex;
  flex-direction: column;
  height: 600px;
  background: #fff;
  border-radius: 0.475rem;
  box-shadow: 0 0 20px 0 rgba(76, 87, 125, 0.05);
  overflow: hidden;
}

// Messages container
.messages-container {
  flex: 1;
  max-height: 100%;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f5f8fa;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #e4e6ef;
    border-radius: 3px;
    
    &:hover {
      background: #b5b5c3;
    }
  }
}

// Chat message styles
.message {
  max-width: 80%;
  padding: 0.75rem 1rem;
  border-radius: 0.475rem;
  font-size: 0.95rem;
  line-height: 1.5;
  position: relative;
  
  &-user {
    align-self: flex-end;
    background-color: #f1faff;
    color: #009ef7;
    border-top-right-radius: 0;
  }
  
  &-bot {
    align-self: flex-start;
    background-color: #f5f8fa;
    color: #5e6278;
    border-top-left-radius: 0;
  }
  
  &-time {
    display: block;
    font-size: 0.7rem;
    opacity: 0.7;
    margin-top: 0.25rem;
    text-align: right;
  }
}

// Input container
.chat-input-container {
  margin-top: auto;
  padding: 1.25rem 1.5rem;
  border-top: 1px solid #eff2f5;
  background-color: #f8f9fa;
  
  .input-group {
    display: flex;
    gap: 0.75rem;
    
    .form-control {
      flex: 1;
      border: 1px solid #e4e6ef;
      border-radius: 0.475rem;
      padding: 0.75rem 1rem;
      font-size: 0.95rem;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
      
      &:focus {
        border-color: #009ef7;
        box-shadow: 0 0 0 3px rgba(0, 158, 247, 0.1);
        outline: none;
      }
    }
    
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 1.25rem;
      background-color: #009ef7;
      color: #fff;
      border: none;
      border-radius: 0.475rem;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s ease;
      
      &:hover {
        background-color: #0095e8;
      }
      
      &:disabled {
        background-color: #e4e6ef;
        cursor: not-allowed;
      }
      
      .mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

// Typing indicator
.typing-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f5f8fa;
  border-radius: 1.5rem;
  margin-bottom: 1rem;
  
  span {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #b5b5c3;
    border-radius: 50%;
    margin: 0 2px;
    animation: bounce 1.5s infinite ease-in-out;
    
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.4s; }
  }
}

@keyframes bounce {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-5px); }
}

// Responsive adjustments
@media (max-width: 768px) {
  .chat-widget {
    height: 500px;
  }
  
  .message {
    max-width: 90%;
    font-size: 0.9rem;
  }
  
  .chat-input-container {
    padding: 1rem;
    
    .input-group {
      .form-control {
        padding: 0.65rem 0.9rem;
      }
      
      .btn {
        padding: 0 1rem;
      }
    }
  }
}
