import { Component, Input, OnInit, ViewChild, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { Storage } from 'aws-amplify';
import { finalize } from 'rxjs';

import { ChatService } from 'src/app/pages/organizations/components/organization-chatbot-widget/services/chat.service';
import { ChatMessage, ChatType } from 'src/app/pages/organizations/components/organization-chatbot-widget/models/chat.model';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { UserSearchDropdownComponent } from 'src/app/shared/formcontrol/user-search-dropdown/user-search-dropdown.component';

@Component({
  selector: 'app-user-chatbot-widget',
  standalone: true,
  imports: [
    CommonModule, FormsModule, UserSearchDropdownComponent,
    MatFormFieldModule, MatSelectModule, MatInputModule, MatAutocompleteModule
  ],
  templateUrl: './user-chatbot-widget.component.html',
  styleUrls: ['./user-chatbot-widget.component.scss']
})
export class UserChatbotWidgetComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() color = '';
  @Input() cssClass = '';
  @Input() widgetHeight = '';
  private _cityUsers: any[] = [];
  @Input()
  set cityUsers(users: any[]) {
    this._cityUsers = users || [];
    // If we have a selected user but it's not in the new users array, clear the selection
    if (this.selectedUser && !this._cityUsers.some(u => u.id === this.selectedUser?.id)) {
      this.selectedUser = null;
      this.messages = [];
    }
  }
  get cityUsers(): any[] {
    return this._cityUsers;
  }
  @ViewChild('messagesContainer') private readonly messagesContainer: ElementRef;

  readonly defaultLogo: string = 'assets/media/logos/my-village-logo.png';
  readonly defaultAvatar: string = 'assets/media/avatars/blank.png';

  selectedUser: any = null;
  userProfileImage: string = '';
  messages: ChatMessage[] = [];
  newMessage: string = '';
  isLoading: boolean = false;
  isTyping: boolean = false;
  searchText: string = '';
  filteredUsers: any[] = [];

  chatTypes = [
    { label: 'Unify', value: ChatType.UNIFY },
    { label: 'Community', value: ChatType.COMMUNITY },
    { label: 'Family', value: ChatType.FAMILY },
    { label: 'Knowledge', value: ChatType.KNOWLEDGE },
    { label: 'Dreams', value: ChatType.DREAMS },
    { label: 'Spirituality', value: ChatType.SPIRITUALITY },
  ];
  selectedChatType: ChatType = ChatType.UNIFY;


  constructor(
    private readonly chatService: ChatService,
    private readonly spinner: NgxSpinnerService,
    private readonly cdr: ChangeDetectorRef
  ) { }

  ngOnInit() {
    this.filteredUsers = this.cityUsers;
    if (this.selectedUser?.id) {
      this.callFetchChatbotData(this.selectedUser.id, this.selectedChatType).then();
    }
  }

  ngOnDestroy() {
    if (this.selectedUser?.id) {
      this.callRemoveChatbotData(this.selectedUser.id).then();
    }
  }

  ngAfterViewInit() {
    this.scrollToBottom();
  }

  scrollToBottom(): void {
    try {
      if (this.messagesContainer?.nativeElement) {
        setTimeout(() => {
          this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;
        }, 0);
      }
    } catch (error) { console.error(error); }
  }

  filterUsers(): void {
    const text = this.searchText.toLowerCase();
    this.filteredUsers = this.cityUsers.filter(u => u.name?.toLowerCase().includes(text));
  }

  async onChatTypeChange(chatType: ChatType) {
    this.selectedChatType = chatType;
    // Reset messages when chat type changes
    this.messages = [];
    this.loadChatHistory();
  }

  async onUserSelected(userId: string | null) {
    if (!userId) {
      this.selectedUser = null;
      this.messages = [];
      return;
    }

    const user = this.cityUsers.find(u => u.id === userId);
    if (!user) {
      console.warn(`User with id ${userId} not found in cityUsers`);
      return;
    }

    try {
      this.selectedUser = user;
      this.loadUserImage();

      // Initialize the chatbot and wait for it to complete
      await this.callFetchChatbotData(user.id, this.selectedChatType);

      // Then load the chat history
      this.loadChatHistory();
    } catch (error) {
      console.error('Error initializing chat:', error);
      this.selectedUser = null;
      this.messages = [];
    }
  }

  private loadChatHistory() {
    if (!this.selectedUser) {
      console.warn('No user selected to load chat history');
      return;
    }

    this.messages = [];
    this.isLoading = true;

    this.chatService.fetchChatHistory(this.selectedUser.id, this.selectedChatType).subscribe({
      next: (messages) => {
        try {
          if (!messages) {
            console.warn('Received empty or undefined messages array');
            messages = [];
          }
          this.messages = Array.isArray(messages) ? messages : [];

          if (this.messages.length === 0) {
            this.addWelcomeMessage();
          }
        } catch (error) {
          console.error('Error processing chat history:', error);
          this.addWelcomeMessage();
        } finally {
          this.isLoading = false;
          this.cdr.detectChanges();
          this.scrollToBottom();
        }
      },
      error: (error) => {
        console.error('Error fetching chat history:', error);
        this.addWelcomeMessage();
        this.isLoading = false;
        this.cdr.detectChanges();
        this.scrollToBottom();
      }
    });
  }

  private loadUserProfileImage() {
    if (this.selectedUser?.imageUrl) {
      Storage.get(this.selectedUser.imageUrl, { level: 'public' })
        .then((result: string) => {
          this.userProfileImage = result || this.defaultAvatar;
        });
    } else {
      this.userProfileImage = this.defaultAvatar;
    }
  }

  async onUserChange(userId: string): Promise<void> {
    if (this.selectedUser?.id) {
      await this.callRemoveChatbotData(this.selectedUser.id);
    }
    const user = this.cityUsers.find(u => u.id === userId);
    if (user) {
      this.selectedUser = user;
      this.loadUserImage();
      await this.callFetchChatbotData(user.id, this.selectedChatType);
      this.loadChatHistory();
    }
  }

  private async callFetchChatbotData(userId: string, chatType: string) {
    this.isLoading = true;
    await this.chatService.initializeChatbot(userId, chatType).toPromise();
    this.isLoading = false;
  }

  private async callRemoveChatbotData(userId: string) {
    await this.chatService.removeChatbotData(userId).toPromise();
  }

  private loadUserImage(): void {
    if (this.selectedUser?.imageUrl) {
      Storage.get(this.selectedUser.imageUrl, { level: 'public' })
        .then((result: string) => {
          this.userProfileImage = result;
        });
    } else {
      this.userProfileImage = this.defaultAvatar;
    }
  }

  private addWelcomeMessage(): void {
    this.messages = [
      ...this.messages,
      { sender: 'bot', content: `Welcome! How can I help you today?`, timestamp: new Date() }
    ];
  }

  sendMessage(): void {
    const message = this.newMessage.trim();
    if (!message || !this.selectedUser) return;
    const messageData = {
      userId: this.selectedUser.id,
      chatType: this.selectedChatType,
      message,
      createdAt: new Date().toISOString(),
      role: 'user',
      messageType: 'TEXT',
      fileUrl: '',
      isDeleted: 'false'
    };
    this.messages = [
      ...this.messages,
      { sender: 'user', content: message, timestamp: new Date() }
    ];
    this.newMessage = '';
    this.isTyping = true;
    this.cdr.detectChanges();
    this.scrollToBottom();
    this.chatService.sendMessageWithBot(
      messageData,
      this.selectedUser.id,
      this.selectedChatType,
      message
    )
      .pipe(finalize(() => this.isTyping = false))
      .subscribe({
        next: (botResponse) => {
          this.messages = [
            ...this.messages,
            { sender: 'bot', content: botResponse, timestamp: new Date() }
          ];
          this.cdr.detectChanges();
          this.scrollToBottom();
        }
      });
  }

  getUserAvatar(): string {
    if (this.selectedUser?.imageUrl && this.userProfileImage) {
      return this.userProfileImage;
    }
    return this.defaultAvatar;
  }
}
