import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { API, graphqlOperation } from 'aws-amplify';

import { GET_EXISTING_USERS } from 'src/app/pages/organizations/graphql/users-graphql.queries';
import { UserChatbotWidgetComponent } from '../user-chatbot-widget/user-chatbot-widget.component';

@Component({
  selector: 'app-user-chatbot',
  standalone: true,
  imports: [CommonModule, UserChatbotWidgetComponent],
  templateUrl: './user-chatbot.component.html',
  styleUrls: ['./user-chatbot.component.scss']
})
export class UserChatbotComponent implements OnInit {
  @Input() cityId: string = '';
  cityUsers: any[] = [];

  async ngOnInit() {
    await this.fetchCityUsers();
  }

  async fetchCityUsers() {
    try {
      const result: any = await API.graphql(graphqlOperation(GET_EXISTING_USERS));
      // Filter users by cityId if provided
      this.cityUsers = (result.data.userByDate.items ?? []).filter((u: any) => !this.cityId || u.cityId === this.cityId);
    } catch (err) {
      this.cityUsers = [];
    }
  }
}
