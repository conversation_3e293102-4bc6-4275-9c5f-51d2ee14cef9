<div class="card card-flush">
  <!--begin::Card header-->
  <div class="card-header">
    <h3 class="card-title align-items-start flex-column">
      <span class="card-label fw-bold text-gray-800">Prompt History</span>
      <span class="text-muted mt-1 fw-semibold fs-7">{{ dataSource.data.length }} prompts found</span>
    </h3>
  </div>
  <!--end::Card header-->
  
  <!--begin::Card body-->
  <div class="card-body pt-0">
    <!--begin::Table wrapper-->
    <div class="table-responsive position-relative">
      <!--begin::Loading overlay-->
      <div *ngIf="loading" class="d-flex justify-content-center align-items-center position-absolute top-0 start-0 w-100 h-100 bg-white bg-opacity-75 z-index-2">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
      <!--end::Loading overlay-->
      
      <!--begin::Table-->
      <table class="table align-middle table-row-dashed fs-6 gy-4" [class.opacity-50]="loading">
        <!--begin::Table head-->
        <thead>
          <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
            <th class="min-w-150px">User Name</th>
            <th class="min-w-150px">Chat Type</th>
            <th class="min-w-100px text-end">Actions</th>
          </tr>
        </thead>
        <!--end::Table head-->
        
        <!--begin::Table body-->
        <tbody class="text-gray-600 fw-semibold">
          <tr *ngFor="let element of paginatedData">
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-50px me-5">
                  <span class="symbol-label bg-light-primary">
                    <span class="svg-icon svg-icon-2x svg-icon-primary">
                      <i class="ki-duotone ki-profile-user fs-2">
                        <span class="path1"></span>
                        <span class="path2"></span>
                      </i>
                    </span>
                  </span>
                </div>
                <div class="d-flex flex-column">
                  <span class="text-gray-800 text-hover-primary mb-1">{{ element.userData?.givenName }} {{ element.userData?.familyName }}</span>
                </div>
              </div>
            </td>
            <td>
              <span class="badge badge-light-{{ getChatTypeBadgeClass(element.chatType) }} text-{{ getChatTypeBadgeClass(element.chatType) }} text-capitalize fw-bold px-4 py-2" style="font-size: 1rem;">
                {{ element.chatType || 'N/A' }}
              </span>
            </td>
            <td class="text-end">
              <button class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1"
                (click)="showPromptData(element.promptData)" 
                title="View Prompt Data"
                [disabled]="loading">
                <i class="ki-duotone ki-eye fs-2 text-primary">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                </i>
              </button>
            </td>
          </tr>
          
          <!-- Empty state -->
          <tr *ngIf="!loading && dataSource.data.length === 0">
            <td colspan="3" class="text-center py-10">
              <div class="d-flex flex-column align-items-center">
                <i class="ki-duotone ki-document-remove fs-5x text-gray-400 mb-4">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                <h4 class="text-gray-800 mb-2">No prompts found</h4>
                <p class="text-muted">There are no prompts to display at this time.</p>
              </div>
            </td>
          </tr>
        </tbody>
        <!--end::Table body-->
      </table>
      <!--end::Table-->
      <!--begin::Pagination-->
      <nav *ngIf="!loading && totalPages > 1" aria-label="Prompt table pagination" class="mt-4">
        <ul class="pagination justify-content-end">
          <li class="page-item" [class.disabled]="currentPage === 1">
            <button class="page-link" (click)="changePage(currentPage - 1)" [disabled]="currentPage === 1">Prev</button>
          </li>
          <li class="page-item" *ngFor="let page of [].constructor(totalPages); let i = index" [class.active]="currentPage === (i+1)">
            <button class="page-link" (click)="changePage(i+1)">{{ i+1 }}</button>
          </li>
          <li class="page-item" [class.disabled]="currentPage === totalPages">
            <button class="page-link" (click)="changePage(currentPage + 1)" [disabled]="currentPage === totalPages">Next</button>
          </li>
        </ul>
      </nav>
      <!--end::Pagination-->
    </div>
    <!--end::Table wrapper-->
  </div>
  <!--end::Card body-->
</div>