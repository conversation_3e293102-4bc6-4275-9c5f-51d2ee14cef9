import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

interface PromptData {
  userData?: {
    givenName?: string;
    familyName?: string;
    email?: string;
  };
  chatType?: string;
  promptData: any;
}

interface DataSource {
  data: PromptData[];
}

@Component({
  selector: 'app-user-prompt-table',
  standalone: true,
  imports: [CommonModule, MatProgressSpinnerModule],
  templateUrl: './user-prompt-table.component.html',
  styleUrls: ['./user-prompt-table.component.scss']
})
export class UserPromptTableComponent {
  @Input() dataSource: DataSource = { data: [] };
  @Input() loading: boolean = false;
  @Output() promptDataView = new EventEmitter<any>();

  // Pagination
  pageSize = 5;
  currentPage = 1;
  get totalPages(): number {
    return Math.ceil(this.dataSource.data.length / this.pageSize) || 1;
  }
  get paginatedData(): PromptData[] {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.dataSource.data.slice(start, start + this.pageSize);
  }
  changePage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  /**
   * Get the appropriate badge class based on chat type
   * Matches the color scheme from the analytics component
   */
  getChatTypeBadgeClass(chatType?: string): string {
    if (!chatType) return 'secondary';
    
    // Convert to ChatType enum value if possible
    const type = chatType.toUpperCase();
    
    // Match the color scheme from analytics component
    switch (type) {
      case 'UNIFY':
      case 'GENERAL':
        return 'primary';
      case 'COMMUNITY':
        return 'success';
      case 'FAMILY':
        return 'info';
      case 'KNOWLEDGE':
        return 'warning';
      case 'DREAMS':
        return 'danger';
      case 'SPIRITUALITY':
        return 'dark';
      default:
        return 'secondary';
    }
  }

  /**
   * Emit the prompt data to parent component
   */
  showPromptData(promptData: any): void {
    this.promptDataView.emit(promptData);
  }
}
