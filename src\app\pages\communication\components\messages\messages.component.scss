:host ::ng-deep .ck.ck-editor__editable {
  border: none !important;
}

.ck-editor {
  border: none !important;
}

.ck.ck.editor_editable {
  border: none !important;
}

@media (max-width: 991.98px) {
  .menu-sub-dropdown.show {
    margin-top: 20px !important;
  }
}

.force-to-the-bottom {
  margin-top: 90vh;
}

.example-chip-list {
  width: 100%;

  .mat-mdc-form-field-infix {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}

// Deep styling for Material components
:host ::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      background-color: transparent;
      padding: 0;
    }

    .mdc-text-field--filled:not(.mdc-text-field--disabled) {
      background-color: transparent;

      &::before,
      &::after {
        display: none !important;
      }
    }

    .mat-mdc-chip-grid-input {
      color: white !important;
      caret-color: white !important;
    }

    .mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label {
      color: rgba(255, 255, 255, 0.7);
    }

    .mdc-line-ripple {
      display: none;
    }

    .mdc-text-field--filled .mdc-line-ripple::before,
    .mdc-text-field--filled .mdc-line-ripple::after {
      border-bottom-color: transparent !important;
    }

    .mat-mdc-form-field-focus-overlay {
      background-color: transparent;
      opacity: 0 !important;
    }

    // Remove all borders and background on hover/focus
    .mdc-text-field:hover.mdc-text-field--filled::before,
    .mdc-text-field--filled:not(.mdc-text-field--disabled):hover::before {
      opacity: 0 !important;
    }

    .mdc-text-field--focused:not(.mdc-text-field--disabled)
      .mdc-line-ripple::after {
      border-bottom-color: transparent !important;
    }
  }

  // Style for chips
  .mdc-evolution-chip-set {
    .mdc-evolution-chip {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

// Deep styling for Material components
:host ::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      background-color: transparent;
      padding: 0;
    }

    .mdc-text-field--filled:not(.mdc-text-field--disabled) {
      background-color: transparent;

      &::before,
      &::after {
        display: none !important;
      }
    }

    .mat-mdc-chip-grid-input {
      color: white !important;
      caret-color: white !important;
    }

    .mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label {
      color: rgba(255, 255, 255, 0.7);
    }

    .mdc-line-ripple {
      display: none;
    }

    .mdc-text-field--filled .mdc-line-ripple::before,
    .mdc-text-field--filled .mdc-line-ripple::after {
      border-bottom-color: transparent !important;
    }

    .mat-mdc-form-field-focus-overlay {
      background-color: transparent;
      opacity: 0 !important;
    }

    // Remove all borders and background on hover/focus
    .mdc-text-field:hover.mdc-text-field--filled::before,
    .mdc-text-field--filled:not(.mdc-text-field--disabled):hover::before {
      opacity: 0 !important;
    }

    .mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-line-ripple::after {
      border-bottom-color: transparent !important;
    }
  }

  // Style for chips
  .mdc-evolution-chip-set {
    .mdc-evolution-chip {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.mat-chip-avatar {
  width: 32px;
  height: 32px;
}

.mat-chip-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.min-w-125px {
  min-width: 125px;
}

.mat-chip-avatar[_ngcontent-xwu-c312] {
  width: 50px;
  height: 50px;
}

.example-form {
  min-width: 150px;
  max-width: 500px;
  width: 100%;
}

.mem-btn {
  display: block;
  position: absolute;
  bottom: 0;
  top: 16px;
  left: 0px;
}

.example-full-width {
  width: 100%;
}

.select-btn {
  width: inherit;
  border: none;
  text-align: inherit;
  margin-left: 2%;
  background: white;
}

.ck.ck-toolbar {
  border-top: none !important;
}

::ng-deep .mat-form-field-underline {
  display: none;
}

::ng-deep .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
  border: none !important;
}

.con-mem {
  position: relative;
  left: 5px;
}

.mem-name {
  position: relative;
  font-weight: 600;
  display: block;
  color: #626161;
  font-size: 14px;
  bottom: 5px;
}

.mem-email {
  display: block;
  position: absolute;
  font-size: 12px;
  color: #626161;
  bottom: 0;
  top: 14px;
}

.add-btn {
  font-size: 14px;
  color: #626161;
  display: block;
  position: relative;
  bottom: 3px;
  width: 100px;
}

::ng-deep .ck-reset_all :not(.ck-reset_all-excluded *),
.ck.ck-reset,
.ck.ck-reset_all {
  word-wrap: break-word;
  background: transparent;
  border: 0;
  border: none !important;
  margin: 0;
  padding: 0;
  text-decoration: none;
  transition: none;
  vertical-align: middle;
}

.menu-posi {
  display: inline-block !important;
  position: relative;
  left: 99px;
  top: 17px;
}

.mat-option:hover span {
  color: #354029;
}

// Menu highlighting styles
.menu-link {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0.75rem 1rem;

  &.active {
    background-color: #f5f8fa;
    border-radius: 6px;

    .menu-title {
      color: #354029;
      font-weight: 600;
    }

    .svg-icon {
      color: #354029;
    }
  }

  &:hover:not(.active) {
    background-color: #f5f8fa;
    border-radius: 6px;
  }
}

// Recipient section spacing
.recipient-section {
  margin-bottom: 1rem;
  border-bottom: 1px solid #e4e6ef;
  padding: 1rem 2rem;

  .mat-form-field {
    margin-bottom: -1.25em; // Adjust the negative margin to reduce space
  }
}

// Bookmark bar spacing
.card-body {
  .border-bottom {
    border-color: #e4e6ef !important;
  }
}

.position-relative {
  position: relative;
}

.table-responsive {
  min-height: 200px;
  overflow-x: auto;
}

::ng-deep {
  ngx-spinner[name="table-spinner"] {
    position: absolute !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95) !important;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    border-radius: 0.475rem;

    &.loading-spinner {
      visibility: visible;
      opacity: 1;
      transition: visibility 0s, opacity 0.3s linear;
    }

    &:not(.loading-spinner) {
      visibility: hidden;
      opacity: 0;
      transition: visibility 0s linear 0.3s, opacity 0.3s linear;
    }

    > div {
      position: relative;
    }

    .loading-text {
      position: absolute;
      top: 60%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #181c32;
      font-weight: 500;
    }
  }
}

// Table styling
.table {
  margin-bottom: 0;

  thead th {
    font-weight: 600;
    padding: 0.75rem;
  }

  tbody {
    tr {
      transition: all 0.2s ease;

      &:hover {
        background-color: #f5f8fa;
      }

      td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
      }
    }
  }
}

// DataTable wrapper styling
.dataTables_wrapper {
  padding: 1rem;
  border-radius: 0.475rem;

  .table-responsive {
    margin: 0;
  }
}
