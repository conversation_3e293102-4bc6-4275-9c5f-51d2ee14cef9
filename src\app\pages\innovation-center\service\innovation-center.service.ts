import { Injectable } from '@angular/core';
import { Observable, map, switchMap, forkJoin } from 'rxjs';
import { Apollo } from 'apollo-angular';
import {
    CREATE_CHAT_BOT,
    CHAT_MESSAGES_BY_DATE,
    ACADEMIC_CHAT_BOT,
    BEDROCK_CHAT_BOT,
    FETCH_CHATBOT_DATA,
    REMOVE_CHATBOT_DATA
} from '../graphql/innovation-center-graphql.queries';
import { ChatMessageData } from 'src/app/pages/organizations/components/organization-chatbot-widget/models/chat.model';

@Injectable({
    providedIn: 'root'
})
export class InnovationCenterService {
    constructor(private readonly apollo: Apollo) { }

    createChatMessages(data: any): Observable<any> {
        return this.apollo.mutate({
            mutation: CREATE_CHAT_BOT,
            variables: {
                input: data,
            },
        });
    }

    chatMessagesByDate(filter: any): Observable<any> {
        return this.apollo.query({
            query: CHAT_MESSAGES_BY_DATE,
            variables: {
                filter: filter
            }
        });
    }

    callAcademicChatBot(userId: string, username: string, question: string): Observable<any> {
        return this.apollo.query({
            query: ACADEMIC_CHAT_BOT,
            variables: {
                userId: userId,
                username: username,
                question: question
            }
        });
    }

    bedrockChatBot(question: string, userId: string, chatType: string): Observable<any> {
        return this.apollo.query({
            query: BEDROCK_CHAT_BOT,
            variables: {
                question,
                userId,
                chatType
            }
        });
    }

    fetchChatbotData(userId: string, chatType: string): Observable<any> {
        return this.apollo.query({
            query: FETCH_CHATBOT_DATA,
            variables: {
                userId,
                chatType
            }
        });
    }

    removeChatbotData(userId: string): Observable<any> {
        return this.apollo.query({
            query: REMOVE_CHATBOT_DATA,
            variables: {
                userId
            }
        });
    }

    sendMessageWithBot(messageData: ChatMessageData, userId: string, chatType: string, question: string): Observable<any> {
        // 1. Call BedrockChatBot API
        return this.bedrockChatBot(question, userId, chatType).pipe(
            switchMap((response: any) => {
                const botResponse = response?.data?.bedrockChatBot?.Assistant || '';
                const userMsg: ChatMessageData = { ...messageData, messageType: 'TEXT' };
                const botMsg: ChatMessageData = {
                    ...messageData,
                    message: botResponse,
                    role: 'bot',
                    messageType: 'TEXT'
                };
                return forkJoin([
                    this.createChatMessages(userMsg),
                    this.createChatMessages(botMsg)
                ]).pipe(map(() => botResponse));
            })
        );
    }
}