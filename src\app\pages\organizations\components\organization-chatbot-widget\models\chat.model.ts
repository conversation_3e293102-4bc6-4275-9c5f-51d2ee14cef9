export enum ChatType {
  UNIFY = 'Unify',
  COMMUNITY = 'Community',
  FAMILY = 'Family',
  KNOWLEDGE = 'Knowledge',
  DREAMS = 'Dreams',
  SPIRITUALITY = 'Spirituality'
}

export interface ChatMessage {
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

export interface ChatMessageData {
  userId: string;
  chatType: string;
  message: string;
  createdAt: string;
  role: string;
  messageType: string;
  fileUrl: string;
  isDeleted: string;
}

export interface ChatResponse {
  data?: {
    bedrockChatBot?: {
      Assistant: string;
    };
    chatGPTByDate?: {
      items: any[];
    };
    fetchChatbotData?: {
      success: boolean;
    };
  };
} 