import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { InnovationCenterService } from 'src/app/pages/innovation-center/service/innovation-center.service';
import { ChatMessage, ChatMessageData, ChatResponse, ChatType } from '../models/chat.model';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  constructor(private readonly innovationCenterService: InnovationCenterService) { }

  fetchChatHistory(userId: string, chatType: ChatType): Observable<ChatMessage[]> {
    const dataFilter = {
      userId: { eq: userId },
      chatType: { eq: chatType }
    };

    return this.innovationCenterService.chatMessagesByDate(dataFilter).pipe(
      map((response: ChatResponse) => {
        const messages = response?.data?.chatGPTByDate?.items || [];
        return messages.toReversed().map(msg => ({
          content: msg.message,
          sender: msg.role === 'user' ? 'user' : 'bot',
          timestamp: new Date(msg.createdAt)
        }));
      })
    );
  }

  sendMessage(messageData: ChatMessageData): Observable<any> {
    return this.innovationCenterService.createChatMessages(messageData);
  }

  sendMessageWithBot(messageData: ChatMessageData, userId: string, chatType: string, question: string): Observable<any> {
    return this.innovationCenterService.sendMessageWithBot(messageData, userId, chatType, question);
  }

  sendBotMessage(message: string, userId: string, chatType: string): Observable<string> {
    return this.innovationCenterService.bedrockChatBot(message, userId, chatType).pipe(
      map((response: ChatResponse) => response?.data?.bedrockChatBot?.Assistant || '')
    );
  }

  initializeChatbot(userId: string, chatType: string): Observable<boolean> {
    return this.innovationCenterService.fetchChatbotData(userId, chatType).pipe(
      map((response: ChatResponse) => response?.data?.fetchChatbotData?.success || false)
    );
  }

  removeChatbotData(userId: string): Observable<any> {
    return this.innovationCenterService.removeChatbotData(userId);
  }

  createMessageData(userId: string, chatType: string, message: string, role: string = 'user'): ChatMessageData {
    return {
      userId,
      chatType: `${chatType}Web`,
      message,
      createdAt: new Date().toISOString(),
      role,
      messageType: 'TEXT',
      fileUrl: '',
      isDeleted: 'false'
    };
  }
}