<div class="d-flex justify-content-end">
  <a routerLink="/funding-dashboard" class="btn btn-light">
    <i class="bi bi-arrow-left"></i> Back to Funding Dashboard
  </a>
</div>
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Purchase MVT Tokens</h3>
  </div>
  <div class="card-body">
    <div class="alert alert-info">
      <div class="d-flex align-items-center">
        <div class="me-3">
          <span class="svg-icon svg-icon-2hx">
            <i class="bi bi-info-circle-fill fs-2"></i>
          </span>
        </div>
        <div class="d-flex flex-column">
          <h4 class="mb-1">
            <ng-container *ngIf="rateLoading">Loading exchange rate...</ng-container>
            <ng-container *ngIf="!rateLoading && exchangeRate !== null">
              1 MVT Token = {{ exchangeRate | number:'1.2-6' }} USDC
            </ng-container>
            <ng-container *ngIf="!rateLoading && exchangeRate === null">Exchange rate unavailable</ng-container>
          </h4>
          <span>You'll be redirected to Stripe to complete your USDC purchase securely. After payment, MVT tokens will
            be transferred to your account automatically.</span>
        </div>
      </div>
    </div>

    <form [formGroup]="purchaseForm" (ngSubmit)="onSubmit()">
      <!-- MVT Amount -->
      <div class="mb-5">
        <label class="form-label" for="mvtAmount">Amount of MVT Tokens</label>
        <input type="number" class="form-control form-control-lg" formControlName="mvtAmount"
          placeholder="Enter amount (minimum {{MIN_AMOUNT}} tokens)"
          [class.is-invalid]="f.mvtAmount.touched && f.mvtAmount.invalid">
        <div *ngIf="f.mvtAmount.touched && f.mvtAmount.invalid" class="invalid-feedback">
          <div *ngIf="f.mvtAmount.errors?.required">Amount is required</div>
          <div *ngIf="f.mvtAmount.errors?.min">Amount must be at least {{MIN_AMOUNT}}</div>
          <div *ngIf="f.mvtAmount.errors?.pattern">Amount must be a whole number</div>
        </div>
      </div>
      <!-- Total USDC Calculation -->
      <div *ngIf="totalUSDC" class="mb-4">
        <!-- Professional USDC total display -->
        <div class="d-flex justify-content-between align-items-center p-3 bg-light border rounded">
          <span class="text-muted fw-normal">Estimated Total:</span>
          <span class="fw-semibold text-dark">${{ totalUSDC }} USDC</span>
        </div>

        <!-- Single consolidated validation message -->
        <div *ngIf="totalUSDC" class="mt-2">
          <!-- Critical error: Below Stripe minimum -->
          <div *ngIf="parseFloat(totalUSDC) < STRIPE_MIN_AMOUNT" class="alert alert-danger p-3 mb-0">
            <div class="d-flex align-items-start">
              <i class="bi bi-exclamation-circle-fill text-danger me-2 mt-1"></i>
              <div>
                <strong>Payment Processing Error</strong>
                <p class="mb-2 mt-1">Amount too small for Stripe payment processing. Minimum purchase is <strong>${{ STRIPE_MIN_AMOUNT }} USDC</strong>.</p>
                <div *ngIf="minimumMVTForStripe" class="alert alert-light p-2 mb-0">
                  <strong>Solution:</strong> Purchase at least <strong>{{ minimumMVTForStripe }} MVT tokens</strong> to meet the minimum requirement.
                </div>
              </div>
            </div>
          </div>

          <!-- Informational: Small but valid amount -->
          <div *ngIf="parseFloat(totalUSDC) >= STRIPE_MIN_AMOUNT && parseFloat(totalUSDC) < 0.10" class="alert alert-info p-3 mb-0">
            <div class="d-flex align-items-start">
              <i class="bi bi-info-circle-fill text-info me-2 mt-1"></i>
              <div>
                <strong>Small Transaction Notice</strong>
                <p class="mb-1 mt-1">This is a small transaction amount ({{ totalUSDC }} USDC).</p>
                <small class="text-muted">
                  <span *ngIf="suggestedMVTAmount">
                    For a $1 USDC transaction, consider purchasing {{ suggestedMVTAmount }} MVT tokens.
                  </span>
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- User Selection -->
      <div class="mb-5">
        <label class="form-label">Select User</label>
        <app-user-search-dropdown 
          [users]="users"
          [selectedUserId]="purchaseForm.get('userId')?.value"
          (userChange)="onUserChange($event)"
          [class.is-invalid]="f.userId.touched && f.userId.invalid">
        </app-user-search-dropdown>
        <div *ngIf="f.userId.touched && f.userId.invalid" class="invalid-feedback d-block">
          <div *ngIf="f.userId.errors?.required">Please select a user</div>
          <div *ngIf="f.userId.errors?.minlength">User ID must be at least 3 characters</div>
        </div>
        <div class="form-text text-muted">
          The MVT tokens will be transferred to the selected user's account after payment
        </div>
      </div>

      <!-- Error message -->
      <div *ngIf="error" class="alert alert-danger mb-5">
        {{ error }}
      </div>

      <!-- Debug Information (remove in production) -->
      <div *ngIf="false" class="alert alert-light border mb-3">
        <small>
          <strong>Debug Info:</strong><br>
          Form Valid: {{ purchaseForm.valid }}<br>
          Exchange Rate: {{ exchangeRate }}<br>
          MVT Amount: {{ purchaseForm.get('mvtAmount')?.value }}<br>
          Calculated USDC: {{ totalUSDC }}<br>
          Stripe Compatible: {{ totalUSDC ? isStripeCompatible(parseFloat(totalUSDC)) : 'N/A' }}<br>
          Stripe Amount: {{ totalUSDC ? getStripeCompatibleAmount(parseFloat(totalUSDC)) : 'N/A' }}<br>
          Stripe Min Amount: ${{ STRIPE_MIN_AMOUNT }}<br>
          Min MVT for Stripe: {{ minimumMVTForStripe }}<br>
          Loading: {{ loading }}<br>
          Submit Disabled: {{ isSubmitDisabled }}
        </small>
      </div>

      <!-- Submit button -->
      <div class="d-flex justify-content-end">
        <button type="submit" class="btn btn-primary btn-lg" [disabled]="isSubmitDisabled">
          <span *ngIf="loading">
            <span class="spinner-border spinner-border-sm me-2" aria-hidden="true"></span>
            <output>Processing payment...</output>
          </span>
          <span *ngIf="!loading">Buy MVT Tokens</span>
        </button>
      </div>
    </form>
  </div>
</div>