import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { CryptoOnrampService } from '../../../services/crypto-onramp.service';

@Component({
  selector: 'app-crypto-onramp',
  templateUrl: './crypto-onramp.component.html',
  styleUrls: ['./crypto-onramp.component.scss']
})
export class CryptoOnrampComponent implements OnInit {
  @ViewChild('onrampContainer') onrampContainer!: ElementRef;
  
  onrampForm: FormGroup;
  loading = false;
  error = '';
  success = false;
  clientSecret: string;
  
  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly cryptoOnrampService: CryptoOnrampService,
    private readonly router: Router,
    private readonly route: ActivatedRoute
  ) {
    this.onrampForm = this.formBuilder.group({
      usdcAmount: ['', [Validators.required, Validators.min(0.01)]],
      userWallet: ['', [Validators.required, Validators.pattern('^0x[a-fA-F0-9]{40}$')]]
    });
  }
  
  ngOnInit(): void {

    this.route.queryParams.subscribe(params => {
      this.clientSecret = params['client_secret'];
      if (this.clientSecret) {
        this.loadScripts();
      }
    });
  }
  
  loadScripts(): void {
    Promise.all([
      this.loadScript('https://js.stripe.com/v3/'),
      this.loadScript('https://crypto-js.stripe.com/crypto-onramp-outer.js')
    ]).then(() => {
      setTimeout(() => {
        this.openOnramp(this.clientSecret);
      }, 1000);
    }).catch(error => {
      console.error('Error loading Stripe scripts:', error);
      this.error = 'Failed to load payment processing scripts. Please refresh the page and try again.';
    });
  }

  private loadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (document.querySelector(`script[src*="${src}"]`)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load script: ' + src));
      document.head.appendChild(script);
    });
  }
  
  openOnramp(clientSecret: string): void {
    if (!clientSecret) {
      console.error('Client secret is required');
      return;
    }

    this.loading = true;
    this.cryptoOnrampService.openOnramp(clientSecret)
      .then((result) => {
        this.success = true;
        this.loading = false;
        
        setTimeout(() => {
          this.router.navigate(['/crypto-onramp/success'], {
            queryParams: {
              mvtAmount: this.route.snapshot.queryParamMap.get('mvtAmount'),
              userId: this.route.snapshot.queryParamMap.get('userId'),
              from: this.route.snapshot.queryParamMap.get('from'),
              onramp_session_id: result.sessionId
            }
          });
        }, 2000);
      })
      .catch((error) => {
        console.error('Onramp failed:', error);
        this.error = error.message ?? 'Failed to complete the crypto purchase';
        this.loading = false;
        setTimeout(() => {
          this.router.navigate(['/crypto-onramp/failed'], {
            queryParams: {
              mvtAmount: this.route.snapshot.queryParamMap.get('mvtAmount'),
              userId: this.route.snapshot.queryParamMap.get('userId'),
              from: this.route.snapshot.queryParamMap.get('from')
            }
          });
        }, 2000);
      });
  }
}