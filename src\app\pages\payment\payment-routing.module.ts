import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { PaymentSuccessComponent } from './payment-success/payment-success.component';
import { PaymentCancelComponent } from './payment-cancel/payment-cancel.component';
import { PaymentFailedComponent } from './payment-failed/payment-failed.component';
import { BuyMVTComponent } from './buy-mvt/buy-mvt.component';

const routes: Routes = [
  { path: 'buy', component: BuyMVTComponent },
  { path: 'success', component: PaymentSuccessComponent },
  { path: 'cancel', component: PaymentCancelComponent },
  { path: 'failed', component: PaymentFailedComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PaymentRoutingModule {}
