<div class="payment-success-container">
  <div class="card">
    <div class="card-body text-center">

      <!-- Success State -->
      <div *ngIf="success" class="success-section">
        <div class="success-icon mb-4">
          <i class="far fa-check-circle fa-5x text-success"></i>
        </div>
        <h2 class="mb-3">Payment Successful!</h2>
        <p class="lead mb-4">Your payment was successful and is being handled by our backend systems.</p>

        <button class="btn btn-primary btn-lg" (click)="goToWallet()">
          View Wallet
        </button>
        <p class="text-muted mt-3"><small>Redirecting automatically in {{ autoRedirectTimer }} seconds...</small></p>
      </div>

    </div>
  </div>
</div>