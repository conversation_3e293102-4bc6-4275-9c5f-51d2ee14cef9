import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { OnrampSessionService } from '../../../shared/services/onramp-session.service';

@Component({
  selector: 'app-payment-success',
  templateUrl: './payment-success.component.html',
  styleUrls: ['./payment-success.component.scss']
})
export class PaymentSuccessComponent implements OnInit, OnDestroy {
  success = false;
  autoRedirectTimer = 5;

  private subscriptions: Subscription[] = [];
  private redirectInterval?: number;

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly onrampSessionService: OnrampSessionService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      // Payment success is now handled by backend webhook
      // Just show success message and auto-redirect to wallet
      this.success = true;
      this.startAutoRedirect();
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.redirectInterval) {
      clearInterval(this.redirectInterval);
    }
  }

  private startAutoRedirect(): void {
    this.redirectInterval = window.setInterval(() => {
      this.autoRedirectTimer--;
      if (this.autoRedirectTimer <= 0) {
        this.goToWallet();
      }
    }, 1000);
  }

  goToWallet(): void {
    if (this.redirectInterval) {
      clearInterval(this.redirectInterval);
    }
    this.onrampSessionService.clearSessionData();
    this.router.navigate(['/funding-dashboard']);
  }
}
