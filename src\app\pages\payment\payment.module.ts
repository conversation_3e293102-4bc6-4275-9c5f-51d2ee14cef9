import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientModule } from '@angular/common/http';

import { PaymentSuccessComponent } from './payment-success/payment-success.component';
import { PaymentCancelComponent } from './payment-cancel/payment-cancel.component';
import { SharedModule } from '../../shared/shared.module';
import { CryptoOnrampComponent } from './crypto-onramp/crypto-onramp.component';
import { PaymentFailedComponent } from './payment-failed/payment-failed.component';
import { UserSearchDropdownModule } from '../../shared/formcontrol/user-search-dropdown/user-search-dropdown.module';
import { PaymentRoutingModule } from './payment-routing.module';
import { BuyMVTComponent } from './buy-mvt/buy-mvt.component';
import { UserSearchDropdownComponent } from 'src/app/shared/formcontrol/user-search-dropdown/user-search-dropdown.component';

@NgModule({
  declarations: [
    BuyMVTComponent,
    PaymentSuccessComponent,
    PaymentCancelComponent,
    CryptoOnrampComponent,
    PaymentFailedComponent
  ],
  imports: [
    CommonModule,
    PaymentRoutingModule,
    UserSearchDropdownComponent,
    RouterModule,
    InlineSVGModule,
    FormsModule,
    ReactiveFormsModule,
    NgbTooltipModule,
    HttpClientModule,
    SharedModule
  ],
})
export class PaymentModule { }