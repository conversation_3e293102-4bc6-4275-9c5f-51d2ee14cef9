<!-- begin::Header -->
<div class="card-header border-0">
  <p class="h3 card-title align-items-start flex-column">
    <span class="card-label fw-bold text-dark">
      Captured Knowledge Minutes
    </span>
    <span class="text-muted mt-1 fw-semibold fs-7">
      {{totalCapturedMinutes}} total minutes captured
    </span>
  </p>
  <div class="card-toolbar" *ngIf="!isStudent">
    <!-- begin::Menu -->
    <button class="btn btn-sm btn-flex btn-light btn-active-primary fw-bolder cursor-pointer"
      (click)="categoryClickHandler('Membership MOUs & Contracts',modal)">
      Upload
    </button>
    <!-- end::Menu -->
  </div>
</div>
<!-- end::Header -->
<!-- begin::Body -->
<div class="card-body pt-0">
  <!-- begin::Item -->
  <div class="d-flex align-items-center rounded mb-7" *ngFor="let category of categories">
    <!-- begin::Icon -->
    <button type="button" [ngStyle]="{ backgroundColor: category.color === 'warning' ? '#fcf8c7' :
    category.color === 'success' ? '#e8fff3' :
    category.color === 'info' ? 'antiquewhite' :
    category.color === 'primary' ? '#d7f6fa' :
    category.color === 'danger' ? '#f3e3f6' : 'transparent' }" class="btn btn-sm btn-icon mar-right-funding"
      data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-flip="top-end">
      <span [ngClass]="{'svg-icon-warning': category.color === 'warning',
      'svg-icon-success': category.color === 'success',
      'svg-icon-danger': category.color === 'danger',
      'svg-icon-info': category.color === 'info',
      'svg-icon-primary': category.color === 'primary'}" class="svg-icon svg-icon-1">
        <app-svg-general [svg]="category.icon"></app-svg-general>
      </span>
    </button>
    <!-- end::Icon -->
    <!-- begin::Title -->
    <div class="flex-grow-1 me-2">
      <a class="fw-bolder text-gray-800 text-hover-primary fs-6">
        {{category.name}}
      </a>
      <span class="text-muted fw-bold d-block">
        <ng-container *ngIf="category.totalMinutesWithoutLabel > 0">
          <span *ngIf="category.lastUploadDate">
            Last upload: {{category.lastUploadDate | date:'MM/dd/yyyy'}}
          </span>
        </ng-container>
        <ng-container *ngIf="category.totalMinutesWithoutLabel === 0">
          No repository knowledge uploaded
        </ng-container>
      </span>
    </div>
    <!-- end::Title -->
    <!-- begin::Lable -->
    <span class="fw-bolder txt-color py-1">{{category.totalMinutes}}</span>
    <!-- end::Lable -->
  </div>
  <!-- end::Item -->
</div>
<!-- end::Body -->

<!-- Upload Documents Modal -->
<ng-template #modal>
  <div class="upload-modal" [class.uploading]="isUploading">
    <!-- Modal Header -->
    <div class="card-header border-0">
      <p class="h3 card-title flex-column">
        <span class="card-label fw-bold text-dark">
          Upload Stakeholder Knowledge
        </span>
        <span class="text-muted mt-1 fw-semibold fs-7">
          Upload audio or video files to capture stakeholder knowledge
        </span>
      </p>
    </div>

    <!-- Upload Progress - Show ONLY during upload -->
    <div *ngIf="isUploading" class="upload-progress-container">
      <app-upload-progress
        [isUploading]="isUploading"
        [currentUploadingFile]="currentUploadingFile"
        [uploadProgress]="uploadProgress"
        [files]="files"
        [currentFileIndex]="currentFileIndex"
        [inline]="true">
      </app-upload-progress>
    </div>

    <!-- Upload Form - Show ONLY when not uploading -->
    <div *ngIf="!isUploading" class="upload-form-container">
      <!-- File Drop Zone -->
      <div class="head_para mb-4" (drop)="fileDrop($event,modal,true)" (dragover)="fileDragOver($event)"
        (click)="selectDocumentsInner.click()" (keydown.enter)="selectDocumentsInner.click()">
        <img class="modal-set-size" src="../../../../../assets/media/svg/files/upload.svg" alt="">
        <input type="file" name="documents" (change)="onChange($event,modal,true)" #selectDocumentsInner [hidden]="true"
          accept="audio/*,video/*" multiple />
        <div class="flex-grow-1 me-2">
          <h5>Drag & Drop or choose files from computer</h5>
          <p class="para-text">Upload audio (MP3, WAV) or video (MP4, WebM) files up to 200MB each.</p>
        </div>
      </div>

      <!-- File List -->
      <div class="modal-sub-header mb-4" *ngIf="files.length > 0">
        <p class="h6 card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">
            Selected Files
          </span>
        </p>
        <div class="modal-file-list">
          <div *ngFor="let file of files; let i = index" class="d-flex align-items-center rounded mb-3">
            <span class="svg-icon svg-icon-2x me-3" *ngIf="file.type.startsWith('audio/')">
              <app-svg-general svg="gen064"></app-svg-general>
            </span>
            <span class="svg-icon svg-icon-2x me-3" *ngIf="!file.type.startsWith('audio/')">
              <app-svg-general svg="gen065"></app-svg-general>
            </span>
            <div class="flex-grow-1">
              <div class="fw-bold text-gray-800">{{file.name}}</div>
              <div class="text-muted">{{file.type}} - {{formatFileSize(file.size)}}</div>
            </div>
            <button type="button" class="btn btn-icon btn-sm btn-light-danger" (click)="files.splice(i, 1)">
              <span class="svg-icon svg-icon-2">
                <app-svg-general svg="gen027"></app-svg-general>
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- Name Input -->
      <div class="mb-4">
        <label class="form-label required" for="">Name</label>
        <input type="text" class="form-control" [(ngModel)]="knowledgeName" required
          placeholder="Enter a name for this knowledge entry">
      </div>

      <!-- Description Input -->
      <div class="mb-4">
        <label class="form-label" for="">Description</label>
        <textarea class="form-control" [(ngModel)]="knowledgeDescription" rows="3"
          placeholder="Enter a description (optional)"></textarea>
      </div>

      <!-- Category Selection -->
      <div class="mb-8">
        <label class="form-label required" for="">Category</label>
        <select class="form-select form-select-solid" (change)="onCategorySelect($event)"
          [class.is-invalid]="!selectedCategory">
          <option value="">Select Category</option>
          <option *ngFor="let category of categories" [value]="category.id">
            {{category.name}}
          </option>
        </select>
        <div class="text-muted fs-7 mt-2" *ngIf="selectedCategory?.description">
          {{ selectedCategory?.description }}
        </div>
      </div>

      <!-- Subcategories Selection -->
      <div class="mb-8" *ngIf="selectedCategory">
        <label class="form-label required" for="">Subcategories</label>
        <div class="d-flex flex-wrap gap-2">
          <ng-container *ngFor="let subcategory of getSubcategoriesForCategory(selectedCategory.id)">
            <div class="form-check form-check-custom form-check-solid mb-2">
              <input class="form-check-input" type="checkbox" [id]="'subcategory_' + subcategory.id"
                [checked]="isSubcategorySelected(subcategory.id)"
                (change)="onSubcategoryCheckboxChange($event, subcategory)" />
              <label class="form-check-label" [for]="'subcategory_' + subcategory.id">
                {{subcategory.name}}
              </label>
            </div>
          </ng-container>
        </div>
        <div class="text-danger fs-7 mt-2" *ngIf="showSubcategoryError">
          Please select at least one subcategory
        </div>
      </div>

      <!-- Public/Private Toggle -->
      <div class="mb-4">
        <label class="form-label" for="isPublicToggle">Visibility</label>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="isPublicToggle" [(ngModel)]="isPrivate">
          <label class="form-check-label" for="isPublicToggle">
            {{ isPrivate ? 'Private' : 'Public' }}
          </label>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="modal-footer">
        <button type="button" class="btn btn-light" (click)="closeModal()">Cancel</button>
        <button type="button" class="btn btn-primary"
          [disabled]="!files.length || !selectedCategory || !knowledgeName || !selectedSubcategories.length || isUploading"
          (click)="uploadFiles()">
          <span *ngIf="!isUploading">Upload</span>
          <span *ngIf="isUploading">Uploading...</span>
        </button>
      </div>
    </div>
  </div>
</ng-template>