import { Component, Input, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { gql } from 'apollo-angular';
import { lastValueFrom } from 'rxjs';

import { CategoriesService } from 'src/app/pages/categories/services/categories.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { StakeholderKnowledgeService } from '../person-knowledge-repository/services/stakeholder-knowledge.service';
import { TranscribeService } from 'src/app/pages/posts/services/transcribe.service';

type FileType = 'AUDIO' | 'VIDEO';
type KnowledgeEntityType = 'STAKEHOLDER' | 'ORGANIZATION';

interface UploadData {
  organizationId: string;
  categoryId: string;
  subCategoryIds: string[];
  file: File;
  fileType: FileType;
  name: string;
  description: string;
  durationInMinutes: number;
  submittedBy: string;
  entityType: KnowledgeEntityType;
  cityId?: string;
  status?: string;
  isPublic: boolean;
}

interface KnowledgeSummary {
  categoryId: string;
  totalMinutes: number;
  lastUploadDate: string;
  organizationId: string;
  totalMinutesWithoutLabel: number;
}


@Component({
  selector: 'app-member-knowledge-repository',
  templateUrl: './member-knowledge-repository.component.html',
  styleUrls: ['./member-knowledge-repository.component.scss']
})
export class MemberKnowledgeRepositoryComponent implements OnInit, OnDestroy {

  @Input() organizationId: string;
  @Input() isStudent: boolean;

  categories: any[] = [];
  subCategories: any[] = [];
  files: File[] = [];
  selectedCategory: any = null;
  selectedSubcategories: any[] = [];
  uploadProgress: number = 0;
  isUploading: boolean = false;
  knowledgeName: string = '';
  knowledgeDescription: string = '';
  private mediaElements: HTMLMediaElement[] = [];
  categoryTypeList: any = [];
  bgClasses: any = ['warning', 'success', 'danger', 'info', 'primary'];
  bgIcons: any = ['gen063', 'gen065', 'gen064', 'gen024', 'gen019'];
  knowledgeSummaries: KnowledgeSummary[] = [];
  totalCapturedMinutes: number = 0;
  showSubcategoryError: boolean = false;
  currentUploadingFile: File | null = null;
  currentFileIndex: number = 0;
  isPrivate: boolean = true; // Default to true (Private) to match the API's expectation of isPublic: false

  constructor(
    public readonly sharedService: SharedService,
    public categoriesService: CategoriesService,
    private readonly toastr: ToastrService,
    private readonly modalService: NgbModal,
    private readonly stakeholderKnowledgeService: StakeholderKnowledgeService,
    private readonly transcribeService: TranscribeService,
    private readonly changeDetectorRef: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.getCategories()
  }

  private async getOrganizationKnowledgeSummaries() {
    try {
      const query = /* GraphQL */ `
          query ListKnowledgeRepositoryStoreSummaries($filter: ModelKnowledgeRepositoryStoreSummaryFilterInput) {
            listKnowledgeRepositoryStoreSummaries(filter: $filter) {
              items {
                id
                organizationId
                categoryId
                totalMinutes
                lastUploadDate
                isDeleted
                cityId
              }
            }
          }
        `;

      const result: any = await lastValueFrom(this.stakeholderKnowledgeService.apollo.query({
        query: gql(query),
        variables: {
          filter: {
            organizationId: { eq: this.organizationId },
            isDeleted: { eq: "false" }
          }
        }
      }));
      this.knowledgeSummaries = result.data.listKnowledgeRepositoryStoreSummaries.items.map((iterator: any) => {
        let minutes = 0;
        const timeString = iterator.totalMinutes.toString().toLowerCase();

        if (timeString.includes('h')) {
          const hours = parseInt(timeString.replace('h', ''));
          minutes += hours * 60;
        } else if (timeString.includes('m')) {
          minutes += parseInt(timeString.replace('m', ''));
        } else {
          minutes += parseInt(timeString) || 0;
        }

        return {
          ...iterator,
          totalMinutesWithoutLabel: minutes,
          totalMinutes: this.formatTime(minutes)
        };
      });

      this.totalCapturedMinutes = this.knowledgeSummaries.reduce((total, summary) =>
        total + summary.totalMinutesWithoutLabel, 0);

      this.updateCategoryDisplays();
      return result;
    } catch (error) {
      console.error('Error fetching knowledge summaries:', error);
      this.toastr.error('Failed to fetch knowledge summaries');
      return null;
    }
  }

  private updateCategoryDisplays() {
    this.categories.forEach((category: any) => {
      const summary = this.knowledgeSummaries.find(s => s.categoryId === category.id);
      if (summary) {
        category.totalMinutes = summary.totalMinutes;
        category.totalMinutesWithoutLabel = summary.totalMinutesWithoutLabel;
        category.lastUploadDate = summary.lastUploadDate;
      } else {
        category.totalMinutes = 0;
        category.totalMinutesWithoutLabel = 0;
        category.lastUploadDate = null;
      }
    });
  }

  public categoryClickHandler(category: string, content: any) {
    this.modalService.open(content, {
      size: 'md',
      backdrop: 'static',
      keyboard: false
    });
  }

  public async closeModal() {
    this.mediaElements.forEach(element => {
      if (element.src) {
        URL.revokeObjectURL(element.src);
      }
    });
    this.mediaElements = [];

    this.modalService.dismissAll();
    this.resetUploadState();
  }


  public getCategories() {
    this.categoriesService.listCategories().subscribe({
      next: (response: any) => {
        const allItems = response?.data?.listCategories?.items ?? [];
        this.categories = allItems
          .filter((element: any) => !element._deleted && !element.parentId)
          .map((cat: { name: any; }, index: number) => ({ ...cat, name: cat.name ?? '', color: this.bgClasses[index % this.bgClasses.length], icon: this.bgIcons[index % this.bgIcons.length] }));

        this.subCategories = allItems
          .filter((element: any) => !element._deleted && element.parentId)
          .map(sub => ({ ...sub, name: sub.name ?? '' }));
        this.getOrganizationKnowledgeSummaries()
      },
      error: (error: any) => {
        this.toastr.error(error.message);
      },
    });
  }

  public fileDrop(event: DragEvent, modal: any, isDocument: boolean) {
    event.preventDefault();
    event.stopPropagation();
    const files = event.dataTransfer?.files;
    if (files) {
      this.handleFiles(files, modal, isDocument);
    }
  }

  public fileDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
  }

  public onChange(event: any, modal: any, isDocument: boolean) {
    const files = event.target.files;
    if (files) {
      this.handleFiles(files, modal, isDocument);
    }
  }

  private handleFiles(files: FileList, modal: any, isDocument: boolean) {
    const allowedTypes = ['audio/mpeg', 'audio/wav', 'video/mp4', 'video/webm'];
    const maxSize = 200 * 1024 * 1024;

    this.files.forEach(file => {
      const element = this.mediaElements.find(el => el.src.includes(file.name));
      if (element?.src) {
        URL.revokeObjectURL(element.src);
      }
    });

    Array.from(files).forEach(file => {
      if (!allowedTypes.includes(file.type)) {
        this.toastr.error(`File type ${file.type} is not supported. Please upload audio (MP3, WAV) or video (MP4, WebM) files.`);
        return;
      }

      if (file.size > maxSize) {
        this.toastr.error(`File ${file.name} is too large. Maximum size is 100MB.`);
        return;
      }

      this.files.push(file);
    });
  }

  public onCategorySelect(event: any) {
    const categoryId = event?.target?.value;
    if (!categoryId) {
      this.selectedCategory = null;
      this.selectedSubcategories = [];
      this.showSubcategoryError = false;
      return;
    }

    this.selectedCategory = this.categories.find(c => c.id === categoryId);
    this.selectedSubcategories = [];
    this.showSubcategoryError = false;
  }

  isSubcategorySelected(subcategoryId: string): boolean {
    return this.selectedSubcategories.some(sub => sub.id === subcategoryId);
  }

  public onSubcategoryCheckboxChange(event: any, subcategory: any) {
    if (event.target.checked) {
      if (!this.selectedSubcategories.some(sub => sub.id === subcategory.id)) {
        this.selectedSubcategories.push(subcategory);
      }
    } else {
      this.selectedSubcategories = this.selectedSubcategories.filter(sub => sub.id !== subcategory.id);
    }
    this.showSubcategoryError = false;
  }

  public getSubcategoriesForCategory(categoryId: string): any[] {
    return this.subCategories.filter(sub => sub.parentId === categoryId);
  }

  getFileIcon(fileType: string): string {
    const baseIconPath = '../../../../../assets/media/icons/';
    if (fileType.startsWith('audio/')) {
      return `${baseIconPath}audio.png`;
    }
    return `${baseIconPath}video.png`;
  }

  async uploadFiles() {
    console.log('Starting uploadFiles - isPrivate value:', this.isPrivate);
    
    if (!this.selectedCategory) {
      this.toastr.error('Please select a category');
      return;
    }

    if (this.selectedSubcategories.length === 0) {
      this.showSubcategoryError = true;
      this.toastr.error('Please select at least one subcategory');
      return;
    }

    if (!this.knowledgeName.trim()) {
      this.toastr.error('Please enter a name for the knowledge');
      return;
    }

    if (this.files.length === 0) {
      this.toastr.error('Please select files to upload');
      return;
    }

    this.isUploading = true;
    this.currentFileIndex = 0;
    this.uploadProgress = 0;
    const totalFiles = this.files.length;

    console.log('Starting upload process:', {
      isUploading: this.isUploading,
      totalFiles,
      uploadProgress: this.uploadProgress
    });

    try {
      for (let i = 0; i < totalFiles; i++) {
        const file = this.files[i];
        this.currentUploadingFile = file;
        this.currentFileIndex = i;
        const fileType: FileType = file.type.startsWith('audio/') ? 'AUDIO' : 'VIDEO';
        const duration = await this.getMediaDuration(file);

        // Update progress for current file start
        this.uploadProgress = Math.round((i / totalFiles) * 100);
        console.log(`File ${i + 1}/${totalFiles} - Progress: ${this.uploadProgress}%`, {
          currentFile: file.name,
          isUploading: this.isUploading,
          currentUploadingFile: this.currentUploadingFile?.name,
          isPrivate: this.isPrivate,
          isPrivateType: typeof this.isPrivate
        });
        this.changeDetectorRef.detectChanges();

        const uploadData: UploadData = {
          organizationId: this.organizationId,
          categoryId: this.selectedCategory.id,
          subCategoryIds: this.selectedSubcategories.map(sub => sub.id),
          file,
          fileType,
          name: this.knowledgeName,
          description: this.knowledgeDescription,
          durationInMinutes: duration,
          submittedBy: this.sharedService.currentUser.value['custom:dynamodbId'],
          entityType: 'ORGANIZATION',
          cityId: this.sharedService.defaultCityId.value,
          status: 'ACTIVE',
          isPublic: !this.isPrivate // Invert the value for the API
        };

        console.log('Sending mutation with variables:', JSON.stringify({
          input: {
            name: uploadData.name,
            description: uploadData.description,
            fileUrl: '',
            fileType: uploadData.fileType,
            durationInMinutes: uploadData.durationInMinutes,
            organizationId: uploadData.organizationId,
            entityType: uploadData.entityType,
            submittedBy: uploadData.submittedBy,
            categoryId: uploadData.categoryId,
            subCategoryIds: uploadData.subCategoryIds,
            cityId: uploadData.cityId,
            status: uploadData.status,
            isPublic: uploadData.isPublic,
            isDeleted: 'false'
          }
        }, null, 2));

        const mutation = gql`
            mutation CreateKnowledgeRepositoryStore(
              $input: CreateKnowledgeRepositoryStoreInput!
              $condition: ModelKnowledgeRepositoryStoreConditionInput
            ) {
              createKnowledgeRepositoryStore(input: $input, condition: $condition) {
                id
                name
                description
                fileUrl
                fileType
                durationInMinutes
                organizationId
                entityType
                submittedBy
                categoryId
                subCategoryIds
                cityId
                status
                isDeleted
                createdAt
                updatedAt
              }
            }
          `;

        const variables = {
          input: {
            name: uploadData.name,
            description: uploadData.description,
            fileUrl: '',
            fileType: uploadData.fileType,
            durationInMinutes: uploadData.durationInMinutes,
            organizationId: uploadData.organizationId,
            entityType: uploadData.entityType,
            submittedBy: uploadData.submittedBy,
            categoryId: uploadData.categoryId,
            subCategoryIds: uploadData.subCategoryIds,
            cityId: uploadData.cityId,
            status: uploadData.status,
            isPublic: uploadData.isPublic,
            isDeleted: 'false'
          }
        };

        const result: any = await lastValueFrom(this.stakeholderKnowledgeService.apollo.mutate({
          mutation,
          variables
        }));

        console.log('Mutation result:', JSON.stringify(result, null, 2));

        if (result?.data?.createKnowledgeRepositoryStore?.id) {
          // Update progress to show file upload starting
          this.uploadProgress = Math.round(((i + 0.5) / totalFiles) * 100);
          this.changeDetectorRef.detectChanges();

          const fileUploadResult = await this.stakeholderKnowledgeService.uploadFile(
            file,
            result.data.createKnowledgeRepositoryStore.id,
            this.organizationId,
            'organization-knowledge'
          );

          if (fileUploadResult?.fileUrl) {


            const updateMutation = gql`
                mutation UpdateKnowledgeRepositoryStore(
                  $input: UpdateKnowledgeRepositoryStoreInput!
                ) {
                  updateKnowledgeRepositoryStore(input: $input) {
                    id
                    fileUrl
                  }
                }
              `;

            await lastValueFrom(this.stakeholderKnowledgeService.apollo.mutate({
              mutation: updateMutation,
              variables: {
                input: {
                  id: result.data.createKnowledgeRepositoryStore.id,
                  fileUrl: fileUploadResult.fileUrl,
                }
              }
            }));

            await this.stakeholderKnowledgeService.updateKnowledgeSummaryForOrganization(this.organizationId, uploadData.categoryId, uploadData.durationInMinutes)
            this.transcribeService.fetchVideoOrAudioTranscript(fileUploadResult?.fileUrl, result?.data?.createKnowledgeRepositoryStore?.id, "knowledge")
          }
        }

        // Update progress to show file completion
        this.uploadProgress = Math.round(((i + 1) / totalFiles) * 100);
        this.changeDetectorRef.detectChanges();
      }

      this.toastr.success('All files uploaded successfully');
      this.closeModal();
      this.resetUploadState();
      await this.getOrganizationKnowledgeSummaries();
    } catch (error) {
      console.error('Error uploading files:', error);
      this.toastr.error('Failed to upload files');
    } finally {
      this.isUploading = false;
    }
  }

  private resetUploadState() {
    this.mediaElements.forEach(element => {
      if (element.src) {
        URL.revokeObjectURL(element.src);
      }
    });
    this.mediaElements = [];

    this.files = [];
    this.selectedCategory = null;
    this.selectedSubcategories = [];
    this.knowledgeName = '';
    this.knowledgeDescription = '';
    this.uploadProgress = 0;
    this.showSubcategoryError = false;
    this.currentUploadingFile = null;
    this.currentFileIndex = 0;
    this.isPrivate = true;
  }

  private async getMediaDuration(file: File): Promise<number> {
    return new Promise((resolve, reject) => {
      const element = document.createElement(file.type.startsWith('audio/') ? 'audio' : 'video');
      element.preload = 'metadata';

      element.onloadedmetadata = () => {
        window.URL.revokeObjectURL(element.src);
        const durationInMinutes = Math.ceil(element.duration / 60);
        resolve(durationInMinutes);
      };

      element.onerror = () => {
        window.URL.revokeObjectURL(element.src);
        reject(new Error('Error loading media file'));
      };

      element.src = window.URL.createObjectURL(file);
      this.mediaElements.push(element);
    });
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatTime(value: number): string {
    if (value < 60) {
      return `${value}m`;
    } else {
      const hours = Math.round(value / 60);
      return `${hours}h`;
    }
  }


  ngOnDestroy() {
    this.mediaElements.forEach(element => {
      if (element.src) {
        URL.revokeObjectURL(element.src);
      }
    });
    this.mediaElements = [];

    this.resetUploadState();
  }


}