import { Component, Input, OnInit, OnD<PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { gql } from 'apollo-angular';
import { lastValueFrom } from 'rxjs';

import { CategoriesService } from 'src/app/pages/categories/services/categories.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { StakeholderKnowledgeService } from './services/stakeholder-knowledge.service';

type FileType = 'AUDIO' | 'VIDEO';
type KnowledgeEntityType = 'STAKEHOLDER' | 'ORGANIZATION' | 'STUDENT';

interface UploadData {
  userId: string;
  categoryId: string;
  subCategoryIds: string[];  // Array of selected subcategory IDs
  file: File;
  fileType: FileType;
  knowledgeEntityType: KnowledgeEntityType;
  name: string;
  description: string;
  durationInMinutes: number;  // Updated to match schema
  submittedBy: string;
  isPublic: boolean;
}

interface KnowledgeSummary {
  categoryId: string;
  totalMinutes: number;
  lastUploadDate: string;
  totalMinutesWithoutLabel: number;
}

@Component({
  selector: 'app-person-knowledge-repository',
  templateUrl: './person-knowledge-repository.component.html',
  styleUrls: ['./person-knowledge-repository.component.scss']
})
export class PersonKnowledgeRepositoryComponent implements OnInit, OnDestroy {
  @Input() personId: string;
  @Input() isStudent: boolean;

  categories: any[] = [];
  subCategories: any[] = [];
  files: File[] = [];
  selectedCategory: any = null;
  selectedSubcategories: any[] = [];
  uploadProgress: number = 0;
  isUploading: boolean = false;
  knowledgeName: string = '';
  knowledgeDescription: string = '';
  private mediaElements: HTMLMediaElement[] = [];
  categoryTypeList: any = [];
  bgClasses: any = ['warning', 'success', 'danger', 'info', 'primary'];
  bgIcons: any = ['gen063', 'gen065', 'gen064', 'gen024', 'gen019'];
  knowledgeSummaries: KnowledgeSummary[] = [];
  totalCapturedMinutes: number = 0;
  showSubcategoryError: boolean = false;
  currentUploadingFile: File | null = null;
  currentFileIndex = 0;
  isTranscribing = false;
  transcriptionProgress = 0;
  transcriptionStatus = '';
  currentFileType: 'AUDIO' | 'VIDEO' | null = null;
  isPrivate: boolean = true; // Default to true (Private) to match the API's expectation of isPublic: false

  constructor(
    public readonly sharedService: SharedService,
    public categoriesService: CategoriesService,
    private readonly toastr: ToastrService,
    private readonly modalService: NgbModal,
    private readonly stakeholderKnowledgeService: StakeholderKnowledgeService,
    private readonly changeDetectorRef: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.getCategories()
  }

  private async getStakeholderKnowledgeSummaries() {
    try {
      const query = /* GraphQL */ `
        query ListKnowledgeRepositoryStoreSummaries($filter: ModelKnowledgeRepositoryStoreSummaryFilterInput) {
          listKnowledgeRepositoryStoreSummaries(filter: $filter) {
            items {
              id
              userId
              categoryId
              totalMinutes
              lastUploadDate
              isDeleted
            }
          }
        }
      `;

      const result: any = await lastValueFrom(this.stakeholderKnowledgeService.apollo.query({
        query: gql(query),
        variables: {
          filter: {
            userId: { eq: this.personId },
            isDeleted: { eq: "false" }
          }
        }
      }));

      // Process the knowledge summaries first
      this.knowledgeSummaries = result.data.listKnowledgeRepositoryStoreSummaries.items.map((iterator: any) => {
        // Convert totalMinutes (e.g., "1h", "1m") into a number of minutes
        let minutes = 0;
        const timeString = iterator.totalMinutes.toString().toLowerCase();

        if (timeString.includes('h')) {
          const hours = parseInt(timeString.replace('h', ''));
          minutes += hours * 60; // Convert hours to minutes
        } else if (timeString.includes('m')) {
          minutes += parseInt(timeString.replace('m', '')); // Extract just the number
        } else {
          // If no unit, assume it's already in minutes
          minutes += parseInt(timeString) || 0;
        }

        return {
          ...iterator,
          totalMinutesWithoutLabel: minutes, // Store the numeric value
          totalMinutes: this.formatTime(minutes) // Format it for display
        };
      });

      // Calculate total minutes after processing
      this.totalCapturedMinutes = this.knowledgeSummaries.reduce((total, summary) =>
        total + summary.totalMinutesWithoutLabel, 0);

      this.updateCategoryDisplays();
      return result;
    } catch (error) {
      console.error('Error fetching knowledge summaries:', error);
      this.toastr.error('Failed to fetch knowledge summaries');
      return null;
    }
  }

  private updateCategoryDisplays() {
    this.categories.forEach((category: any) => {
      const summary = this.knowledgeSummaries.find(s => s.categoryId === category.id);
      if (summary) {
        category.totalMinutes = summary.totalMinutes;
        category.totalMinutesWithoutLabel = summary.totalMinutesWithoutLabel;
        category.lastUploadDate = new Date(summary.lastUploadDate).toLocaleDateString();
      } else {
        category.totalMinutes = '0';
        category.totalMinutesWithoutLabel = 0;
        category.lastUploadDate = null;
      }
    });
  }

  public categoryClickHandler(category: string, content: any) {
    this.modalService.open(content, {
      size: 'md',
      backdrop: 'static',
      keyboard: false
    });
  }

  public async closeModal() {
    // Cleanup any media elements before closing
    this.mediaElements.forEach(element => {
      if (element.src) {
        URL.revokeObjectURL(element.src);
      }
    });
    this.mediaElements = [];

    this.modalService.dismissAll();
    this.resetUploadState();
  }


  public getCategories() {
    this.categoriesService.listCategories().subscribe({
      next: (response: any) => {
        const allItems = response?.data?.listCategories?.items ?? [];
        // Get only main categories (no parentId)
        this.categories = allItems
          .filter((element: any) => !element._deleted && !element.parentId)
          .map((cat: { name: any; }, index: number) => ({ ...cat, name: cat.name ?? '', color: this.bgClasses[index % this.bgClasses.length], icon: this.bgIcons[index % this.bgIcons.length] }));

        // Store all subcategories separately
        this.subCategories = allItems
          .filter((element: any) => !element._deleted && element.parentId)
          .map((sub: { name: any; }) => ({ ...sub, name: sub.name ?? '' }));
        this.getStakeholderKnowledgeSummaries()
      },
      error: (error: any) => {
        this.toastr.error(error.message);
      },
    });
  }

  public fileDrop(event: DragEvent, modal: any, isDocument: boolean) {
    event.preventDefault();
    event.stopPropagation();
    const files = event.dataTransfer?.files;
    if (files) {
      this.handleFiles(files, modal, isDocument);
    }
  }

  public fileDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
  }

  public onChange(event: any, modal: any, isDocument: boolean) {
    const files = event.target.files;
    if (files) {
      this.handleFiles(files, modal, isDocument);
    }
  }

  private handleFiles(files: FileList, modal: any, isDocument: boolean) {
    const allowedTypes = ['audio/mpeg', 'audio/wav', 'video/mp4', 'video/webm'];
    const maxSize = 200 * 1024 * 1024; // 200MB limit

    // Cleanup existing files
    this.files.forEach(file => {
      const element = this.mediaElements.find(el => el.src.includes(file.name));
      if (element?.src) {
        URL.revokeObjectURL(element.src);
      }
    });

    Array.from(files).forEach(file => {
      if (!allowedTypes.includes(file.type)) {
        this.toastr.error(`File type ${file.type} is not supported. Please upload audio (MP3, WAV) or video (MP4, WebM) files.`);
        return;
      }

      if (file.size > maxSize) {
        this.toastr.error(`File ${file.name} is too large. Maximum size is 200MB.`);
        return;
      }

      // Set the file type when the file is first added
      const fileWithType = file as any;
      if (file.type.startsWith('audio/')) {
        fileWithType.fileType = 'AUDIO';
      } else if (file.type.startsWith('video/')) {
        fileWithType.fileType = 'VIDEO';
      }

      this.files.push(fileWithType);
    });
  }

  public onCategorySelect(event: any) {
    const categoryId = event?.target?.value;
    if (!categoryId) {
      this.selectedCategory = null;
      this.selectedSubcategories = [];
      this.showSubcategoryError = false;
      return;
    }

    this.selectedCategory = this.categories.find(c => c.id === categoryId);
    this.selectedSubcategories = [];
    this.showSubcategoryError = false;
  }

  isSubcategorySelected(subcategoryId: string): boolean {
    return this.selectedSubcategories.some(sub => sub.id === subcategoryId);
  }

  public onSubcategoryCheckboxChange(event: any, subcategory: any) {
    if (event.target.checked) {
      if (!this.selectedSubcategories.some(sub => sub.id === subcategory.id)) {
        this.selectedSubcategories.push(subcategory);
      }
    } else {
      this.selectedSubcategories = this.selectedSubcategories.filter(sub => sub.id !== subcategory.id);
    }
    this.showSubcategoryError = false;
  }

  public getSubcategoriesForCategory(categoryId: string): any[] {
    return this.subCategories.filter(sub => sub.parentId === categoryId);
  }

  getFileIcon(fileType: string): string {
    const baseIconPath = '../../../../../assets/media/icons/';
    if (fileType.startsWith('audio/')) {
      return `${baseIconPath}audio.png`;
    }
    return `${baseIconPath}video.png`;
  }

  async uploadFiles() {
    if (!this.files.length || !this.selectedCategory || !this.knowledgeName) {
      return;
    }

    if (this.selectedSubcategories.length === 0) {
      this.showSubcategoryError = true;
      return;
    }

    this.isUploading = true;
    this.uploadProgress = 0;
    this.currentFileIndex = 0;
    this.isTranscribing = false;
    this.transcriptionProgress = 0;
    this.transcriptionStatus = '';
    const totalFiles = this.files.length;
    const transcriptionTimeout = 300000; // 5 minutes timeout for transcription

    try {
      for (let i = 0; i < totalFiles; i++) {
        const file = this.files[i] as any; // Cast to any to access fileType
        this.currentUploadingFile = file;
        this.currentFileIndex = i;

        // Use the fileType we set in handleFiles, with fallback to MIME type detection
        this.currentFileType = file.fileType ??
                              (file.type.startsWith('audio/') ? 'AUDIO' : 'VIDEO');

        // Reset progress for new file
        this.uploadProgress = (i / totalFiles) * 100;

        const duration = await this.getMediaDuration(file);
        const fileType: FileType = this.currentFileType;

        // Start upload progress tracking
        const uploadPromise = new Promise<void>((resolve, reject) => {
          const uploadData: UploadData = {
            userId: this.personId,
            categoryId: this.selectedCategory.id,
            subCategoryIds: this.selectedSubcategories.map(sub => sub.id),
            file: file,
            fileType,
            knowledgeEntityType: 'STAKEHOLDER',
            name: this.knowledgeName,
            description: this.knowledgeDescription || '',
            durationInMinutes: duration,
            submittedBy: this.personId,
            isPublic: !this.isPrivate, // Invert the value for the API
          };

          this.stakeholderKnowledgeService.uploadStakeholderKnowledge(
            uploadData,
            (progress) => {
              // Calculate progress for current file (0-100%)
              const fileProgress = (progress.loaded / progress.total) * 100;
              // Calculate overall progress across all files (upload is 80% of total progress)
              const overallProgress = (i / totalFiles) * 100 + (fileProgress / totalFiles);
              this.uploadProgress = Math.round(overallProgress * 0.8); // Upload is 80% of total progress
              this.changeDetectorRef.detectChanges();
            }
          )
          .then(() => resolve())
          .catch(reject);
        });

        // Wait for upload to complete
        await uploadPromise;

        // Start transcription progress
        this.isTranscribing = true;
        this.transcriptionStatus = 'Processing your file...';

        // Simulate transcription progress (will be updated by actual progress if available)
        const startTime = Date.now();
        const updateProgress = () => {
          if (!this.isTranscribing) return;

          const elapsed = Date.now() - startTime;
          const progress = Math.min(100, Math.floor((elapsed / transcriptionTimeout) * 100));
          this.transcriptionProgress = progress;

          if (progress < 100) {
            setTimeout(updateProgress, 1000);
          }
        };

        updateProgress();

        // Set a timeout for transcription
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Transcription timed out')), transcriptionTimeout)
        );

        try {
          // Wait for either transcription to complete or timeout
          await Promise.race([
            new Promise(resolve => setTimeout(resolve, 2000)), // Simulate transcription delay
            timeoutPromise
          ]);

          // Update progress to 100% when done
          this.uploadProgress = Math.round(((i + 1) / totalFiles) * 100);
          this.transcriptionProgress = 100;
          this.transcriptionStatus = 'Processing complete!';

          // Small delay to show completion
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          console.error('Transcription error:', error);
          this.toastr.warning('File uploaded but transcription is taking longer than expected. It will continue in the background.');
        } finally {
          this.isTranscribing = false;
        }
      }

      this.toastr.success('Knowledge uploaded successfully');
      this.closeModal();
      this.resetUploadState();
      this.getCategories();
    } catch (error: any) {
      console.error('Upload error:', error);
      this.toastr.error(error.message ?? 'Error uploading files');
    } finally {
      this.isUploading = false;
      this.currentUploadingFile = null;
      this.currentFileIndex = 0;
    }
  }

  private resetUploadState() {
    // Cleanup any media elements
    this.mediaElements.forEach(element => {
      if (element.src) {
        URL.revokeObjectURL(element.src);
      }
    });

    this.mediaElements = [];
    this.files = [];
    this.selectedCategory = null;
    this.selectedSubcategories = [];
    this.knowledgeName = '';
    this.knowledgeDescription = '';
    this.uploadProgress = 0;
    this.isUploading = false;
    this.isTranscribing = false;
    this.transcriptionProgress = 0;
    this.transcriptionStatus = '';
    this.currentFileIndex = 0;
    this.currentUploadingFile = null;
    this.currentFileType = null;
    this.showSubcategoryError = false;
    this.isPrivate = true;
  }

  private getMediaDuration(file: File): Promise<number> {
    return new Promise((resolve, reject) => {
      const element = document.createElement(file.type.startsWith('audio/') ? 'audio' : 'video');
      element.preload = 'metadata';

      element.onloadedmetadata = () => {
        URL.revokeObjectURL(element.src);
        const durationInMinutes = Math.ceil(element.duration / 60);
        resolve(durationInMinutes);
      };

      element.onerror = () => {
        URL.revokeObjectURL(element.src);
        reject(new Error('Error loading media file'));
      };

      element.src = URL.createObjectURL(file);
      this.mediaElements.push(element);
    });
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatTime(value: number): string {
    if (value < 60) {
      return `${value}m`;
    } else {
      const hours = Math.round(value / 60);
      return `${hours}h`;
    }
  }


  ngOnDestroy() {
    // Cleanup any media elements
    this.mediaElements.forEach(element => {
      if (element.src) {
        URL.revokeObjectURL(element.src);
      }
    });
    this.mediaElements = [];

    // Reset state
    this.resetUploadState();
  }
}

