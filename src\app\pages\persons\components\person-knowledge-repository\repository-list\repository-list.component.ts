import { Component, Input, OnInit } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Storage } from 'aws-amplify';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { environment } from 'src/environments/environment';

import { CategoriesService } from 'src/app/pages/categories/services/categories.service';
import { StakeholderKnowledgeService } from '../services/stakeholder-knowledge.service';
import { TranscriptionRegenerationService } from 'src/app/shared/services/transcription-regeneration.service';

@Component({
  selector: 'app-repository-list',
  templateUrl: './repository-list.component.html',
  styleUrls: ['./repository-list.component.scss']
})
export class RepositoryListComponent implements OnInit {
  @Input() personId: string;
  @Input() isStudent: boolean = false;

  public repositoryFiles: any[] = [];
  public filteredFiles: any[] = [];
  public categories: any[] = [];
  public selectedCategory: string = '';
  public searchTerm: string = '';
  public loading: boolean = false;
  public currentTab: string = 'Table Panel';
  public showOptions: string = '';
  public getCountStartValue: number = 1;
  public getCountEndValue: number = 10;
  public sortBy: string = 'Recently Updated';

  // Add modal state for summary and metadata
  public summaryModalContent: { transcription: string; summary: string; metadata: any } | null = null;
  public metadataModalContent: any = null;
  public regeneratingTranscription: boolean = false;
  public currentSummaryFile: any = null;

  constructor(
    private readonly stakeholderKnowledgeService: StakeholderKnowledgeService,
    private readonly categoriesService: CategoriesService,
    private readonly spinner: NgxSpinnerService,
    private readonly toastr: ToastrService,
    private readonly modalService: NgbModal,
    private readonly transcriptionRegenerationService: TranscriptionRegenerationService
  ) { }

  ngOnInit(): void {
    if (this.personId) {
      this.loadCategories();
      this.loadRepositoryFiles();
    }
  }

  private loadCategories(): void {
    this.categoriesService.listCategories().subscribe({
      next: (response: any) => {
        const allItems = response?.data?.listCategories?.items ?? [];
        this.categories = allItems
          .filter((cat: any) => !cat._deleted && !cat.parentId)
          .map((cat: any) => ({
            id: cat.id,
            name: cat.name ?? '',
            description: cat.description ?? ''
          }));
        this.loadRepositoryFiles();
      },
      error: (error: any) => {
        console.error('Error loading categories:', error);
        this.toastr.error('Error loading categories');
      }
    });
  }

  private async loadRepositoryFiles(): Promise<void> {
    if (!this.personId) return;

    try {
      this.loading = true;

      const filter = { userId: { eq: this.personId } };
      const result = await this.stakeholderKnowledgeService.listKnowledgeRepositoryStores(filter);
      let items = result?.data?.listKnowledgeRepositoryStores?.items ?? [];

      // Sort items by createdAt in descending order (newest first) on the client side
      items = items.sort((a: any, b: any) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA; // For descending order (newest first)
      });

      this.repositoryFiles = items.map((item: any) => ({
        ...item,
        category: this.categories.find(c => c.id === item.categoryId)?.name ?? 'Uncategorized',
        categoryDescription: this.categories.find(c => c.id === item.categoryId)?.description ?? ''
      }));

      this.filteredFiles = [...this.repositoryFiles];
    } catch (error) {
      console.error('Error loading repository files:', error);
      this.toastr.error('Error loading repository files');
    } finally {
      this.loading = false;
    }
  }

  public onCategoryChange(categoryId: string): void {
    this.selectedCategory = categoryId;
    // Don't apply filters immediately
  }

  public clearFilter(): void {
    this.selectedCategory = '';
    this.searchTerm = '';
    this.filteredFiles = [...this.repositoryFiles];
  }

  public onSearchChange(term: string): void {
    this.searchTerm = term;
    // Don't apply filters immediately
  }

  public onSearch(): void {
    this.applyFilters();
  }

  public getCategoryDescription(): string {
    if (!this.selectedCategory || !this.categories) return '';
    const category = this.categories.find(c => c.id === this.selectedCategory);
    return category?.description ?? '';
  }

  public applyFilters(): void {
    let filtered = [...this.repositoryFiles];

    // Apply category filter
    if (this.selectedCategory) {
      filtered = filtered.filter(file => file.categoryId === this.selectedCategory);
    }

    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(file =>
        (file.name.toLowerCase().includes(searchLower) ??
        file.description?.toLowerCase().includes(searchLower)) ??
        file.category.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    filtered = this.sortFiles(filtered);

    this.filteredFiles = filtered;
  }

  private sortFiles(files: any[]): any[] {
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const lastQuarter = new Date();
    lastQuarter.setMonth(lastQuarter.getMonth() - 3);

    const lastYear = new Date();
    lastYear.setFullYear(lastYear.getFullYear() - 1);

    let filteredFiles = [...files];

    // First filter based on date ranges if needed
    switch (this.sortBy) {
      case 'Last Month':
        filteredFiles = files.filter(f => new Date(f.createdAt) >= lastMonth);
        break;
      case 'Last Quarter':
        filteredFiles = files.filter(f => new Date(f.createdAt) >= lastQuarter);
        break;
      case 'Last Year':
        filteredFiles = files.filter(f => new Date(f.createdAt) >= lastYear);
        break;
    }

    // Then sort by date
    return filteredFiles.sort((a, b) => {
      const dateA = new Date(b.updatedAt ?? b.createdAt).getTime();
      const dateB = new Date(a.updatedAt ?? a.createdAt).getTime();
      return dateA - dateB;
    });
  }

  public async downloadFile(file: any): Promise<void> {
    try {
      this.spinner.show();

      // Get the signed URL from S3
      const url: any = await Storage.get(file.fileUrl, {
        download: true,
        expires: 60 // URL expires in 60 seconds
      });

      // Create an anchor element
      const link = document.createElement('a');

      if (typeof url === 'object' && url.Body) {
        // Handle the downloaded blob
        const blob = url.Body as Blob;
        const downloadUrl = window.URL.createObjectURL(blob);

        // Set up download attributes
        link.href = downloadUrl;
        link.download = (file.name ?? file.fileUrl.split('/').pop()) ?? 'download';

        // Append to body, click and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Cleanup
        window.URL.revokeObjectURL(downloadUrl);
      } else {
        this.toastr.error('Error downloading file: Invalid response format');
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      this.toastr.error('Error downloading file');
    } finally {
      this.spinner.hide();
    }
  }

  public async deleteFile(file: any, confirmModal: any): Promise<void> {
    // Open confirmation dialog
    this.modalService.open(confirmModal, {
      ariaLabelledBy: 'modal-basic-title',
      centered: true
    }).result.then(
      async (result) => {
        if (result === 'confirm') {
          try {
            this.spinner.show();
            await this.stakeholderKnowledgeService.deleteStakeholderKnowledge(file.id);
            this.repositoryFiles = this.repositoryFiles.filter(f => f.id !== file.id);
            this.applyFilters();
            this.toastr.success('File deleted successfully');
          } catch (error: any) {
            console.error('Error deleting file:', error);
            this.toastr.error(error.message ?? 'Error deleting file');
          } finally {
            this.spinner.hide();
          }
        }
      },
      (reason) => {
        // Dialog dismissed
      }
    );
  }

  public formatDuration(minutes: number): string {
    if (!minutes && minutes !== 0) return '0m';

    // Round to nearest minute
    minutes = Math.round(minutes);

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours > 0) {
      return remainingMinutes > 0 ?
        `${hours}h ${remainingMinutes}m` :
        `${hours}h`;
    }
    return `${remainingMinutes}m`;
  }

  public refreshList(): void {
    this.loadRepositoryFiles();
  }

  public setCurrentTab(tab: string): void {
    this.currentTab = tab;
  }

  public onPageChange(event: any): void {
    this.getCountStartValue = event.startIndex;
    this.getCountEndValue = event.endIndex;
  }

  // Show summary modal with regenerate logic
  public showSummary(file: any, modal: any): void {
    this.currentSummaryFile = file;
    this.summaryModalContent = file.transcriptDetail;
    this.regeneratingTranscription = false;
    this.modalService.open(modal, { size: 'lg' });
  }

  // Regenerate transcription for a file using the shared service
  public async regenerateTranscriptionForFile(file: any): Promise<void> {
    // Get the S3 bucket name from environment
    const bucket = environment.s3.bucket;
    // Construct the full S3 URI with the public path
    const s3Uri = `s3://${bucket}/public/${file.fileUrl}`;
    
    await this.transcriptionRegenerationService.regenerateTranscription({
      file,
      type: 'knowledge',
      transcribeFn: () => this.stakeholderKnowledgeService.transcribeVideoOrAudio(s3Uri, file.id, 'knowledge'),
      fetchUpdatedFn: async () => {
        const updated = await this.stakeholderKnowledgeService.getKnowledgeRepositoryItem(file.id);
        file.transcriptDetail = updated?.transcriptDetail;
        this.summaryModalContent = file.transcriptDetail;
        return updated;
      },
      setLoading: (loading: boolean) => { this.regeneratingTranscription = loading; }
    });
  }

  // Show metadata modal
  public showMetadata(file: any, modal: any): void {
    this.modalService.open(modal, { 
      size: 'lg',
      windowClass: file.transcriptDetail ? '' : 'no-backdrop-modal'
    });

    if (!file.transcriptDetail) {
      this.metadataModalContent = null;
      return;
    }

    let meta = file.transcriptDetail;
    if (typeof meta === 'string') {
      try { 
        meta = JSON.parse(meta);
      } catch (error) {
        console.error('Error parsing transcriptDetail:', error);
        this.metadataModalContent = null;
        return;
      }
    }

    let metadata = meta?.metadata;
    if (typeof metadata === 'string') {
      try {
        metadata = JSON.parse(metadata);
      } catch (error) {
        console.error(error);
      }
    }

    // Only assign if metadata is a non-empty object
    if (metadata && typeof metadata === 'object' && Object.keys(metadata).length > 0) {
      this.metadataModalContent = metadata;
    } else {
      this.metadataModalContent = null;
    }
  }
}