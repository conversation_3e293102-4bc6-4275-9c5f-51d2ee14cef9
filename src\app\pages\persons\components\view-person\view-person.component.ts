import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { Storage } from 'aws-amplify';
import { NgxSpinnerService } from 'ngx-spinner';
import { map } from 'rxjs';

import { OrganizationsService } from 'src/app/pages/organizations/services/organizations.service';
import { PersonToPersonService } from 'src/app/pages/person-to-person/services/person-to-person.service';
import { SharedService } from '../../../../shared/services/shared.service';
import { PersonsService } from '../../services/persons.service';
import { BusinessesService } from 'src/app/pages/businesses/services/businesses.service';
import { PostsService } from 'src/app/pages/posts/services/posts.service';
import { environment } from 'src/environments/environment';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';
@Component({
  selector: 'app-view-person',
  templateUrl: './view-person.component.html',
  styleUrls: ['./view-person.component.scss'],
})
export class ViewPersonComponent implements OnInit {
  filters: any;
  personDetails: any;
  showOptions: string = '';
  _version: number;
  currentPage: number;
  sortBy: string = 'Recently Updated';
  personId: string = '';
  personName: string = '';
  currentTab: string = 'overview';
  allTransactions: any[] = [];
  memberPercentageList: any[] = [];
  chartColor: any;
  isStudent: boolean = false;
  activeId: string = '';
  oldImagesPreview: any[] = [];
  updatedImagesPreview: any[] = [];
  associationList: any[] = [];
  filteredAssociationList: any[] = [];
  pointsData: any[] = [];
  updatedData: any;
  updateImages: any;
  userList: any;
  title: any;
  activityId: string;
  type: any;
  updatedCityName: any;
  oldCityName: any;
  categoryWisePoints: any = {};
  listCategories: any = [];
  allPointLogs: any = [];
  activitiesList: any = [];
  activitiesCount: number = 0;
  cityData: any;
  loggedInUserId: string = '';
  loggedInUserName: string = '';
  updateProfile: boolean = false;
  userPoints: any = "-";
  mvtWalletBalance: any = null;
  isLoadingMVT: boolean = false;

  roleMap: { [key: string]: string } = {
    SCHOOL_OWNER: 'School Owner',
    SCHOOL_EMPLOYEE: 'School Employee',
    CUSTOMER: 'Customer',
    PRESIDENT: 'President',
    EXECUTIVE_DIRECTOR: 'Executive Director',
    BOARD_CHAIR: 'Board Chair',
    BOARD_MEMBER: 'Board Member',
    STAFF: 'Staff',
    VOLUNTEER: 'Volunteer',
    SUPER_ADMIN: 'Super Admin',
    STAFF_MEMBER: 'Staff Member',
    MEMBER: 'Member',
    SUBSCRIBER: 'Subscriber',
  };


  constructor(
    public router: Router,
    private readonly location: Location,
    private readonly toastr: ToastrService,
    private readonly modalService: NgbModal,
    private readonly spinner: NgxSpinnerService,
    public sharedService: SharedService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly personToPersonService: PersonToPersonService,
    private readonly organizationsService: OrganizationsService,
    private readonly businessService: BusinessesService,
    public personsService: PersonsService,
    private readonly postService: PostsService,
    private readonly mvtWalletService: MvtWalletService
  ) {
    this.sharedService.listCategories().subscribe({
      next: (response: any) => {
        this.listCategories = response?.data?.listCategories?.items.filter((type: any) => !type.parentId).map((category: any) => category.name);
      }
    })
  }

  ngOnInit(): void {
    this.personId = this.activatedRoute.snapshot.paramMap.get('id') || '';
    this.isStudent = this.activatedRoute.snapshot.url.join('/').split('/')[0].includes('student');
    this.sharedService.getUserData().subscribe((res: any) => {
      this.loggedInUserName = res?.name;
      this.loggedInUserId = res['custom:dynamodbId'];
    });
    if (this.personsService.filters) {
      this.filters = this.personsService.filters;
    }

    if (this.personsService.currentPage) {
      this.currentPage = this.personsService.currentPage;
    }

    this.getUserDetails();
    this.getAllMembers();
    let activityId: any = localStorage.getItem('actId');
    this.sharedService.getActivityById(activityId).subscribe((result) => {
      this.updatedData = result?.data?.getActivity;
      if (this.updatedData) {
        this.currentTab = 'logs';
        this.updateProfile = true;
      }
      if (this.updatedData?.updatedData?.imageUrl) {
        Storage.get(this.updatedData?.updatedData?.imageUrl, {
          level: 'public',
        }).then((result: string) => {
          this.updatedImagesPreview.push(result);
        });
      }

      this.sharedService.cityList.subscribe((cityList: any) => {

        let index = cityList.findIndex((element: any) => element?.id === this.updatedData?.updatedData?.cityId);
        if (index !== -1) {
          this.updatedCityName = cityList[index].name;
        }

        let indexForOldCity = cityList.findIndex((element: any) => element?.id === this.personDetails?.cityId);
        if (indexForOldCity !== -1) {
          this.oldCityName = cityList[indexForOldCity].name;
        }
      })
    });
    this.getUserList();
    this.getPointsData();
    this.getAllCities();
    this.sharedService.getActivitiesList(this.personId).subscribe({
      next: ((response: any) => {
        this.activitiesList = response.data.activitiesByDate.items;
        this.activitiesCount = response.data.activitiesByDate.items.length;
      })
    })
  }
  openImageCarouselModal(content: any, images: any) {
    this.updateImages = images;

    this.modalService.open(content, {
      size: 'md',
    });
  }

  getRoleDisplayName(role: string): string {
    return this.roleMap[role] || role;
  }

  getUserList() {
    this.sharedService
      .getUserList()
      .pipe(
        map((res) => {
          res?.data?.userByDate?.items?.forEach((userData: any) => {
            userData.phoneNumber = userData?.phoneNumber.replace(/-/g, '');
            userData.phoneNumber = userData?.phoneNumber.replace('+1', '');
          });
          return res;
        })
      )
      .subscribe(({ data }: any) => {
        this.userList = data.userByDate.items;
        this.userList = this.userList.filter(
          (element: any) =>
            element.id !== this.sharedService.userData.value['custom:dynamodbId'] &&
            element.cityId === this.sharedService?.defaultCityId?.value &&
            !element._deleted
        );
      });
  }


  goToOrganizationDetailsPage(id: string) {
    this.router.navigate([
      'organizations/view-organization',
      this.sharedService.getEncryptedId(id),
    ]);
  }

  get getCountStartValue() {
    return this.sharedService.getCountStartValue(
      this.filteredAssociationList.length
    );
  }

  get getCountEndValue() {
    return this.sharedService.getCountEndValue(
      this.filteredAssociationList.length
    );
  }

  getRandomColor(index: number) {
    return this.sharedService.randomColor(index);
  }

  setCurrentTab(value: string) {
    this.currentTab = value;
  }

  setFilters(associationData: any, personIdentification: any) {
    if (associationData?.type === 'Business') {
      this.businessService.currentPage = this.currentPage;
      this.router.navigate([
        '/schools/view-school',
        associationData?.business?.id,
      ]);
    } else if (associationData?.type === 'Organization') {
      this.organizationsService.currentPage = this.currentPage;

      this.router.navigate([
        '/members/view-member',
        this.sharedService.getEncryptedId(associationData?.organization?.id),
      ]);
    }
    else if (personIdentification !== 'person') {
      this.router.navigate([
        '/students/view-student',
        associationData?.otherPersonsID,
      ]).then(() => {
        let currentUrl = this.router.url;
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.router.navigate([currentUrl]);
        });
      });
    }
    else {
      this.router.navigate([
        '/students/view-student',
        associationData?.person?.id,
      ]).then(() => {
        let currentUrl = this.router.url;
        this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
          this.router.navigate([currentUrl]);
        });
      });
    }
  }

  navigateForEdit(id: string) {
    if (!this.router.url.includes('stakeholders')) {
      this.router.navigate(['/students/edit-student', id]);
    }
    else {
      this.router.navigate(['/stakeholders/edit-stakeholder', id]);
    }

  }

  getAllMembers(nextToken?: any): void {

    this.sharedService?.getAllPointsFilter(nextToken).subscribe((res: any) => {
      if (res?.data?.pointsByDate?.items) {
        this.pointsData.push(...res.data.pointsByDate.items);
      }

      if (res?.data?.pointsByDate?.nextToken !== null) {
        this.getAllMembers(res?.data?.pointsByDate?.nextToken);
      } else {
        this.getAllPointsData([...new Set(this.pointsData)]);
      }

    });
    this.spinner.hide();
    this.sharedService?.isLoading.next(false)
  }

  getAllPointsData(res: any) {
    const now = new Date();
    this.allTransactions = res?.filter((element: any) => !element?._deleted);
    this.memberPercentageList = this.sumPointsById(res).filter(
      (element: any) => element?.memberId === this.personId
    );

    this.memberPercentageList.forEach((memberElement: any) => {
      memberElement.impactScore = parseInt(memberElement?.impactScore);
      const createdAt = memberElement?.type !== 'student'
        ? memberElement?.organization?.createdAt
        : memberElement?.person?.createdAt;

      const diffInDays = this.calculateDaysSince(createdAt, now);
      const timeFramePoints = diffInDays * 2;

      const percentage = timeFramePoints === 0
        ? 0
        : this.calculatePercentage(memberElement.impactScore, timeFramePoints);

      memberElement.percentageWithTimeAllTime = percentage;
      this.setChartColor(percentage);
    });

    this.memberPercentageList.forEach((element: any) => {
      if (this.personId === element?.person?.id) {
        return parseInt(element?.impactScore);
      }
    });
  }

  private calculateDaysSince(createdAt: string, now: Date): number {
    const diffInMilliSeconds = now.getTime() - new Date(createdAt).getTime();
    return Math.round(diffInMilliSeconds / (24 * 60 * 60 * 1000));
  }

  private calculatePercentage(impactScore: number, timeFramePoints: number): number {
    return parseInt(((impactScore / timeFramePoints) * 100).toString());
  }

  private setChartColor(percentage: number): void {
    if (percentage >= 90) {
      this.chartColor = 'success';
    } else if (percentage >= 80) {
      this.chartColor = 'primary';
    } else if (percentage >= 70) {
      this.chartColor = 'info';
    } else if (percentage >= 60) {
      this.chartColor = 'warning';
    } else {
      this.chartColor = 'danger';
    }
  }

  sumPointsById(items: any[]): any[] {
    const result = [];
    const map = new Map();

    for (const item of items) {
      const memberId = item.memberId;
      const impactScore = Math.floor(item.impactScore);

      if (isNaN(impactScore)) continue;

      if (map.has(memberId)) {
        this.updateMemberScore(map.get(memberId), impactScore, item.status);
      } else {
        const member = this.createMember(item, impactScore);
        map.set(memberId, member);

        if (this.isMemberValid(member)) {
          result.push(member);
        }
      }
    }

    return result;
  }

  private updateMemberScore(member: any, score: number, status: string): void {
    const currentScore = Math.floor(member.impactScore);
    if (status === 'CREDITED') {
      member.impactScore = currentScore + score;
    } else if (status === 'DEBITED') {
      member.impactScore = currentScore - score;
    }
  }

  private createMember(item: any, impactScore: number): any {
    return {
      memberId: item.memberId,
      impactScore,
      ...item
    };
  }

  private isMemberValid(member: any): boolean {
    return (
      (member.organization && !member.organization._deleted) ||
      (member.person && !member.person._deleted)
    );
  }

  updateStatus(status: any) {
    this.sharedService.isLoading.next(true);
    this.approvedTask(status);
  }

  async isUserExist(cognitoUserObj: any) {
    let userExist = { userExist: false, mobile: 0, email: 0 };

    if (this.updatedData?.moduleId) {


      this.userList.forEach((item: any) => {
        if (
          ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase() ||
            item.phoneNumber === cognitoUserObj.phoneNumber) &&
          this.updatedData?.moduleId != item.id
        ) {
          userExist.userExist = true;
          if ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase()) {
            userExist.email = 1;
          }
          if (item.phoneNumber === cognitoUserObj.phoneNumber) {
            userExist.mobile = 1;
          }
          return userExist;
        }
      });
    } else {
      this.userList.forEach((item: any) => {
        if (
          (item.email as string).toLowerCase() ===
          cognitoUserObj.email.toLowerCase() ||
          '+1' + item.phoneNumber === cognitoUserObj.phoneNumber) {
          userExist.userExist = true;
          if ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase()) {
            userExist.email = 1;
          }
          if (item.phoneNumber === cognitoUserObj.phoneNumber) {
            userExist.mobile = 1;
          }
          return userExist;
        }
      });
    }
    return userExist;
  }

  async approvedTask(status: any): Promise<void> {
    let isUserExist: boolean = false;
    let moduleUser = this.updatedData.moduleUser;

    let cognitoUserObj = {
      firstName: this.updatedData.updatedData?.givenName,
      lastName: this.updatedData?.updatedData?.familyName,
      email: this.updatedData?.updatedData?.email,
      phoneNumber: this.updatedData?.updatedData?.phoneNumber,
      name:
        this.updatedData?.updatedData?.givenName +
        ' ' +
        this.updatedData?.updatedData?.familyName,
      id: this.updatedData?.moduleId,
    };

    cognitoUserObj.phoneNumber = cognitoUserObj?.phoneNumber.replace(/-/g, '');
    cognitoUserObj.phoneNumber = cognitoUserObj?.phoneNumber ? cognitoUserObj?.phoneNumber.replace(/\s/g, '') : "";
    cognitoUserObj.phoneNumber = cognitoUserObj?.phoneNumber ? cognitoUserObj?.phoneNumber.replace('+1', '') : "";

    await this.isUserExist(cognitoUserObj).then((result) => {
      if (result.userExist) {
        isUserExist = true;
        if (result.email === 1 && result.mobile === 1) {
          this.toastr.error('Email and phone number already exists');
        } else if (result.mobile === 1) {
          this.toastr.error('Phone number already exists.');
        } else if (result.email === 1) {
          this.toastr.error('Email already exists');
        }
      }
    });
    if (isUserExist) return;
    if (cognitoUserObj.email === moduleUser?.email) {
      delete cognitoUserObj.email
    }
    this.updatedData.moduleUser.givenName =
      this.updatedData?.updatedData?.givenName;
    this.updatedData.moduleUser.familyName =
      this.updatedData?.updatedData?.familyName;
    this.updatedData.moduleUser.email = this.updatedData?.updatedData?.email;
    this.updatedData.moduleUser.phoneNumber =
      this.updatedData?.updatedData?.phoneNumber;
    this.updatedData.moduleUser.cityId = this.updatedData?.updatedData?.cityId;
    this.updatedData.moduleUser.birthday = this.updatedData?.updatedData?.birthday;
    this.updatedData.moduleUser.gender = this.updatedData?.updatedData?.gender;
    this.updatedData.moduleUser.imageUrl = this.updatedData?.updatedData?.imageUrl;
    this.updatedData.moduleUser.walletAddress = this.updatedData?.updatedData?.walletAddress;
    if (this.updatedData?.updatedData?.walletAddress) {
      this.updatedData.moduleUser.walletAddress = this.updatedData?.updatedData?.walletAddress;
    }
    this.updatedData.moduleUser.name =
      this.updatedData?.updatedData?.givenName +
      ' ' +
      this.updatedData?.updatedData?.familyName;
    let paramData = this.updatedData?.moduleUser;
    delete paramData.membership;
    let number = this.updatedData?.updatedData?.phoneNumber.replace(/-/g, "").replace('+1', '');
    let userObj = {
      id: this.updatedData?.moduleUser?.id,
      "cognitoId": this.updatedData?.moduleUser?.cognitoId,
      "email": this.updatedData?.updatedData?.email?.toLowerCase(),
      "givenName": this.updatedData.updatedData?.givenName.trim(),
      "familyName": this.updatedData?.updatedData?.familyName.trim(),
      "userType": this.updatedData?.moduleUser?.userType,
      "name": this.updatedData.updatedData?.givenName.trim() +
        ' ' +
        this.updatedData.updatedData?.familyName,
      "phoneNumber": number ?? '',
      "streetAddressOne": this.updatedData.moduleUser?.streetAddressOne,
      "streetAddressTwo": this.updatedData.moduleUser?.streetAddressTwo,
      "city": this.updatedData?.moduleUser?.city.trim(),
      "endpointArn": this.updatedData?.moduleUser?.endpointArn,
      "isLogin": this.updatedData?.moduleUser?.isLogin,
      "cityId": this.updatedData?.updatedData?.cityId,
      "state": this.updatedData?.moduleUser?.state,
      "zipCode": this.updatedData?.moduleUser?.zipCode,
      "countryCode": this.updatedData?.moduleUser?.countryCode,
      "role": this.updatedData?.moduleUser?.role,
      "birthday": this.updatedData?.updatedData?.birthday,
      "ethnicity": this.updatedData?.moduleUser?.ethnicity ?? '',
      "gender": this.updatedData?.updatedData?.gender,
      "type": this.updatedData?.moduleUser?.type,
      "impactScore": this.updatedData?.moduleUser?.impactScore,
      "imageUrl": this.updatedData?.updatedData?.imageUrl,
      "walletAddress": this.updatedData?.updatedData?.walletAddress ?? this.updatedData?.moduleUser?.walletAddress,
      "status": this.updatedData?.moduleUser?.status,
      "registeredFrom": this.updatedData?.moduleUser?.registeredFrom,
      "isStakeholder": this.updatedData?.moduleUser?.isStakeholder,
      "memberCode": this.updatedData?.moduleUser?.memberCode,
      "stackholderCities": this.updatedData?.moduleUser?.stackholderCities,
      "isDeleted": this.updatedData?.moduleUser?.isDeleted,
      "createdUserId": this.loggedInUserId,
      "createdUserName": this.loggedInUserName
    }
    this.sharedService.editCognitoUserv2(userObj).subscribe({
      next: (response: any) => {
        let userData = this.updatedData?.moduleUser;
        delete this.updatedData?.createdBy;
        delete this.updatedData?.moduleUser;
        delete this.updatedData?._deleted;

        if (status === 'Approved') {
          this.updatedData.requestStatus = 'ADMIN_APPROVED';
        } else {
          this.updatedData.requestStatus = 'ADMIN_DENIED';
        }
        //update activity
        this.personsService
          .updateActivity({ id: this.updatedData.id, ...this.updatedData })
          .subscribe({
            next: () => {
              this.sharedService
                .generateLog({
                  type: 'UPDATED',
                  moduleId: userData?.id,
                  moduleName: userData?.name,
                  moduleType: userData?.isStakeholder === true ? 'stakeholder' : 'student',
                  requestStatus: 'SYSTEM_APPROVED',
                  activityType: 'MEMBERSHIP',
                  cityId:
                    this.sharedService?.defaultCityId?.value,
                })
                .subscribe();
              this.sendMessage(this.updatedData, userData, 'approve');
              this.toastr.success(
                'Profile updated successfully!'
              );
              this.sharedService.isLoading.next(false);

            },
            error: (error: any) => {

            },
          })
      },
      error: (err: any) => {

      }
    })
  }

  openRejectModel(content: any, id: string, title: string, type: any) {
    this.activityId = id;
    this.title = title;
    this.type = type;
    this.modalService.open(content, {
      size: 'md',
    });
  }

  rejectTask(status: any): void {
    let userData = this.updatedData?.moduleUser;
    delete this.updatedData?.createdBy;
    delete this.updatedData?.moduleUser;
    delete this.updatedData?._deleted;

    let requestStatus;
    if (status === 'Approved') {
      requestStatus = 'ADMIN_APPROVED';
    } else {
      requestStatus = 'ADMIN_DENIED';
    }
    this.updatedData.requestStatus = requestStatus;

    this.personsService
      .updateActivity({ id: this.updatedData.id, ...this.updatedData })
      .subscribe({
        next: ({ data }) => {
          this.sharedService
            .generateLog({
              type: 'UPDATED',
              moduleId: this.updatedData.moduleId,
              moduleName: this.updatedData?.moduleName,
              moduleType: userData?.isStakeholder === true ? 'stakeholder' : 'student',
              requestStatus: 'SYSTEM_APPROVED',
              activityType: 'MEMBERSHIP',
              cityId: this.sharedService?.defaultCityId?.value,
            })
            .subscribe();
          this.sendMessage(this.updatedData, userData, 'deny');
          this.toastr.success('Request Denied.');
          this.location.back();
        },
        error: (error: any) => {
        },
      });
    this.closeModal();
  }

  sendMessage(activityData: any, userData: any, requestType: any): void {
    let formData: any;
    if (requestType === 'deny') {
      formData = {
        title: activityData?.relatedName,
        body: 'Admin has denied your request.',
        notificationType: 'deny-update-profile',
        notificationIcon: 'notificationIcons/' + this.sharedService?.warningNotificationIconName,
      };
    } else {
      formData = {
        title: activityData?.relatedName,
        body: 'Admin has approved your request.',
        notificationType: 'approve-update-profile',
        notificationIcon: 'notificationIcons/' + this.sharedService?.feedbackNotificationiconName,
      };
    }

    formData.userList = [{ id: userData?.id, endpointArn: userData?.endpointArn, isLogin: userData?.isLogin }];

    this.sharedService.notificationSend(formData).subscribe((res: any) => { });
  }

  getRelationshipsList() {
    this.personToPersonService.getPersonToPersonRelationshipsList(this.personId, 'person').subscribe({
      next: ({ data }: any) => {
        this.associationList = data?.associationsByDate?.items;
        this.associationList = data?.associationsByDate?.items.filter((element: any) => element && !element?._deleted && !element?.business?._deleted && !element?.person?._deleted && element.person
          && !element?.organization?._deleted && element?.cityId === this.sharedService?.defaultCityId?.value
          && (((element.type === 'Person' && element.otherPersonsID && element.otherPerson) ||
            (element.type === "Business" || element.type === "Organization") && (!element.otherPersonsID || element.otherPersonsID === 'null') && !element?.otherPerson))
        );

        this.associationList?.forEach((element: any) => {
          if (element !== null) {
            element.personName = element?.person?.name;
            if (element?.type === 'Person') {
              element.otherRelationName = element?.otherPerson?.name;
            } else if (element?.type === 'Business') {
              element.otherRelationName = element?.business?.name;
            } else {
              element.otherRelationName = element?.organization?.name;
            }
          }
        });
        this.filteredAssociationList = this.associationList;

      },
      error: (error: any) => {
      },
    });
  }

  getUserDetails() {
    this.personsService.getUserById(this.personId)
      .subscribe({
        next: (response: any) => {
          if (response?.data?.getUser?._deleted || response?.data?.getUser === null) {
            this.toastr.warning(`This ${this.isStudent ? 'student' : 'stakeholder'} has been deleted!`);
            this.location.back();
            return;
          }
          this.personDetails = response?.data?.getUser;
          // Fetch MVT token balance after personDetails is set, but only for stakeholders
          if (this.personDetails?.isStakeholder) {
            this.fetchMVTTokenBalance();
          }
          if (this.personDetails?.role == "SUPER_ADMIN") {
            this.personDetails.role = 'Super Admin';
          } else if (this.personDetails?.role == "STAFF_MEMBER") {
            this.personDetails.role = 'Staff Member';
          } else if (this.personDetails?.role == "MEMBER") {
            this.personDetails.role = 'Member';
          } else if (this.personDetails?.role == "SUBSCRIBER") {
            this.personDetails.role = 'Subscriber';
          }

          if (this.personDetails?.imageUrl) {
            Storage.get(this.personDetails?.imageUrl, {
              level: 'public',
            }).then((result: string) => {
              this.personDetails.imagePreview = result;
              this.oldImagesPreview.push(result);
            });
          }

          this.getRelationshipsList();
        },
        error: (error: any) => {
          this.location.back();
        },
      });
  }

  openDeleteConfirmationModal(content: any, id: string, personName: string) {
    this.personId = id;
    this.personName = personName;

    this.modalService.open(content, {
      size: 'md',
    });
  }


  openDeleteConfirmationModalErase(content: any) {
    this.closeDeleteConfirmationModal();

    this.modalService.open(content, {
      size: 'md',
    });
  }

  closeDeleteConfirmationModal() {
    this.modalService.dismissAll();
  }

  deletePerson() {
    this.normalizeRole();

    const data = this.prepareDeletePayload();

    this.sharedService.isLoading.next(true);

    if (this.isStudent) {
      this.personsService.editUser(data, this.personId).subscribe({
        next: ({ data }) => this.handleStudentDeletionFlow(data),
        error: (data) => this.toastr.error(data.message),
      });
    } else {
      this.handleNonStudentDeletion();
    }
  }

  // --- Helpers Below ---

  private normalizeRole(): void {
    const roleMap: any = {
      'Super Admin': 'SUPER_ADMIN',
      'Staff Member': 'STAFF_MEMBER',
      'Member': 'MEMBER',
      'Subscriber': 'SUBSCRIBER',
    };
    const normalized = roleMap[this.personDetails?.role];
    if (normalized) this.personDetails.role = normalized;
  }

  private prepareDeletePayload(): any {
    const data = { ...this.personDetails, isDeleted: 'true' };
    delete this.personDetails?.imagePreview;
    delete data.updatedAt;
    delete data._deleted;
    delete data._lastChangedAt;
    delete data.cityData;
    return data;
  }

  private handleStudentDeletionFlow(data: any): void {
    const homeworkRelationIds = data?.updateUser?.homeworks?.items?.map((ex: any) => ex.id) ?? [];

    this.personsService.deleteUserRelations({ homeworkRelationIds }).subscribe();

    const associationIds = [
      ...(data?.updateUser?.associations?.items?.map((a: any) => a.id) ?? []),
      ...(data?.updateUser?.userAssociations?.items?.map((a: any) => a.id) ?? []),
    ];

    this.personToPersonService?.deleteWithStudents(associationIds).subscribe();

    this.personsService
      .deleteUser(data.updateUser?.id, data.updateUser?._version)
      .subscribe(() => this.afterUserDeleted());
  }

  private afterUserDeleted(): void {
    this.generateDeletionLog();

    this.sharedService.getMember(this.personId, 'person').subscribe(({ data }) => {
      const membership = data?.membershipByDate?.items?.[0];
      if (membership) {
        this.handleMembershipDeletion(membership);
      }
    });

    this.showDeletionSuccessMessage();
    this.sharedService.isLoading.next(false);
    this.spinner.hide();
    this.closeDeleteConfirmationModal();
  }

  private handleMembershipDeletion(membership: any): void {
    this.personsService.deleteMembership({
      id: membership.id,
      _version: membership._version,
    }).subscribe(({ data }) => {
      const updatedMembership = {
        ...membership,
        isDeleted: 'true',
        _version: data?.deleteMembership?._version,
      };
      delete updatedMembership.person;
      delete updatedMembership.organization;
      delete updatedMembership.updatedAt;
      delete updatedMembership._deleted;
      delete updatedMembership._lastChangedAt;

      this.personsService.editMembership(updatedMembership).subscribe();
    });
  }

  private generateDeletionLog(): void {
    this.sharedService.generateLog({
      type: 'DELETED',
      moduleId: this.personId,
      moduleName: `${this.personDetails.givenName} ${this.personDetails.familyName}`,
      moduleType: this.isStudent ? 'student' : 'stakeholder',
      requestStatus: 'SYSTEM_APPROVED',
      activityType: 'MEMBERSHIP',
      cityId: this.sharedService.defaultCityId.value,
    }).subscribe();
  }

  private showDeletionSuccessMessage(): void {
    if (this.router?.url?.includes('students')) {
      this.toastr.success('Successfully deleted student!');
      this.router.navigate(['/students']);
    } else {
      this.toastr.success('Successfully deleted stakeholder!');
      this.router.navigate(['/stakeholders']);
    }
  }

  private handleNonStudentDeletion(): void {
    this.sharedService.deleteUserV3(this.personId).subscribe({
      next: ({ data }: any) => {
        this.generateDeletionLog();
        this.showDeletionSuccessMessage();
        this.closeDeleteConfirmationModal();
      },
      error: (error: any) => { },
    });
  }

  checkFileExtension(key: string) {
    return /\.(gif|jpg|jpeg|tiff|png)$/i.test(key);
  }


  openOldImageCarouselModal(content: any, url: string) {
    this.modalService.open(content, {
      size: 'md',
    });

    this.activeId = this.oldImagesPreview
      .findIndex((imageUrl: string) => imageUrl === url)
      .toString();
  }
  closeModal() {
    this.modalService.dismissAll();
  }

  getPointsData(nextToken?: string) {
    this.sharedService.getAllPointsNextToken(nextToken).subscribe({
      next: (response: any) => {
        if (response?.data?.pointsByDate?.items) {
          this.allPointLogs.push(...response.data.pointsByDate.items);
        }
        if (response?.data?.pointsByDate?.nextToken === null) {
          let data: any = {};
          this.allPointLogs.map((point: any) => {
            if (this.personId === point?.memberId) {
              if (data.hasOwnProperty(point?.category)) {
                data[point?.category] = data[point?.category] + Number(point?.impactScore);
              } else {
                data[point?.category] = Number(point?.impactScore);
              }
            }
          });
          this.categoryWisePoints = data;

          // Only update userPoints from traditional points system if:
          // 1. User is not a stakeholder, OR
          // 2. User is a stakeholder but MVT wallet balance is not available
          const traditionalPoints = Object.values(data).reduce((prev: any, curr: any) => prev + curr, 0);

          if (!this.personDetails?.isStakeholder || !this.mvtWalletBalance) {
            this.userPoints = traditionalPoints;
            console.log(`Using traditional points system for ${this.personDetails?.isStakeholder ? 'stakeholder (no MVT data)' : 'non-stakeholder'}: ${this.userPoints}`);
          } else {
            console.log(`Keeping MVT wallet points for stakeholder: ${this.userPoints}, traditional would be: ${traditionalPoints}`);
          }
        } else {
          this.getPointsData(response?.data?.pointsByDate?.nextToken);
        }
      }
    })
  }

  /**
 * * Profile Navigation
 * ? This function is used to redirect the visitor to the url in parameter.
 * @param activity  This parameter holds the current activity data.
 * @param url This parameter holds the url used for redirect.
 */
  profileNavigation(activity: any, url: string) {
    localStorage.setItem('actId', activity.id);
    if (activity.moduleType === 'update-profile') {
      this.spinner.show('person_activity_spinner');
      this.updateProfile = true;
      let activityId: any = localStorage.getItem('actId');
      this.sharedService.getActivityById(activityId).subscribe((result) => {
        this.updatedData = result?.data?.getActivity;
        if (this.updatedData) {
          this.currentTab = 'logs';
          this.updateProfile = true;
        }
        if (this.updatedData?.updatedData?.imageUrl) {
          Storage.get(this.updatedData?.updatedData?.imageUrl, {
            level: 'public',
          }).then((result: string) => {
            this.updatedImagesPreview.push(result);
          });
        }
        this.spinner.hide('person_activity_spinner');
      });
    } else {
      this.router.navigate([url])
    }
  }

  /**
 * * Navigate To Organization
 * ? This function is used for redirecting visitor to member's profile page.
 * @param data This parameter holds the clicked member's data
 */
  navigateToOrganization(data: any): void {
    this.router.navigate([
      '/members/view-member',
      this.sharedService.getEncryptedId(data?.relatedId),
    ]);
  }

  /**
 * * Get All Cities
 * ? This function is used for getting all city data.
 */
  getAllCities() {
    this.sharedService.getAllCities().subscribe({
      next: ((response: any) => {
        this.cityData = Object.fromEntries(response.data.listCities.items.map((city: any) => [city.id, city.name]));
      })
    })
  }


  /**
 * * Add Submission Points
 * ? This function is used for adding points as per the homework submitted.
 * @param response This parameter consists of the homework data.
 * @param type This parameter holds the type of entity performing the activity of submission.
 * @param activity This parameter holds the activity data of the submission action,
 */
  addSubmissionPoints(response: any, type: any, activity: any): void {
    const isMember = type === 'member';
    const data = isMember ? response?.data?.updateHomeworkOrganizations : response?.data?.updateHomeworkUsers;
    const memberData = isMember ? data?.memberData : data?.studentStakeholderData;
    const membership = memberData?.membership;

    const memberId = isMember ? data?.memberId : data?.studentStakeholderId;
    const impactScore = data?.homeworkData?.assignmentPoints;
    let entityType: string;
    if (isMember) {
      entityType = 'member';
    } else if (memberData?.isStakeholder) {
      entityType = 'stakeholder';
    } else {
      entityType = 'student';
    }
    const coCreationType = data?.homeworkData?.coCreationType;
    const MVPTokens = membership?.MVPTokens;
    const currentImpactScore = membership?.currentImpactScore;
    const membershipId = membership?.id;
    const memberName = memberData?.name;
    const imageUrl = memberData?.imageUrl;
    const version = membership?._version;

    this.sharedService.addPoints(
      {
        memberId: memberId,
        impactScore: impactScore,
        MVPTokens: impactScore * this.sharedService?.tokenBaseValue,
        pointType: 'homework',
        status: 'CREDITED',
        type: entityType,
        cityId: this.sharedService.defaultCityId.value,
        createdBy: this.loggedInUserId,
        category: coCreationType,
      },
    ).subscribe({
      next: (result => {
        // Transfer MVT tokens using MVT wallet service
        this.mvtWalletService.adminTransferMVT(this.personDetails?.id, impactScore, `Admin transferred ${impactScore} MVT tokens for co-creation activity`).subscribe({
          next: (response: any) => {
            console.log("MVT transfer success for co-creation:", response);
            if (response?.data?.adminTransferMVT?.statusCode === 200) {
              console.log("MVT transfer completed successfully for co-creation");
            }
          },
          error: (error: any) => {
            console.error("MVT transfer error for co-creation:", error);
          }
        });
        this.sharedService.updateMembership({
          id: membershipId,
          imageUrl: imageUrl,
          currentImpactScore: parseFloat(currentImpactScore) + impactScore,
          MVPTokens: parseFloat(MVPTokens) + (impactScore * this.sharedService?.tokenBaseValue),
          lastAddedImpactScore: impactScore,
          _version: version,
        });
      })
    })
    this.sharedService
      .generateLog({
        type: 'UPDATED',
        moduleId: activity.moduleId,
        moduleName: activity.moduleName,
        moduleType: 'homework-submission',
        relatedTo: entityType,
        relatedId: memberId,
        relatedName: memberName,
        activityType: 'MEMBERSHIP',
        cityId: this.sharedService.defaultCityId.value,
        activityTokens: impactScore.toString()
      })
      .subscribe((log) => {
      });
  }


  /**
   * * Add Microcredential Points
   * ? This function is used to submit the earned points to the microcredential.
   * @param response API response for processing the point add procedure.
   * @param type This parameter accepts the type of entity connected to the microcredential in question.
   */
  addMicrocredentialPoints(response: any, type: any): void {
    let microcredentialId: any, memberId: any, microcredentialpoint: any, assignmentPoints: any, entityType: any, microcredentialType: any, membershipId: any, currentImpactScore: any, MVPTokens: any, version: any;
    if (type === 'member') {
      microcredentialId = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.id;
      microcredentialpoint = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.totalPoints;
      memberId = response?.data?.updateHomeworkOrganizations?.memberId;
      assignmentPoints = response?.data?.updateHomeworkOrganizations?.homeworkData?.assignmentPoints ?? 0;
      entityType = 'member';
      microcredentialType = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.type;
      membershipId = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.id;
      currentImpactScore = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.currentImpactScore;
      MVPTokens = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.MVPTokens;
      version = response?.data?.updateHomeworkOrganizations?.memberData?.membership?._version;

      this.postService.getHomeworkMembersByMicrocredential(memberId, microcredentialId).subscribe({
        next: ((data: any) => {
          let homeworkUsersData = data.data.listHomeworkOrganizations.items.filter(
            (element: any) =>
              !element?._deleted && element?.homeworkStatus === "completed"
          );
          let completedHomeworkPoints = homeworkUsersData.reduce((prev: any, curr: any) => prev + Number(curr?.homeworkData?.assignmentPoints), 0);
          let newCompletedHomeworkPoints = Number(completedHomeworkPoints) + Number(assignmentPoints);
          if (microcredentialpoint > completedHomeworkPoints && microcredentialpoint <= newCompletedHomeworkPoints) {
            this.sharedService.addPoints(
              {
                memberId: memberId,
                impactScore: microcredentialpoint,
                MVPTokens: microcredentialpoint * this.sharedService?.tokenBaseValue,
                pointType: 'microcredential',
                status: 'CREDITED',
                type: entityType,
                cityId: this.sharedService.defaultCityId.value,
                createdBy: this.loggedInUserId,
                category: microcredentialType,
              },
            ).subscribe({
              next: (result => {
                // Transfer MVT tokens using new MVT wallet system
                this.mvtWalletService.adminTransferMVT(
                  this.personDetails?.id,
                  microcredentialpoint,
                  `Microcredential reward: ${microcredentialpoint} MVT tokens`
                ).subscribe({
                  next: (result) => console.log('MVT transfer successful:', result),
                  error: (error) => console.error('MVT transfer failed:', error)
                })
                this.sharedService.updateMembership({
                  id: membershipId,
                  currentImpactScore: parseFloat(currentImpactScore) + microcredentialpoint,
                  MVPTokens: parseFloat(MVPTokens) + (microcredentialpoint * this.sharedService?.tokenBaseValue),
                  lastAddedImpactScore: microcredentialpoint,
                  _version: version,
                });
              })
            })
          }
        })
      })
    } else {
      microcredentialId = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.id;
      microcredentialpoint = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.totalPoints;
      memberId = response?.data?.updateHomeworkUsers?.studentStakeholderId;
      assignmentPoints = response?.data?.updateHomeworkUsers?.homeworkData?.assignmentPoints ?? 0;
      entityType = response?.data?.updateHomeworkUsers?.studentStakeholderData?.isStakeholder ? 'stakeholder' : 'student';
      microcredentialType = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.type;
      membershipId = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.id;
      currentImpactScore = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.currentImpactScore;
      MVPTokens = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.MVPTokens;
      version = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?._version

      this.postService.getHomeworkUsersByMicrocredential(memberId, microcredentialId).subscribe({
        next: ((data: any) => {
          let homeworkUsersData = data.data.listHomeworkUsers.items.filter(
            (element: any) =>
              !element?._deleted && element?.homeworkStatus === "completed"
          );
          let completedHomeworkPoints = homeworkUsersData.reduce((prev: any, curr: any) => prev + Number(curr?.homeworkData?.assignmentPoints), 0);

          let newCompletedHomeworkPoints = Number(completedHomeworkPoints) + Number(assignmentPoints);

          if (microcredentialpoint > completedHomeworkPoints && microcredentialpoint <= newCompletedHomeworkPoints) {
            this.sharedService.addPoints(
              {
                memberId: memberId,
                impactScore: microcredentialpoint,
                MVPTokens: microcredentialpoint * this.sharedService?.tokenBaseValue,
                pointType: 'microcredential',
                status: 'CREDITED',
                type: entityType,
                cityId: this.sharedService.defaultCityId.value,
                createdBy: this.loggedInUserId,
                category: microcredentialType,
              },
            ).subscribe({
              next: (result => {
                // Transfer MVT tokens using new MVT wallet system
                this.mvtWalletService.adminTransferMVT(
                  this.personDetails?.id,
                  microcredentialpoint,
                  `Microcredential reward: ${microcredentialpoint} MVT tokens`
                ).subscribe({
                  next: (result) => console.log('MVT transfer successful:', result),
                  error: (error) => console.error('MVT transfer failed:', error)
                })
                this.sharedService.updateMembership({
                  id: membershipId,
                  currentImpactScore: parseFloat(currentImpactScore) + microcredentialpoint,
                  MVPTokens: parseFloat(MVPTokens) + (microcredentialpoint * this.sharedService?.tokenBaseValue),
                  lastAddedImpactScore: microcredentialpoint,
                  _version: version,
                });
              })
            })
          }
        })
      });
    }
  }
  /**
 * * Update Activity
 * ? This function is used for updating the activity changes.
 * @param request This parameter holds the type of the request.
 * @param activity This parameter holds the activity data to be be updated.
 */
  updateActivity(request: any, activity: any): void {
    this.sharedService.isLoading.next(true);
    this.spinner.show();

    const activityUpdate = {
      id: activity?.id,
      requestStatus: request,
      _version: activity?._version,
    };

    this.sharedService.updateActivity(activityUpdate).subscribe((resActivity: any) => {
      switch (activity.moduleType) {
        case 'homework-submission':
          this.handleHomeworkSubmission(request, activity);
          break;

        case 'update-profile':
          this.handleProfileUpdate(request, activity);
          break;

        default:
          this.handleAssociationUpdate(request, activity, resActivity);
          break;
      }

      this.sharedService.isLoading.next(false);
      this.spinner.hide();
    });
  }

  private handleHomeworkSubmission(request: string, activity: any): void {
    const homeworkStatus = request === 'ADMIN_APPROVED' ? 'completed' : 'denied';

    const updatePostStatus = () => {
      this.postService.getSubmissionById(activity?.moduleId).subscribe({
        next: (result: any) => {
          this.postService.updatePost({
            id: activity?.moduleId,
            isActive: request === 'ADMIN_APPROVED' ? 'approved' : 'denied',
            _version: result?.data?.getSubmission?._version,
          }).subscribe(() => {
            activity.requestStatus = request;
          });
        },
      });
    };

    const handleUpdate = (response: any, type: 'user' | 'member') => {
      if (request === 'ADMIN_APPROVED') {
        this.addSubmissionPoints(response, type, activity);
        this.addMicrocredentialPoints(response, type);
      }
      updatePostStatus();
    };

    if (['stakeholder', 'student', 'person'].includes(activity.relatedTo)) {
      this.postService.getHomeworkUsersById(activity?.updatedData?.id).subscribe({
        next: (res: any) => {
          this.postService.updateHomeworkUsers({
            id: activity?.updatedData?.id,
            homeworkStatus,
            _version: res._version,
          }).subscribe({
            next: (response: any) => handleUpdate(response, 'user'),
          });
        },
      });
    } else if (['member', 'organization'].includes(activity.relatedTo)) {
      this.postService.getHomeworkOrganizationsById(activity?.updatedData?.id).subscribe({
        next: (res: any) => {
          this.postService.updateHomeworkOrganizations({
            id: activity?.updatedData?.id,
            homeworkStatus,
            _version: res._version,
          }).subscribe({
            next: (response: any) => handleUpdate(response, 'member'),
          });
        },
      });
    }
  }

  private handleProfileUpdate(request: string, activity: any): void {
    if (request === 'ADMIN_APPROVED') {
      this.approvedActivityTask('Approved', activity);
    } else if (request === 'ADMIN_DENIED') {
      this.rejectActivityTask('Denied', activity);
    }
  }

  private handleAssociationUpdate(request: string, activity: any, resActivity: any): void {
    this.sharedService.getAssociationData(activity).subscribe({
      next: (res) => {
        const item = res.data.associationsByDate.items[0];
        const input = {
          id: item?.id,
          status: true,
          _version: item?._version,
        };

        if (resActivity?.data?.updateActivity?.requestStatus === 'ADMIN_APPROVED') {
          this.toastr.success('Activity Approved.!');
          activity.requestStatus = request;
          this.sharedService.updateAssociation(input).subscribe();
        } else {
          this.toastr.success('Activity Denied.!');
          activity.requestStatus = request;
          this.sharedService.deleteAssociation([item?.id]).subscribe();
        }
      },
    });
  }


  /**
 * * Is User Exist Activity
 * ? This function is used to check if the user exists or not.
 * @param cognitoUserObj This parameter holds the cognito user data object.
 * @param activityData This parameter holds the data of the activity in question.
 * @returns THis function returns true or false.
 */
  async isUserExistActivity(cognitoUserObj: any, activityData: any) {
    let userExist = { userExist: false, mobile: 0, email: 0 };

    if (activityData?.moduleId) {


      this.userList.forEach((item: any) => {
        if (
          ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase() ||
            item.phoneNumber === cognitoUserObj.phoneNumber) &&
          activityData?.moduleId != item.id
        ) {
          userExist.userExist = true;
          if ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase()) {
            userExist.email = 1;
          }
          if (item.phoneNumber === cognitoUserObj.phoneNumber) {
            userExist.mobile = 1;
          }
          return userExist;
        }
      });
    } else {
      this.userList.forEach((item: any) => {
        if (
          (item.email as string).toLowerCase() ===
          cognitoUserObj.email.toLowerCase() ||
          '+1' + item.phoneNumber === cognitoUserObj.phoneNumber) {
          userExist.userExist = true;
          if ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase()) {
            userExist.email = 1;
          }
          if (item.phoneNumber === cognitoUserObj.phoneNumber) {
            userExist.mobile = 1;
          }
          return userExist;
        }
      });
    }
    return userExist;
  }

  /**
 * * Approve Activity Task
 * ? This function is called on the click of approve button and is used to set the status of the activity to given status.
 * @param status This parameter holds the status that is to be set.
 * @param activityData This parameter holds the data of the activity whose status is to be updated.
 * @returns If all checks are passed then it returns the activity data with updated status , else it returns false.
 */
  async approvedActivityTask(status: any, activityData: any): Promise<void> {

    let isUserExist: boolean = false;
    let moduleUser = activityData.moduleUser;
    let moduleMembership = Object.assign(moduleUser.membership);
    let cognitoUserObj = {
      firstName: activityData.updatedData?.givenName,
      lastName: activityData?.updatedData?.familyName,
      email: activityData?.updatedData?.email,
      phoneNumber: activityData?.updatedData?.phoneNumber,
      name:
        activityData?.updatedData?.givenName +
        ' ' +
        activityData?.updatedData?.familyName,
      id: activityData?.moduleId,
      walletAddress: activityData?.updatedData?.walletAddress
    };

    cognitoUserObj.phoneNumber = cognitoUserObj?.phoneNumber.replace(/-/g, '');
    cognitoUserObj.phoneNumber = cognitoUserObj?.phoneNumber ? cognitoUserObj?.phoneNumber.replace('+1', '') : "";

    await this.isUserExistActivity(cognitoUserObj, activityData).then((result) => {
      if (result.userExist) {
        isUserExist = true;
        if (result.email === 1 && result.mobile === 1) {
          this.toastr.error('Email and phone number already exists');
        } else if (result.mobile === 1) {
          this.toastr.error('Phone number already exists.');
        } else if (result.email === 1) {
          this.toastr.error('Email already exists');
        }
      }
    });
    if (isUserExist) return;
    if (cognitoUserObj.email === moduleUser?.email) {
      delete cognitoUserObj.email
    }

    activityData.moduleUser.givenName =
      activityData?.updatedData?.givenName;
    activityData.moduleUser.familyName =
      activityData?.updatedData?.familyName;
    activityData.moduleUser.email = activityData?.updatedData?.email;
    activityData.moduleUser.phoneNumber =
      activityData?.updatedData?.phoneNumber.replace(/-/g, '');
    activityData.moduleUser.cityId = activityData?.updatedData?.cityId;
    activityData.moduleUser.birthday = activityData?.updatedData?.birthday;
    activityData.moduleUser.gender = activityData?.updatedData?.gender;
    activityData.moduleUser.imageUrl = activityData?.updatedData?.imageUrl;
    activityData.moduleUser.name =
      activityData?.updatedData?.givenName +
      ' ' +
      activityData?.updatedData?.familyName;
    let paramData = activityData?.moduleUser;
    delete paramData.membership;
    // Build user object for editCognitoUserv2 (match activities.component.ts)
    const updated = activityData.updatedData ?? {};
    const userObj = {
      id: moduleUser.id,
      cognitoId: moduleUser.cognitoId,
      email: updated.email?.toLowerCase(),
      givenName: updated.givenName?.trim(),
      familyName: updated.familyName?.trim(),
      userType: moduleUser.userType,
      name: `${updated.givenName?.trim()} ${updated.familyName?.trim()}`,
      phoneNumber: (updated.phoneNumber ?? '').replace(/-/g, '').replace('+1', ''),
      streetAddressOne: moduleUser.streetAddressOne,
      streetAddressTwo: moduleUser.streetAddressTwo,
      city: moduleUser.city?.trim(),
      cityId: updated.cityId,
      state: moduleUser.state,
      zipCode: moduleUser.zipCode,
      countryCode: moduleUser.countryCode,
      role: moduleUser.role,
      birthday: updated.birthday,
      ethnicity: moduleUser.ethnicity ?? '',
      gender: updated.gender,
      walletAddress: (updated.walletAddress ?? moduleUser.walletAddress) ?? '',
      type: moduleUser.type,
      impactScore: moduleUser.impactScore,
      imageUrl: updated.imageUrl,
      status: moduleUser.status,
      registeredFrom: moduleUser.registeredFrom,
      isStakeholder: moduleUser.isStakeholder,
      memberCode: moduleUser.memberCode,
      stackholderCities: moduleUser.stackholderCities,
      isDeleted: moduleUser.isDeleted,
      createdUserId: this.loggedInUserId,
      createdUserName: this.loggedInUserName,
    };
    this.personsService
      .editUser(paramData, activityData?.moduleId)
      .subscribe({
        next: () => {
          this.sharedService.editCognitoUserv2(userObj).subscribe({
            next: (res: any) => { },
            error: (error) => { },
          });

          if (moduleUser) {
            if (moduleMembership) {
              moduleMembership['name'] =
                activityData?.updatedData?.givenName +
                ' ' +
                activityData?.updatedData?.familyName;
              moduleMembership['imageUrl'] =
                activityData?.updatedData?.imageUrl;
              moduleMembership['cityId'] =
                activityData?.updatedData?.cityId;
              this.personsService
                .editMembership({
                  ...moduleMembership,
                })
                .subscribe((res: any) => {
                  if (res) {
                    let userData = activityData?.moduleUser;
                    delete activityData?.createdBy;
                    delete activityData?.moduleUser;
                    delete activityData?._deleted;
                    delete activityData?.relatedUser;
                    delete activityData?.relatedMember;
                    delete activityData?.moduleMember;
                    if (status === 'Approved') {
                      activityData.requestStatus = 'ADMIN_APPROVED';
                    } else {
                      activityData.requestStatus = 'ADMIN_DENIED';
                    }
                    //update activity
                    this.personsService
                      .updateActivity({ id: activityData.id, ...activityData })
                      .subscribe({
                        next: () => {
                          this.sharedService
                            .generateLog({
                              type: 'UPDATED',
                              moduleId: userData?.id,
                              moduleName: userData?.name,
                              moduleType: userData?.isStakeholder === true ? 'stakeholder' : 'student',
                              requestStatus: 'SYSTEM_APPROVED',
                              activityType: 'MEMBERSHIP',
                              cityId:
                                this.sharedService?.defaultCityId?.value,
                            })
                            .subscribe();
                          this.sendMessage(activityData, userData, 'approve');
                          this.toastr.success(
                            'Profile updated successfully!'
                          );
                          this.sharedService.isLoading.next(false);
                        },
                        error: (error: any) => {
                        },
                      });
                  }
                });
            }
          }
        },
        error: (error: any) => {
          this.toastr.error(error?.message);
          this.sharedService.isLoading.next(false);
        },
      });
  }

  /**
   * * Reject Activity Task
   * ? This function is called on the click of reject button and is used to set the status of the activity to given status.
   * @param status This parameter holds the status that is to be set.
   * @param activityData This parameter holds the data of the activity whose status is to be updated.
   */
  rejectActivityTask(status: any, activityData: any): void {
    let userData = activityData?.moduleUser;
    let notificationData = activityData;
    delete activityData?.createdBy;
    delete activityData?.moduleUser;
    delete activityData?._deleted;
    delete activityData?.relatedUser;
    delete activityData?.relatedMember;
    delete activityData?.moduleMember;

    activityData.requestStatus = status === 'Approved'
      ? 'ADMIN_APPROVED'
      : 'ADMIN_DENIED';

    this.personsService
      .updateActivity({ id: activityData.id, ...activityData })
      .subscribe({
        next: ({ data }) => {
          this.sharedService
            .generateLog({
              type: 'UPDATED',
              moduleId: activityData.moduleId,
              moduleName: activityData?.moduleName,
              moduleType: userData?.isStakeholder === true ? 'stakeholder' : 'student',
              requestStatus: 'SYSTEM_APPROVED',
              activityType: 'MEMBERSHIP',
              cityId: this.sharedService?.defaultCityId?.value,
            })
            .subscribe();
          this.sendMessage(notificationData, userData, 'deny');
          this.toastr.success('Request Denied.');
        },
        error: (error: any) => {
        },
      });
    this.closeModal();
  }

  showActivityList(): void {
    this.updateProfile = false;
    this.updatedImagesPreview = [];
    this.sharedService.getActivitiesList(this.personId, environment.recordsLimit, true).subscribe({
      next: (response: any) => {
        this.activitiesList = response.data.activitiesByDate.items;
        this.activitiesCount = response.data.activitiesByDate.items.length;
        this.updatedData = {};
      }
    })
  }

  fetchMVTTokenBalance() {
    this.mvtWalletBalance = null;
    if (this.personDetails?.id) {
      this.isLoadingMVT = true;

      this.mvtWalletService.getUserMVTWalletBalance({ userId: this.personDetails.id }).subscribe({
        next: (response: any) => {
          console.log('MVT Wallet Balance Response:', response);

          if (response?.data?.getUserMVTWalletBalance?.statusCode === 200) {
            this.mvtWalletBalance = response.data.getUserMVTWalletBalance.data;
            console.log('MVT Wallet Balance Data:', this.mvtWalletBalance);

            if (this.personDetails?.isStakeholder && this.mvtWalletBalance?.totalReceived !== undefined) {
              this.userPoints = this.mvtWalletBalance.totalReceived;
              console.log(`Updated userPoints for stakeholder to MVT totalReceived: ${this.userPoints}`);
            }
          } else {
            console.error('MVT Wallet Balance API Error:', response?.data?.getUserMVTWalletBalance?.message);
            this.mvtWalletBalance = null;
          }
          this.isLoadingMVT = false;
        },
        error: (error: any) => {
          console.error('Error fetching MVT wallet balance:', error);
          this.mvtWalletBalance = null;
          this.isLoadingMVT = false;
        }
      });
    } else {
      console.warn('No user ID available for fetching MVT balance');
    }
  }

  /**
   * Get the display value for user points
   * @returns Formatted points value
   */
  getDisplayPoints(): string {
    if (this.personDetails?.isStakeholder && this.mvtWalletBalance?.totalReceived !== undefined) {
      return this.mvtWalletBalance.totalReceived.toFixed(0);
    }
    return this.userPoints?.toString() ?? '0';
  }

  /**
   * Get the points source label for display
   * @returns Label indicating the source of points
   */
  getPointsSourceLabel(): string {
    if (this.personDetails?.isStakeholder && this.mvtWalletBalance?.totalReceived !== undefined) {
      return 'Points (MVT)';
    }
    return 'Points';
  }

  /**
   * Check if points are from MVT wallet
   * @returns True if points are from MVT wallet
   */
  isUsingMVTPoints(): boolean {
    return this.personDetails?.isStakeholder && this.mvtWalletBalance?.totalReceived !== undefined;
  }

  /**
   * Generate tooltip content for balance details
   * @returns Plain text string for tooltip
   */
  getBalanceTooltip(): string {
    if (!this.mvtWalletBalance) {
      return 'Balance details unavailable';
    }

    const received = this.mvtWalletBalance.totalReceived ?? 0;
    const sent = this.mvtWalletBalance.totalSent ?? 0;
    const locked = this.mvtWalletBalance.lockedBalance ?? 0;
    const available = this.mvtWalletBalance.availableBalance ?? (this.mvtWalletBalance.balance - locked);

    let tooltip = `Balance Details\nTotal Balance: ${this.mvtWalletBalance.balance.toFixed(2)} MVT\nReceived: +${received.toFixed(2)}\nSent: -${sent.toFixed(2)}`;

    if (locked > 0) {
      tooltip += `\nLocked: ${locked.toFixed(2)} MVT\nAvailable: ${available.toFixed(2)} MVT`;
    }

    return tooltip;
  }

  /**
   * Check if user has locked MVT tokens
   * @returns True if user has locked tokens
   */
  hasLockedBalance(): boolean {
    return this.mvtWalletBalance?.lockedBalance && this.mvtWalletBalance.lockedBalance > 0;
  }

  /**
   * Get the available balance (total - locked)
   * @returns Available balance amount
   */
  getAvailableBalance(): number {
    if (!this.mvtWalletBalance) {
      return 0;
    }

    const locked = this.mvtWalletBalance.lockedBalance ?? 0;
    return this.mvtWalletBalance.availableBalance ?? (this.mvtWalletBalance.balance - locked);
  }
}
