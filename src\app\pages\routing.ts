import { Routes } from '@angular/router';
import { SendNotificationsComponent } from './send-notifications/send-notifications.component';
import { AuthGuard } from '../shared/guards/auth.guard.service';

const Routing: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full',
  },
  {
    path: 'profile',
    loadChildren: () =>
      import('./profile/profile.module').then((m) => m.ProfileModule),
  },
  {
    path: 'dashboard',
    loadChildren: () =>
      import('./dashboard/dashboard.module').then((m) => m.DashboardModule),
  },
  {
    path: 'members',
    loadChildren: () =>
      import('./organizations/organizations.module').then(
        (m) => m.OrganizationsModule
      ),
  },
  {
    path: 'schools',
    loadChildren: () =>
      import('./businesses/businesses.module').then((m) => m.BusinessesModule),
  },
  {
    path: 'calendar',
    loadChildren: () =>
      import('./calendar/calendar.module').then((m) => m.CalendarsModule),
  },
  {
    path: 'projects',
    loadChildren: () =>
      import('./programs/programs.module').then((m) => m.ProgramsModule),
  },
  {
    path: 'events',
    loadChildren: () =>
      import('./events/events.module').then((m) => m.EventsModule),
  },
  {
    path: 'students',
    loadChildren: () =>
      import('./persons/persons.module').then((m) => m.PersonsModule),
  },
  {
    path: 'stakeholders',
    loadChildren: () =>
      import('./persons/persons.module').then((m) => m.PersonsModule),
  },
  {
    path: 'feedbacks',
    loadChildren: () =>
      import('./tasks/tasks.module').then((m) => m.TasksModule),
  },
  {
    path: 'families',
    loadChildren: () =>
      import('./person-to-person/person-to-person.module').then(
        (m) => m.PersonToPersonModule
      ),
  },
  {
    path: 'activity-submission',
    loadChildren: () =>
      import('./posts/posts.module').then((m) => m.PostsModule),
  },
  {
    path: 'logs',
    loadChildren: () =>
      import('./activities/activities.module').then((m) => m.ActivitiesModule),
  },
  {
    path: 'payment',
    loadChildren: () =>
      import('./payment/payment.module').then((m) => m.PaymentModule),
  },
  {
    path: 'crafted/pages/profile',
    loadChildren: () =>
      import('../modules/profile/profile.module').then((m) => m.ProfileModule),
  },
  {
    path: 'crafted/account',
    loadChildren: () =>
      import('../modules/account/account.module').then((m) => m.AccountModule),
  },
  {
    path: 'crafted/pages/wizards',
    loadChildren: () =>
      import('../modules/wizards/wizards.module').then((m) => m.WizardsModule),
  },
  {
    path: 'crafted/widgets',
    loadChildren: () =>
      import('../modules/widgets-examples/widgets-examples.module').then(
        (m) => m.WidgetsExamplesModule
      ),
  },
  {
    path: 'apps/chat',
    loadChildren: () =>
      import('../modules/apps/chat/chat.module').then((m) => m.ChatModule),
  },
  {
    path: 'notifications',
    loadChildren: () =>
      import('./communication/communication.module').then((m) => m.CommunicationModule),
  },
  {
    path: 'chat-village',
    loadChildren: () =>
      import('./chat-village/chat-village.module').then((m) => m.ChatVillageModule),
  },
  {
    path: 'funding-dashboard',
    loadChildren: () =>
      import('./customers/customers.module').then((m) => m.CustomersModule),
  },
  {
    path: 'transactions',
    loadChildren: () =>
      import('./transactions/transactions.module').then((m) => m.TransactionsModule),
  },
  {
    path: 'village-mathematics',
    loadChildren: () =>
      import('./snapshot/snapshot.module').then((m) => m.SnapshotModule),
  },
  {
    path: 'knowledge-overview',
    loadChildren: () =>
      import('./predictive/predictive.module').then((m) => m.PredictiveModule),
  },
  {
    path: 'project-overview',
    loadChildren: () =>
      import('./community-curriculum/community-curriculum.module').then((m) => m.CommunityCurriculumModule),
  },
  {
    path: 'assessment',
    loadChildren: () =>
      import('./testing/testing.module').then((m) => m.TestingModule),
  },
  {
    path: 'activities',
    loadChildren: () =>
      import('./homework/homework.module').then((m) => m.HomeworkModule),
  },
  {
    path: 'account-settings',
    loadChildren: () =>
      import('./account-settings/account-settings.module').then((m) => m.AccountSettingsModule),
  },
  {
    path: 'customers-sales',
    loadChildren: () =>
      import('./curriculum/curriculum.module').then((m) => m.CurriculumModule),
  },
  {
    path: 'innovation-center',
    loadChildren: () =>
      import('./innovation-center/innovation-center.module').then((m) => m.InnovationCenterModule),
  },
  {
    path: 'categories',
    loadChildren: () =>
      import('./categories/categories.module').then((m) => m.CategoriesModule),
  },
  {
    path: 'association-relation-type',
    loadChildren: () =>
      import('./association-relation-type/association-relation-type.module').then((m) => m.AssociationRelationTypeModule),
  },
  {
    path: 'villages',
    loadChildren: () =>
      import('./cities/cities.module').then((m) => m.CitiesModule),
  },
  {
    path: 'support-center',
    loadChildren: () =>
      import('./support-center/support-center.module').then((m) => m.SupportCenterModule),
  },
  {
    path: 'import-file',
    loadChildren: () =>
      import('./import-file/import-file.module').then((m) => m.ImportFileModule),
  },
  {
    path: 'send-notifications',
    component: SendNotificationsComponent
  },
  {
    path: 'chatbot-analytics',
    loadChildren: () =>
      import('./chatbot-analytics/chatbot-analytics.module').then(
        (m) => m.ChatbotAnalyticsModule
      ),
    canActivate: [AuthGuard],
  },
  {
    path: 'chatbot-prompts',
    loadChildren: () => import('./chatbot-prompts-list/chatbot-prompts-list.module')
      .then(m => m.ChatbotPromptsListModule),
    data: { preload: true }
  },
  {
    path: '.well-known/apple-app-site-association',
    redirectTo: '../../../.well-known/apple-app-site-association',
  },
  {
    path: '**',
    redirectTo: 'error/404',
  },
];

export { Routing };
