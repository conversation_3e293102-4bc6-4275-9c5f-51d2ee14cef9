import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

type IconType = 'duotone' | 'outline' | 'solid';

type IconConfig = {
  paths: number;
  iconType: IconType;
};

// Map of known icons and their configurations
const ICON_CONFIGS: Record<string, IconConfig> = {
  'ki-setting-3': { paths: 2, iconType: 'duotone' },
  'ki-message-text-2': { paths: 3, iconType: 'duotone' },
  'ki-arrows-rotate': { paths: 2, iconType: 'duotone' },
  'ki-messages': { paths: 2, iconType: 'duotone' },
  // Add more icons as needed
};

@Component({
  selector: 'app-icon',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './app-icon.component.html',
  styleUrls: ['./app-icon.component.scss']
})
export class AppIconComponent implements OnInit {
  @Input() icon: string = '';
  @Input() iconType?: IconType;
  @Input() size: number = 24;
  @Input() color: string = '';
  @Input() tooltip: string = '';
  @Input() additionalClasses: string = '';

  private _iconConfig: IconConfig = { paths: 2, iconType: 'duotone' };

  ngOnInit() {
    // Get the icon configuration or use defaults
    this._iconConfig = ICON_CONFIGS[this.icon] || { paths: 2, iconType: this.iconType || 'duotone' };
  }

  getIconClasses(): string {
    const classes = [
      'ki',
      `ki-${this._iconConfig.iconType}`,  // ki-duotone, ki-outline, or ki-solid
      `ki-${this.icon}`,                 // The specific icon class
      'fs-2'                             // Default size
    ];

    if (this.additionalClasses) {
      classes.push(this.additionalClasses);
    }

    return classes.join(' ').trim();
  }

  getPaths(): number[] {
    return Array.from({ length: this._iconConfig.paths }, (_, i) => i + 1);
  }
}
