<div class="user-search-dropdown">
  <ng-select
    [items]="users"
    bindLabel="name"
    bindValue="id"
    [loading]="loading"
    [searchable]="true"
    [clearable]="userControl.value ? true : false"
    [multiple]="false"
    [formControl]="userControl"
    (search)="onSearch($event.term)"
    [placeholder]="'Select a user...'"
    [notFoundText]="'No users found'"
    [typeahead]="null"
    class="form-control form-control-solid"
  >
    <ng-template ng-option-tmp let-item="item" let-search="searchTerm">
      <div class="d-flex align-items-center">
        <div class="symbol symbol-35px symbol-circle me-3">
          <div class="symbol-label bg-light-primary">
            <span class="fs-5 fw-bold text-primary">{{ (item.name?.[0] || '?') | uppercase }}</span>
          </div>
        </div>
        <div class="user-info">
          <div class="fw-bold">{{ item.name || 'Unknown User' }}</div>
          <div class="text-muted small">{{ item.email || '' }}</div>
        </div>
      </div>
    </ng-template>
  </ng-select>
</div>
