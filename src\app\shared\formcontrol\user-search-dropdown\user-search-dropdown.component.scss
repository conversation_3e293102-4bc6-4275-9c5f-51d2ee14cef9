// User Search Dropdown Styles
.user-search-dropdown {
  width: 100%;
  min-width: 250px; // Set a minimum width for the container
  position: relative;
  
  // Ensure ng-select takes full width
  ng-select {
    width: 100%;
    min-width: 100%;
    
    // Input container
    .ng-select-container {
      min-width: 100%;
      width: 100%;
    }
    
    // Search input
    .ng-input > input {
      width: 100% !important;
      min-width: 100% !important;
    }
  }

  // Fix for ng-select dropdown width
  .ng-dropdown-panel {
    min-width: 100% !important;
    width: 100% !important;
    max-width: 100%;
    margin-top: 0.5rem;
    border: 1px solid var(--kt-border-color);
    border-radius: 0.475rem;
    box-shadow: var(--kt-dropdown-box-shadow);
    background-color: var(--kt-dropdown-bg);
    position: relative;
    z-index: 1050; // Ensure dropdown appears above other elements
    
    // Prevent dropdown from being narrower than the control
    &.ng-select-bottom, &.ng-select-top {
      min-width: 100% !important;
      width: auto !important;
    }

    // Search input
    .ng-dropdown-header {
      padding: 0.5rem;
      border-bottom: 1px solid var(--kt-border-color);
      min-width: 100%;
      box-sizing: border-box;

      input {
        width: 100% !important;
        min-width: 100% !important;
        padding: 0.5rem 1rem;
        border: 1px solid var(--kt-input-border-color);
        border-radius: 0.475rem;
        background-color: var(--kt-input-bg);
        color: var(--kt-input-color);
        box-sizing: border-box;

        &:focus {
          border-color: var(--kt-primary);
          box-shadow: 0 0 0 3px rgba(var(--kt-primary-rgb), 0.1);
          outline: none;
        }
      }
    }

    // Dropdown items
    .ng-dropdown-panel-items {
      max-height: 300px;
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: var(--kt-gray-300) var(--kt-gray-100);

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--kt-gray-300);
        border-radius: 0.475rem;
      }

      &::-webkit-scrollbar-track {
        background-color: var(--kt-gray-100);
      }

      .ng-option {
        padding: 0.75rem 1.25rem;
        border-bottom: 1px solid var(--kt-border-color);
        transition: background-color 0.2s ease;

        &:last-child {
          border-bottom: none;
        }

        &.ng-option-marked,
        &.ng-option-selected {
          background-color: var(--kt-primary-light);
          color: var(--kt-primary);

          .text-muted {
            color: var(--kt-primary-light) !important;
          }
        }

        &:hover {
          background-color: var(--kt-gray-100);
        }
      }
    }
  }


  // Selected value
  .ng-value {
    .user-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .symbol {
        flex-shrink: 0;
      }
    }
  }

  // Loading state
  .ng-spinner-loader {
    border-color: var(--kt-primary) transparent var(--kt-primary) transparent;
  }
}

// Dark mode support
[data-kt-theme="dark"] {
  .user-search-dropdown {
    .ng-dropdown-panel {
      background-color: var(--kt-gray-100);
      border-color: var(--kt-gray-200);

      .ng-dropdown-header {
        border-bottom-color: var(--kt-gray-200);
      }

      .ng-option {
        border-bottom-color: var(--kt-gray-200);

        &:hover {
          background-color: var(--kt-gray-200);
        }
      }
    }
  }
}
