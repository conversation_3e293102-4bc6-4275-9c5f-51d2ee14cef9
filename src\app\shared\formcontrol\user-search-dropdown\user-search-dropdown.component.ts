import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';

interface User {
  id: string;
  name?: string;
  email?: string;
}

@Component({
  selector: 'app-user-search-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule
  ],
  templateUrl: './user-search-dropdown.component.html',
  styleUrls: ['./user-search-dropdown.component.scss']
})
export class UserSearchDropdownComponent implements OnInit, OnChanges {
  @Input() users: User[] = [];
  @Input() selectedUserId: string | null = null;
  @Output() userChange = new EventEmitter<string>();
  
  userControl = new FormControl<string | null>(null);
  loading = false;
  
  ngOnInit() {
    // Set initial value if provided
    if (this.selectedUserId) {
      this.userControl.setValue(this.selectedUserId, { emitEvent: false });
    }
    
    // Handle value changes
    this.userControl.valueChanges.subscribe(value => {
      if (value !== this.selectedUserId) {
        this.userChange.emit(value);
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['selectedUserId'] && this.selectedUserId !== this.userControl.value) {
      this.userControl.setValue(this.selectedUserId, { emitEvent: false });
    }
    
    // Reset firstChange flag if needed when users input changes
    if (changes['users'] && this.selectedUserId) {
      this.userControl.setValue(this.selectedUserId, { emitEvent: false });
    }
  }

  // Handle search event
  onSearch(term: string) {
    // You can implement server-side search here if needed
    // For now, it will use client-side filtering from the input array
  }

  /**
   * Get the name of the currently selected user
   */
  getSelectedUserName(): string {
    if (!this.userControl.value) return '';
    
    const selectedUser = this.users.find(user => user.id === this.userControl.value);
    return selectedUser?.name || '';
  }
}
