import { gql } from 'apollo-angular';

// Knowledge Repository Queries & Mutations
export const CREATE_KNOWLEDGE_REPOSITORY = gql`
  mutation CreateKnowledgeRepositoryStore($input: CreateKnowledgeRepositoryStoreInput!) {
    createKnowledgeRepositoryStore(input: $input) {
      id
      name
      description
      fileUrl
      fileType
      durationInMinutes
      entityType
      cityId
      status
      userId
      organizationId
      categoryId
      subCategoryIds
      submittedBy
      isDeleted
      isPublic
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_KNOWLEDGE_REPOSITORY = gql`
  mutation UpdateKnowledgeRepositoryStore($input: UpdateKnowledgeRepositoryStoreInput!) {
    updateKnowledgeRepositoryStore(input: $input) {
      id
      name
      description
      fileUrl
      fileType
      durationInMinutes
      entityType
      cityId
      status
      userId
      organizationId
      categoryId
      subCategoryIds
      submittedBy
      isDeleted
      isPublic
      createdAt
      updatedAt
    }
  }
`;

export const GET_KNOWLEDGE_REPOSITORY_BY_ID = gql`
  query GetKnowledgeRepositoryStore($id: ID!) {
    getKnowledgeRepositoryStore(id: $id) {
      id
      name
      description
      fileUrl
      fileType
      durationInMinutes
      entityType
      cityId
      status
      userId
      organizationId
      categoryId
      subCategoryIds
      submittedBy
      isDeleted
      isPublic
      createdAt
      updatedAt
    }
  }
`;

export const LIST_KNOWLEDGE_REPOSITORY = gql`
  query ListKnowledgeRepositoryStores($filter: ModelKnowledgeRepositoryStoreFilterInput, $limit: Int, $nextToken: String) {
    listKnowledgeRepositoryStores(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        description
        fileUrl
        fileType
        durationInMinutes
        entityType
        cityId
        status
        userId
        organizationId
        categoryId
        subCategoryIds
        submittedBy
        isDeleted
        isPublic
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;
