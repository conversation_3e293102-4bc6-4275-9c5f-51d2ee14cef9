import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Auth } from '@aws-amplify/auth';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {

  constructor(
    private readonly router: Router,
    private readonly toastr: ToastrService
  ) { }

  async canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean | UrlTree> {
    // Allow access if route is marked as public
    if (route.data && route.data['public'] === true) {
      return true;
    }

    try {
      await Auth.currentAuthenticatedUser();
      return true;

    } catch (error) {
      console.error('Auth Guard Error:', error);
      if (error !== 'The user is not authenticated') {
        this.toastr.error('Your session may have expired or an authentication error occurred.');
      }
      return this.router.parseUrl('/auth/signin');
    }
  }
}