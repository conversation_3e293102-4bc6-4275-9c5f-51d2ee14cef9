import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  CREATE_KNOWLEDGE_REPOSITORY,
  UPDATE_KNOWLEDGE_REPOSITORY,
  GET_KNOWLEDGE_REPOSITORY_BY_ID,
  LIST_KNOWLEDGE_REPOSITORY
} from '../graphql/knowledge-repository-graphql.queries';

@Injectable({
  providedIn: 'root'
})
export class KnowledgeRepositoryService {
  constructor(private readonly apollo: Apollo) {}

  createKnowledgeRepository(input: any): Observable<any> {
    return this.apollo.mutate({
      mutation: CREATE_KNOWLEDGE_REPOSITORY,
      variables: { input }
    }).pipe(
      map((result: any) => result.data.createKnowledgeRepositoryStore),
      catchError(error => {
        console.error('Error creating knowledge repository:', error);
        return throwError(() => new Error('Failed to create knowledge repository'));
      })
    );
  }

  updateKnowledgeRepository(input: any): Observable<any> {
    return this.apollo.mutate({
      mutation: UPDATE_KNOWLEDGE_REPOSITORY,
      variables: { input }
    }).pipe(
      map((result: any) => result.data.updateKnowledgeRepositoryStore),
      catchError(error => {
        console.error('Error updating knowledge repository:', error);
        return throwError(() => new Error('Failed to update knowledge repository'));
      })
    );
  }

  getKnowledgeRepositoryById(id: string): Observable<any> {
    return this.apollo.query({
      query: GET_KNOWLEDGE_REPOSITORY_BY_ID,
      variables: { id },
      fetchPolicy: 'network-only'
    }).pipe(
      map((result: any) => result.data.getKnowledgeRepositoryStore),
      catchError(error => {
        console.error('Error fetching knowledge repository by id:', error);
        return throwError(() => new Error('Failed to fetch knowledge repository by id'));
      })
    );
  }

  listKnowledgeRepositories(filter: any = {}, limit: number = 20, nextToken?: string): Observable<any> {
    return this.apollo.query({
      query: LIST_KNOWLEDGE_REPOSITORY,
      variables: { filter, limit, nextToken },
      fetchPolicy: 'network-only'
    }).pipe(
      map((result: any) => result.data.listKnowledgeRepositoryStores),
      catchError(error => {
        console.error('Error listing knowledge repositories:', error);
        return throwError(() => new Error('Failed to list knowledge repositories'));
      })
    );
  }
}
